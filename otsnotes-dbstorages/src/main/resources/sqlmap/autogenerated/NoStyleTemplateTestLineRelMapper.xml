<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.otsnotes.dbstorages.mybatis.mapper.NoStyleTemplateTestLineRelMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.otsnotes.dbstorages.mybatis.model.NoStyleTemplateTestLineRelPO" >
    <id column="id" property="id" jdbcType="VARCHAR" />
    <result column="nostyle_template_id" property="nostyleTemplateId" jdbcType="VARCHAR" />
    <result column="testline_id" property="testlineId" jdbcType="VARCHAR" />
    <result column="testline_name" property="testlineName" jdbcType="VARCHAR" />
    <result column="customer_group_code" property="customerGroupCode" jdbcType="VARCHAR" />
    <result column="customer_group_name" property="customerGroupName" jdbcType="VARCHAR" />
    <result column="citation_name" property="citationName" jdbcType="VARCHAR" />
    <result column="citation_id" property="citationId" jdbcType="INTEGER" />
    <result column="TemplateStyle" property="templatestyle" jdbcType="INTEGER" />
    <result column="createdDate" property="createddate" jdbcType="TIMESTAMP" />
    <result column="createdBy" property="createdby" jdbcType="VARCHAR" />
    <result column="activeIndicator" property="activeindicator" jdbcType="BIT" />
    <result column="modifiedDate" property="modifieddate" jdbcType="TIMESTAMP" />
    <result column="modifiedBy" property="modifiedby" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, nostyle_template_id, testline_id, testline_name, customer_group_code, customer_group_name, 
    citation_name, citation_id, TemplateStyle, createdDate, createdBy, activeIndicator, 
    modifiedDate, modifiedBy
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.NoStyleTemplateTestLineRelExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_nostyletemplate_testline_mapping
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from tb_nostyletemplate_testline_mapping
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from tb_nostyletemplate_testline_mapping
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.NoStyleTemplateTestLineRelExample" >
    delete from tb_nostyletemplate_testline_mapping
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.NoStyleTemplateTestLineRelPO" >
    insert into tb_nostyletemplate_testline_mapping (id, nostyle_template_id, testline_id, 
      testline_name, customer_group_code, customer_group_name, 
      citation_name, citation_id, TemplateStyle, 
      createdDate, createdBy, activeIndicator, 
      modifiedDate, modifiedBy)
    values (#{id,jdbcType=VARCHAR}, #{nostyleTemplateId,jdbcType=VARCHAR}, #{testlineId,jdbcType=VARCHAR}, 
      #{testlineName,jdbcType=VARCHAR}, #{customerGroupCode,jdbcType=VARCHAR}, #{customerGroupName,jdbcType=VARCHAR}, 
      #{citationName,jdbcType=VARCHAR}, #{citationId,jdbcType=INTEGER}, #{templatestyle,jdbcType=INTEGER}, 
      #{createddate,jdbcType=TIMESTAMP}, #{createdby,jdbcType=VARCHAR}, #{activeindicator,jdbcType=BIT}, 
      #{modifieddate,jdbcType=TIMESTAMP}, #{modifiedby,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.NoStyleTemplateTestLineRelPO" >
    insert into tb_nostyletemplate_testline_mapping
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="nostyleTemplateId != null" >
        nostyle_template_id,
      </if>
      <if test="testlineId != null" >
        testline_id,
      </if>
      <if test="testlineName != null" >
        testline_name,
      </if>
      <if test="customerGroupCode != null" >
        customer_group_code,
      </if>
      <if test="customerGroupName != null" >
        customer_group_name,
      </if>
      <if test="citationName != null" >
        citation_name,
      </if>
      <if test="citationId != null" >
        citation_id,
      </if>
      <if test="templatestyle != null" >
        TemplateStyle,
      </if>
      <if test="createddate != null" >
        createdDate,
      </if>
      <if test="createdby != null" >
        createdBy,
      </if>
      <if test="activeindicator != null" >
        activeIndicator,
      </if>
      <if test="modifieddate != null" >
        modifiedDate,
      </if>
      <if test="modifiedby != null" >
        modifiedBy,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="nostyleTemplateId != null" >
        #{nostyleTemplateId,jdbcType=VARCHAR},
      </if>
      <if test="testlineId != null" >
        #{testlineId,jdbcType=VARCHAR},
      </if>
      <if test="testlineName != null" >
        #{testlineName,jdbcType=VARCHAR},
      </if>
      <if test="customerGroupCode != null" >
        #{customerGroupCode,jdbcType=VARCHAR},
      </if>
      <if test="customerGroupName != null" >
        #{customerGroupName,jdbcType=VARCHAR},
      </if>
      <if test="citationName != null" >
        #{citationName,jdbcType=VARCHAR},
      </if>
      <if test="citationId != null" >
        #{citationId,jdbcType=INTEGER},
      </if>
      <if test="templatestyle != null" >
        #{templatestyle,jdbcType=INTEGER},
      </if>
      <if test="createddate != null" >
        #{createddate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdby != null" >
        #{createdby,jdbcType=VARCHAR},
      </if>
      <if test="activeindicator != null" >
        #{activeindicator,jdbcType=BIT},
      </if>
      <if test="modifieddate != null" >
        #{modifieddate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedby != null" >
        #{modifiedby,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.NoStyleTemplateTestLineRelExample" resultType="java.lang.Integer" >
    select count(*) from tb_nostyletemplate_testline_mapping
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_nostyletemplate_testline_mapping
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.nostyleTemplateId != null" >
        nostyle_template_id = #{record.nostyleTemplateId,jdbcType=VARCHAR},
      </if>
      <if test="record.testlineId != null" >
        testline_id = #{record.testlineId,jdbcType=VARCHAR},
      </if>
      <if test="record.testlineName != null" >
        testline_name = #{record.testlineName,jdbcType=VARCHAR},
      </if>
      <if test="record.customerGroupCode != null" >
        customer_group_code = #{record.customerGroupCode,jdbcType=VARCHAR},
      </if>
      <if test="record.customerGroupName != null" >
        customer_group_name = #{record.customerGroupName,jdbcType=VARCHAR},
      </if>
      <if test="record.citationName != null" >
        citation_name = #{record.citationName,jdbcType=VARCHAR},
      </if>
      <if test="record.citationId != null" >
        citation_id = #{record.citationId,jdbcType=INTEGER},
      </if>
      <if test="record.templatestyle != null" >
        TemplateStyle = #{record.templatestyle,jdbcType=INTEGER},
      </if>
      <if test="record.createddate != null" >
        createdDate = #{record.createddate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdby != null" >
        createdBy = #{record.createdby,jdbcType=VARCHAR},
      </if>
      <if test="record.activeindicator != null" >
        activeIndicator = #{record.activeindicator,jdbcType=BIT},
      </if>
      <if test="record.modifieddate != null" >
        modifiedDate = #{record.modifieddate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedby != null" >
        modifiedBy = #{record.modifiedby,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_nostyletemplate_testline_mapping
    set id = #{record.id,jdbcType=VARCHAR},
      nostyle_template_id = #{record.nostyleTemplateId,jdbcType=VARCHAR},
      testline_id = #{record.testlineId,jdbcType=VARCHAR},
      testline_name = #{record.testlineName,jdbcType=VARCHAR},
      customer_group_code = #{record.customerGroupCode,jdbcType=VARCHAR},
      customer_group_name = #{record.customerGroupName,jdbcType=VARCHAR},
      citation_name = #{record.citationName,jdbcType=VARCHAR},
      citation_id = #{record.citationId,jdbcType=INTEGER},
      TemplateStyle = #{record.templatestyle,jdbcType=INTEGER},
      createdDate = #{record.createddate,jdbcType=TIMESTAMP},
      createdBy = #{record.createdby,jdbcType=VARCHAR},
      activeIndicator = #{record.activeindicator,jdbcType=BIT},
      modifiedDate = #{record.modifieddate,jdbcType=TIMESTAMP},
      modifiedBy = #{record.modifiedby,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.NoStyleTemplateTestLineRelPO" >
    update tb_nostyletemplate_testline_mapping
    <set >
      <if test="nostyleTemplateId != null" >
        nostyle_template_id = #{nostyleTemplateId,jdbcType=VARCHAR},
      </if>
      <if test="testlineId != null" >
        testline_id = #{testlineId,jdbcType=VARCHAR},
      </if>
      <if test="testlineName != null" >
        testline_name = #{testlineName,jdbcType=VARCHAR},
      </if>
      <if test="customerGroupCode != null" >
        customer_group_code = #{customerGroupCode,jdbcType=VARCHAR},
      </if>
      <if test="customerGroupName != null" >
        customer_group_name = #{customerGroupName,jdbcType=VARCHAR},
      </if>
      <if test="citationName != null" >
        citation_name = #{citationName,jdbcType=VARCHAR},
      </if>
      <if test="citationId != null" >
        citation_id = #{citationId,jdbcType=INTEGER},
      </if>
      <if test="templatestyle != null" >
        TemplateStyle = #{templatestyle,jdbcType=INTEGER},
      </if>
      <if test="createddate != null" >
        createdDate = #{createddate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdby != null" >
        createdBy = #{createdby,jdbcType=VARCHAR},
      </if>
      <if test="activeindicator != null" >
        activeIndicator = #{activeindicator,jdbcType=BIT},
      </if>
      <if test="modifieddate != null" >
        modifiedDate = #{modifieddate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedby != null" >
        modifiedBy = #{modifiedby,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.NoStyleTemplateTestLineRelPO" >
    update tb_nostyletemplate_testline_mapping
    set nostyle_template_id = #{nostyleTemplateId,jdbcType=VARCHAR},
      testline_id = #{testlineId,jdbcType=VARCHAR},
      testline_name = #{testlineName,jdbcType=VARCHAR},
      customer_group_code = #{customerGroupCode,jdbcType=VARCHAR},
      customer_group_name = #{customerGroupName,jdbcType=VARCHAR},
      citation_name = #{citationName,jdbcType=VARCHAR},
      citation_id = #{citationId,jdbcType=INTEGER},
      TemplateStyle = #{templatestyle,jdbcType=INTEGER},
      createdDate = #{createddate,jdbcType=TIMESTAMP},
      createdBy = #{createdby,jdbcType=VARCHAR},
      activeIndicator = #{activeindicator,jdbcType=BIT},
      modifiedDate = #{modifieddate,jdbcType=TIMESTAMP},
      modifiedBy = #{modifiedby,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>