<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.otsnotes.dbstorages.mybatis.mapper.TestLineInstanceMultipleLanguageInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineInstanceMultipleLanguageInfoPO" >
    <id column="ID" property="ID" jdbcType="VARCHAR" />
    <result column="TestLineInstanceID" property="testLineInstanceID" jdbcType="VARCHAR" />
    <result column="LanguageId" property="languageId" jdbcType="INTEGER" />
    <result column="EvaluationAlias" property="evaluationAlias" jdbcType="VARCHAR" />
    <result column="StandardName" property="standardName" jdbcType="VARCHAR" />
    <result column="TestLineEvaluation" property="testLineEvaluation" jdbcType="VARCHAR" />
    <result column="CreatedDate" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="CreatedBy" property="createdBy" jdbcType="VARCHAR" />
    <result column="ModifiedDate" property="modifiedDate" jdbcType="TIMESTAMP" />
    <result column="ModifiedBy" property="modifiedBy" jdbcType="VARCHAR" />
    <result column="StandardSectionName" property="standardSectionName" jdbcType="VARCHAR" />
    <result column="CitationName" property="citationName" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    ID, TestLineInstanceID, LanguageId, EvaluationAlias, StandardName, TestLineEvaluation, 
    CreatedDate, CreatedBy, ModifiedDate, ModifiedBy, StandardSectionName, CitationName
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineInstanceMultipleLanguageInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_test_line_instance_multiplelanguage
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from tb_test_line_instance_multiplelanguage
    where ID = #{ID,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from tb_test_line_instance_multiplelanguage
    where ID = #{ID,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineInstanceMultipleLanguageInfoExample" >
    delete from tb_test_line_instance_multiplelanguage
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineInstanceMultipleLanguageInfoPO" >
    insert into tb_test_line_instance_multiplelanguage (ID, TestLineInstanceID, LanguageId, 
      EvaluationAlias, StandardName, TestLineEvaluation, 
      CreatedDate, CreatedBy, ModifiedDate, 
      ModifiedBy, StandardSectionName, CitationName
      )
    values (#{ID,jdbcType=VARCHAR}, #{testLineInstanceID,jdbcType=VARCHAR}, #{languageId,jdbcType=INTEGER}, 
      #{evaluationAlias,jdbcType=VARCHAR}, #{standardName,jdbcType=VARCHAR}, #{testLineEvaluation,jdbcType=VARCHAR}, 
      #{createdDate,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=VARCHAR}, #{modifiedDate,jdbcType=TIMESTAMP}, 
      #{modifiedBy,jdbcType=VARCHAR}, #{standardSectionName,jdbcType=VARCHAR}, #{citationName,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineInstanceMultipleLanguageInfoPO" >
    insert into tb_test_line_instance_multiplelanguage
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="ID != null" >
        ID,
      </if>
      <if test="testLineInstanceID != null" >
        TestLineInstanceID,
      </if>
      <if test="languageId != null" >
        LanguageId,
      </if>
      <if test="evaluationAlias != null" >
        EvaluationAlias,
      </if>
      <if test="standardName != null" >
        StandardName,
      </if>
      <if test="testLineEvaluation != null" >
        TestLineEvaluation,
      </if>
      <if test="createdDate != null" >
        CreatedDate,
      </if>
      <if test="createdBy != null" >
        CreatedBy,
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate,
      </if>
      <if test="modifiedBy != null" >
        ModifiedBy,
      </if>
      <if test="standardSectionName != null" >
        StandardSectionName,
      </if>
      <if test="citationName != null" >
        CitationName,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="ID != null" >
        #{ID,jdbcType=VARCHAR},
      </if>
      <if test="testLineInstanceID != null" >
        #{testLineInstanceID,jdbcType=VARCHAR},
      </if>
      <if test="languageId != null" >
        #{languageId,jdbcType=INTEGER},
      </if>
      <if test="evaluationAlias != null" >
        #{evaluationAlias,jdbcType=VARCHAR},
      </if>
      <if test="standardName != null" >
        #{standardName,jdbcType=VARCHAR},
      </if>
      <if test="testLineEvaluation != null" >
        #{testLineEvaluation,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="standardSectionName != null" >
        #{standardSectionName,jdbcType=VARCHAR},
      </if>
      <if test="citationName != null" >
        #{citationName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineInstanceMultipleLanguageInfoExample" resultType="java.lang.Integer" >
    select count(*) from tb_test_line_instance_multiplelanguage
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_test_line_instance_multiplelanguage
    <set >
      <if test="record.ID != null" >
        ID = #{record.ID,jdbcType=VARCHAR},
      </if>
      <if test="record.testLineInstanceID != null" >
        TestLineInstanceID = #{record.testLineInstanceID,jdbcType=VARCHAR},
      </if>
      <if test="record.languageId != null" >
        LanguageId = #{record.languageId,jdbcType=INTEGER},
      </if>
      <if test="record.evaluationAlias != null" >
        EvaluationAlias = #{record.evaluationAlias,jdbcType=VARCHAR},
      </if>
      <if test="record.standardName != null" >
        StandardName = #{record.standardName,jdbcType=VARCHAR},
      </if>
      <if test="record.testLineEvaluation != null" >
        TestLineEvaluation = #{record.testLineEvaluation,jdbcType=VARCHAR},
      </if>
      <if test="record.createdDate != null" >
        CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdBy != null" >
        CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.modifiedDate != null" >
        ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedBy != null" >
        ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.standardSectionName != null" >
        StandardSectionName = #{record.standardSectionName,jdbcType=VARCHAR},
      </if>
      <if test="record.citationName != null" >
        CitationName = #{record.citationName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_test_line_instance_multiplelanguage
    set ID = #{record.ID,jdbcType=VARCHAR},
      TestLineInstanceID = #{record.testLineInstanceID,jdbcType=VARCHAR},
      LanguageId = #{record.languageId,jdbcType=INTEGER},
      EvaluationAlias = #{record.evaluationAlias,jdbcType=VARCHAR},
      StandardName = #{record.standardName,jdbcType=VARCHAR},
      TestLineEvaluation = #{record.testLineEvaluation,jdbcType=VARCHAR},
      CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      StandardSectionName = #{record.standardSectionName,jdbcType=VARCHAR},
      CitationName = #{record.citationName,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineInstanceMultipleLanguageInfoPO" >
    update tb_test_line_instance_multiplelanguage
    <set >
      <if test="testLineInstanceID != null" >
        TestLineInstanceID = #{testLineInstanceID,jdbcType=VARCHAR},
      </if>
      <if test="languageId != null" >
        LanguageId = #{languageId,jdbcType=INTEGER},
      </if>
      <if test="evaluationAlias != null" >
        EvaluationAlias = #{evaluationAlias,jdbcType=VARCHAR},
      </if>
      <if test="standardName != null" >
        StandardName = #{standardName,jdbcType=VARCHAR},
      </if>
      <if test="testLineEvaluation != null" >
        TestLineEvaluation = #{testLineEvaluation,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        CreatedBy = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        ModifiedBy = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="standardSectionName != null" >
        StandardSectionName = #{standardSectionName,jdbcType=VARCHAR},
      </if>
      <if test="citationName != null" >
        CitationName = #{citationName,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{ID,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineInstanceMultipleLanguageInfoPO" >
    update tb_test_line_instance_multiplelanguage
    set TestLineInstanceID = #{testLineInstanceID,jdbcType=VARCHAR},
      LanguageId = #{languageId,jdbcType=INTEGER},
      EvaluationAlias = #{evaluationAlias,jdbcType=VARCHAR},
      StandardName = #{standardName,jdbcType=VARCHAR},
      TestLineEvaluation = #{testLineEvaluation,jdbcType=VARCHAR},
      CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      CreatedBy = #{createdBy,jdbcType=VARCHAR},
      ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{modifiedBy,jdbcType=VARCHAR},
      StandardSectionName = #{standardSectionName,jdbcType=VARCHAR},
      CitationName = #{citationName,jdbcType=VARCHAR}
    where ID = #{ID,jdbcType=VARCHAR}
  </update>
</mapper>