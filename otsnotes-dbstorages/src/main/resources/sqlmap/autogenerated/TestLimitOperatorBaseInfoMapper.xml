<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.otsnotes.dbstorages.mybatis.mapper.TestLimitOperatorBaseInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.otsnotes.dbstorages.mybatis.model.TestLimitOperatorBaseInfoPO" >
    <id column="Id" property="id" jdbcType="BIGINT" />
    <result column="OperatorId" property="operatorId" jdbcType="INTEGER" />
    <result column="OperatorName" property="operatorName" jdbcType="VARCHAR" />
    <result column="OperatorDepiction" property="operatorDepiction" jdbcType="VARCHAR" />
    <result column="OperandCount" property="operandCount" jdbcType="INTEGER" />
    <result column="BizVersionId" property="bizVersionId" jdbcType="CHAR" />
    <result column="Status" property="status" jdbcType="INTEGER" />
    <result column="CreatedDate" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="ModifiedDate" property="modifiedDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    Id, OperatorId, OperatorName, OperatorDepiction, OperandCount, BizVersionId, `Status`, 
    CreatedDate, ModifiedDate
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLimitOperatorBaseInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_trims_test_limit_operator_baseinfo
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from tb_trims_test_limit_operator_baseinfo
    where Id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from tb_trims_test_limit_operator_baseinfo
    where Id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLimitOperatorBaseInfoExample" >
    delete from tb_trims_test_limit_operator_baseinfo
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLimitOperatorBaseInfoPO" >
    insert into tb_trims_test_limit_operator_baseinfo (Id, OperatorId, OperatorName,
      OperatorDepiction, OperandCount, BizVersionId, 
      `Status`, CreatedDate, ModifiedDate
      )
    values (#{id,jdbcType=BIGINT}, #{operatorId,jdbcType=INTEGER}, #{operatorName,jdbcType=VARCHAR}, 
      #{operatorDepiction,jdbcType=VARCHAR}, #{operandCount,jdbcType=INTEGER}, #{bizVersionId,jdbcType=CHAR}, 
      #{status,jdbcType=INTEGER}, #{createdDate,jdbcType=TIMESTAMP}, #{modifiedDate,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLimitOperatorBaseInfoPO" >
    insert into tb_trims_test_limit_operator_baseinfo
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        Id,
      </if>
      <if test="operatorId != null" >
        OperatorId,
      </if>
      <if test="operatorName != null" >
        OperatorName,
      </if>
      <if test="operatorDepiction != null" >
        OperatorDepiction,
      </if>
      <if test="operandCount != null" >
        OperandCount,
      </if>
      <if test="bizVersionId != null" >
        BizVersionId,
      </if>
      <if test="status != null" >
        `Status`,
      </if>
      <if test="createdDate != null" >
        CreatedDate,
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="operatorId != null" >
        #{operatorId,jdbcType=INTEGER},
      </if>
      <if test="operatorName != null" >
        #{operatorName,jdbcType=VARCHAR},
      </if>
      <if test="operatorDepiction != null" >
        #{operatorDepiction,jdbcType=VARCHAR},
      </if>
      <if test="operandCount != null" >
        #{operandCount,jdbcType=INTEGER},
      </if>
      <if test="bizVersionId != null" >
        #{bizVersionId,jdbcType=CHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLimitOperatorBaseInfoExample" resultType="java.lang.Integer" >
    select count(*) from tb_trims_test_limit_operator_baseinfo
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_trims_test_limit_operator_baseinfo
    <set >
      <if test="record.id != null" >
        Id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.operatorId != null" >
        OperatorId = #{record.operatorId,jdbcType=INTEGER},
      </if>
      <if test="record.operatorName != null" >
        OperatorName = #{record.operatorName,jdbcType=VARCHAR},
      </if>
      <if test="record.operatorDepiction != null" >
        OperatorDepiction = #{record.operatorDepiction,jdbcType=VARCHAR},
      </if>
      <if test="record.operandCount != null" >
        OperandCount = #{record.operandCount,jdbcType=INTEGER},
      </if>
      <if test="record.bizVersionId != null" >
        BizVersionId = #{record.bizVersionId,jdbcType=CHAR},
      </if>
      <if test="record.status != null" >
        `Status` = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.createdDate != null" >
        CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedDate != null" >
        ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_trims_test_limit_operator_baseinfo
    set Id = #{record.id,jdbcType=BIGINT},
      OperatorId = #{record.operatorId,jdbcType=INTEGER},
      OperatorName = #{record.operatorName,jdbcType=VARCHAR},
      OperatorDepiction = #{record.operatorDepiction,jdbcType=VARCHAR},
      OperandCount = #{record.operandCount,jdbcType=INTEGER},
      BizVersionId = #{record.bizVersionId,jdbcType=CHAR},
      `Status` = #{record.status,jdbcType=INTEGER},
      CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLimitOperatorBaseInfoPO" >
    update tb_trims_test_limit_operator_baseinfo
    <set >
      <if test="operatorId != null" >
        OperatorId = #{operatorId,jdbcType=INTEGER},
      </if>
      <if test="operatorName != null" >
        OperatorName = #{operatorName,jdbcType=VARCHAR},
      </if>
      <if test="operatorDepiction != null" >
        OperatorDepiction = #{operatorDepiction,jdbcType=VARCHAR},
      </if>
      <if test="operandCount != null" >
        OperandCount = #{operandCount,jdbcType=INTEGER},
      </if>
      <if test="bizVersionId != null" >
        BizVersionId = #{bizVersionId,jdbcType=CHAR},
      </if>
      <if test="status != null" >
        `Status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="createdDate != null" >
        CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where Id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLimitOperatorBaseInfoPO" >
    update tb_trims_test_limit_operator_baseinfo
    set OperatorId = #{operatorId,jdbcType=INTEGER},
      OperatorName = #{operatorName,jdbcType=VARCHAR},
      OperatorDepiction = #{operatorDepiction,jdbcType=VARCHAR},
      OperandCount = #{operandCount,jdbcType=INTEGER},
      BizVersionId = #{bizVersionId,jdbcType=CHAR},
      `Status` = #{status,jdbcType=INTEGER},
      CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP}
    where Id = #{id,jdbcType=BIGINT}
  </update>
</mapper>