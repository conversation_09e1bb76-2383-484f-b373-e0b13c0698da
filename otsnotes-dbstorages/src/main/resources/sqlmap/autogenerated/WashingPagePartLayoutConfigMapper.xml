<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.otsnotes.dbstorages.mybatis.mapper.WashingPagePartLayoutConfigMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.otsnotes.dbstorages.mybatis.model.WashingPagePartLayoutConfigPO" >
    <id column="Id" property="id" jdbcType="BIGINT" />
    <result column="LayoutCode" property="layoutCode" jdbcType="VARCHAR" />
    <result column="PartCode" property="partCode" jdbcType="VARCHAR" />
    <result column="Seq" property="seq" jdbcType="INTEGER" />
    <result column="Status" property="status" jdbcType="INTEGER" />
    <result column="CreatedBy" property="createdBy" jdbcType="VARCHAR" />
    <result column="CreatedDate" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="Modifiedby" property="modifiedby" jdbcType="VARCHAR" />
    <result column="ModifiedDate" property="modifiedDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    Id, LayoutCode, PartCode, Seq, `Status`, CreatedBy, CreatedDate, Modifiedby, ModifiedDate
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.WashingPagePartLayoutConfigExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_washing_page_part_config
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from tb_washing_page_part_config
    where Id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from tb_washing_page_part_config
    where Id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.WashingPagePartLayoutConfigExample" >
    delete from tb_washing_page_part_config
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.WashingPagePartLayoutConfigPO" >
    insert into tb_washing_page_part_config (Id, LayoutCode, PartCode, 
      Seq, `Status`, CreatedBy, 
      CreatedDate, Modifiedby, ModifiedDate
      )
    values (#{id,jdbcType=BIGINT}, #{layoutCode,jdbcType=VARCHAR}, #{partCode,jdbcType=VARCHAR}, 
      #{seq,jdbcType=INTEGER}, #{status,jdbcType=INTEGER}, #{createdBy,jdbcType=VARCHAR}, 
      #{createdDate,jdbcType=TIMESTAMP}, #{modifiedby,jdbcType=VARCHAR}, #{modifiedDate,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.WashingPagePartLayoutConfigPO" >
    insert into tb_washing_page_part_config
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        Id,
      </if>
      <if test="layoutCode != null" >
        LayoutCode,
      </if>
      <if test="partCode != null" >
        PartCode,
      </if>
      <if test="seq != null" >
        Seq,
      </if>
      <if test="status != null" >
        `Status`,
      </if>
      <if test="createdBy != null" >
        CreatedBy,
      </if>
      <if test="createdDate != null" >
        CreatedDate,
      </if>
      <if test="modifiedby != null" >
        Modifiedby,
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="layoutCode != null" >
        #{layoutCode,jdbcType=VARCHAR},
      </if>
      <if test="partCode != null" >
        #{partCode,jdbcType=VARCHAR},
      </if>
      <if test="seq != null" >
        #{seq,jdbcType=INTEGER},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedby != null" >
        #{modifiedby,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.WashingPagePartLayoutConfigExample" resultType="java.lang.Integer" >
    select count(*) from tb_washing_page_part_config
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_washing_page_part_config
    <set >
      <if test="record.id != null" >
        Id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.layoutCode != null" >
        LayoutCode = #{record.layoutCode,jdbcType=VARCHAR},
      </if>
      <if test="record.partCode != null" >
        PartCode = #{record.partCode,jdbcType=VARCHAR},
      </if>
      <if test="record.seq != null" >
        Seq = #{record.seq,jdbcType=INTEGER},
      </if>
      <if test="record.status != null" >
        `Status` = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null" >
        CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdDate != null" >
        CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedby != null" >
        Modifiedby = #{record.modifiedby,jdbcType=VARCHAR},
      </if>
      <if test="record.modifiedDate != null" >
        ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_washing_page_part_config
    set Id = #{record.id,jdbcType=BIGINT},
      LayoutCode = #{record.layoutCode,jdbcType=VARCHAR},
      PartCode = #{record.partCode,jdbcType=VARCHAR},
      Seq = #{record.seq,jdbcType=INTEGER},
      `Status` = #{record.status,jdbcType=INTEGER},
      CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      Modifiedby = #{record.modifiedby,jdbcType=VARCHAR},
      ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.WashingPagePartLayoutConfigPO" >
    update tb_washing_page_part_config
    <set >
      <if test="layoutCode != null" >
        LayoutCode = #{layoutCode,jdbcType=VARCHAR},
      </if>
      <if test="partCode != null" >
        PartCode = #{partCode,jdbcType=VARCHAR},
      </if>
      <if test="seq != null" >
        Seq = #{seq,jdbcType=INTEGER},
      </if>
      <if test="status != null" >
        `Status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null" >
        CreatedBy = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedby != null" >
        Modifiedby = #{modifiedby,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where Id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.WashingPagePartLayoutConfigPO" >
    update tb_washing_page_part_config
    set LayoutCode = #{layoutCode,jdbcType=VARCHAR},
      PartCode = #{partCode,jdbcType=VARCHAR},
      Seq = #{seq,jdbcType=INTEGER},
      `Status` = #{status,jdbcType=INTEGER},
      CreatedBy = #{createdBy,jdbcType=VARCHAR},
      CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      Modifiedby = #{modifiedby,jdbcType=VARCHAR},
      ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP}
    where Id = #{id,jdbcType=BIGINT}
  </update>
</mapper>