<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.otsnotes.dbstorages.mybatis.mapper.ProductAttributeInstanceInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.otsnotes.dbstorages.mybatis.model.ProductAttributeInstanceInfoPO" >
    <id column="ID" property="ID" jdbcType="VARCHAR" />
    <result column="TestSampleID" property="testSampleID" jdbcType="VARCHAR" />
    <result column="TestMatrixID" property="testMatrixID" jdbcType="VARCHAR" />
    <result column="TestLineVersionID" property="testLineVersionID" jdbcType="INTEGER" />
    <result column="ProductAttributeID" property="productAttributeID" jdbcType="INTEGER" />
    <result column="ProductAttributeValue" property="productAttributeValue" jdbcType="VARCHAR" />
    <result column="OrderNo" property="orderNo" jdbcType="VARCHAR" />
    <result column="ActiveIndicator" property="activeIndicator" jdbcType="BIT" />
    <result column="CreatedDate" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="CreatedBy" property="createdBy" jdbcType="VARCHAR" />
    <result column="ModifiedDate" property="modifiedDate" jdbcType="TIMESTAMP" />
    <result column="ModifiedBy" property="modifiedBy" jdbcType="VARCHAR" />
    <result column="LimitGroupId" property="limitGroupId" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    ID, TestSampleID, TestMatrixID, TestLineVersionID, ProductAttributeID, ProductAttributeValue, 
    OrderNo, ActiveIndicator, CreatedDate, CreatedBy, ModifiedDate, ModifiedBy, LimitGroupId
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.ProductAttributeInstanceInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_product_attribute_instance
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from tb_product_attribute_instance
    where ID = #{ID,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from tb_product_attribute_instance
    where ID = #{ID,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.ProductAttributeInstanceInfoExample" >
    delete from tb_product_attribute_instance
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.ProductAttributeInstanceInfoPO" >
    insert into tb_product_attribute_instance (ID, TestSampleID, TestMatrixID, 
      TestLineVersionID, ProductAttributeID, 
      ProductAttributeValue, OrderNo, ActiveIndicator, 
      CreatedDate, CreatedBy, ModifiedDate, 
      ModifiedBy, LimitGroupId)
    values (#{ID,jdbcType=VARCHAR}, #{testSampleID,jdbcType=VARCHAR}, #{testMatrixID,jdbcType=VARCHAR}, 
      #{testLineVersionID,jdbcType=INTEGER}, #{productAttributeID,jdbcType=INTEGER}, 
      #{productAttributeValue,jdbcType=VARCHAR}, #{orderNo,jdbcType=VARCHAR}, #{activeIndicator,jdbcType=BIT}, 
      #{createdDate,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=VARCHAR}, #{modifiedDate,jdbcType=TIMESTAMP}, 
      #{modifiedBy,jdbcType=VARCHAR}, #{limitGroupId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.ProductAttributeInstanceInfoPO" >
    insert into tb_product_attribute_instance
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="ID != null" >
        ID,
      </if>
      <if test="testSampleID != null" >
        TestSampleID,
      </if>
      <if test="testMatrixID != null" >
        TestMatrixID,
      </if>
      <if test="testLineVersionID != null" >
        TestLineVersionID,
      </if>
      <if test="productAttributeID != null" >
        ProductAttributeID,
      </if>
      <if test="productAttributeValue != null" >
        ProductAttributeValue,
      </if>
      <if test="orderNo != null" >
        OrderNo,
      </if>
      <if test="activeIndicator != null" >
        ActiveIndicator,
      </if>
      <if test="createdDate != null" >
        CreatedDate,
      </if>
      <if test="createdBy != null" >
        CreatedBy,
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate,
      </if>
      <if test="modifiedBy != null" >
        ModifiedBy,
      </if>
      <if test="limitGroupId != null" >
        LimitGroupId,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="ID != null" >
        #{ID,jdbcType=VARCHAR},
      </if>
      <if test="testSampleID != null" >
        #{testSampleID,jdbcType=VARCHAR},
      </if>
      <if test="testMatrixID != null" >
        #{testMatrixID,jdbcType=VARCHAR},
      </if>
      <if test="testLineVersionID != null" >
        #{testLineVersionID,jdbcType=INTEGER},
      </if>
      <if test="productAttributeID != null" >
        #{productAttributeID,jdbcType=INTEGER},
      </if>
      <if test="productAttributeValue != null" >
        #{productAttributeValue,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null" >
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="activeIndicator != null" >
        #{activeIndicator,jdbcType=BIT},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="limitGroupId != null" >
        #{limitGroupId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.ProductAttributeInstanceInfoExample" resultType="java.lang.Integer" >
    select count(*) from tb_product_attribute_instance
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_product_attribute_instance
    <set >
      <if test="record.ID != null" >
        ID = #{record.ID,jdbcType=VARCHAR},
      </if>
      <if test="record.testSampleID != null" >
        TestSampleID = #{record.testSampleID,jdbcType=VARCHAR},
      </if>
      <if test="record.testMatrixID != null" >
        TestMatrixID = #{record.testMatrixID,jdbcType=VARCHAR},
      </if>
      <if test="record.testLineVersionID != null" >
        TestLineVersionID = #{record.testLineVersionID,jdbcType=INTEGER},
      </if>
      <if test="record.productAttributeID != null" >
        ProductAttributeID = #{record.productAttributeID,jdbcType=INTEGER},
      </if>
      <if test="record.productAttributeValue != null" >
        ProductAttributeValue = #{record.productAttributeValue,jdbcType=VARCHAR},
      </if>
      <if test="record.orderNo != null" >
        OrderNo = #{record.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.activeIndicator != null" >
        ActiveIndicator = #{record.activeIndicator,jdbcType=BIT},
      </if>
      <if test="record.createdDate != null" >
        CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdBy != null" >
        CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.modifiedDate != null" >
        ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedBy != null" >
        ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.limitGroupId != null" >
        LimitGroupId = #{record.limitGroupId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_product_attribute_instance
    set ID = #{record.ID,jdbcType=VARCHAR},
      TestSampleID = #{record.testSampleID,jdbcType=VARCHAR},
      TestMatrixID = #{record.testMatrixID,jdbcType=VARCHAR},
      TestLineVersionID = #{record.testLineVersionID,jdbcType=INTEGER},
      ProductAttributeID = #{record.productAttributeID,jdbcType=INTEGER},
      ProductAttributeValue = #{record.productAttributeValue,jdbcType=VARCHAR},
      OrderNo = #{record.orderNo,jdbcType=VARCHAR},
      ActiveIndicator = #{record.activeIndicator,jdbcType=BIT},
      CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      LimitGroupId = #{record.limitGroupId,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.ProductAttributeInstanceInfoPO" >
    update tb_product_attribute_instance
    <set >
      <if test="testSampleID != null" >
        TestSampleID = #{testSampleID,jdbcType=VARCHAR},
      </if>
      <if test="testMatrixID != null" >
        TestMatrixID = #{testMatrixID,jdbcType=VARCHAR},
      </if>
      <if test="testLineVersionID != null" >
        TestLineVersionID = #{testLineVersionID,jdbcType=INTEGER},
      </if>
      <if test="productAttributeID != null" >
        ProductAttributeID = #{productAttributeID,jdbcType=INTEGER},
      </if>
      <if test="productAttributeValue != null" >
        ProductAttributeValue = #{productAttributeValue,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null" >
        OrderNo = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="activeIndicator != null" >
        ActiveIndicator = #{activeIndicator,jdbcType=BIT},
      </if>
      <if test="createdDate != null" >
        CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        CreatedBy = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        ModifiedBy = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="limitGroupId != null" >
        LimitGroupId = #{limitGroupId,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{ID,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.ProductAttributeInstanceInfoPO" >
    update tb_product_attribute_instance
    set TestSampleID = #{testSampleID,jdbcType=VARCHAR},
      TestMatrixID = #{testMatrixID,jdbcType=VARCHAR},
      TestLineVersionID = #{testLineVersionID,jdbcType=INTEGER},
      ProductAttributeID = #{productAttributeID,jdbcType=INTEGER},
      ProductAttributeValue = #{productAttributeValue,jdbcType=VARCHAR},
      OrderNo = #{orderNo,jdbcType=VARCHAR},
      ActiveIndicator = #{activeIndicator,jdbcType=BIT},
      CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      CreatedBy = #{createdBy,jdbcType=VARCHAR},
      ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{modifiedBy,jdbcType=VARCHAR},
      LimitGroupId = #{limitGroupId,jdbcType=VARCHAR}
    where ID = #{ID,jdbcType=VARCHAR}
  </update>
</mapper>