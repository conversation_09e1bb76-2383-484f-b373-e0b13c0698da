# TestSample与TestSampleGroupInfoPO树型结构设计文档 v1.0

## 文档说明
本文档基于树型结构的方式对现有的TestSample进行重新设计，将TestSampleGroupInfoPO与TestSample整合为统一的树型结构体系，实现层级化的样本和分组管理。

## 设计理念
- **统一树型结构**：TestSampleGroupInfoPO和TestSample共同构成一个完整的树型体系
- **层级化管理**：通过树型结构实现分组和样本的层级化组织
- **灵活扩展**：支持动态调整树型结构和节点关系

---

## 核心对象重新设计

### 1. 抽象基类：TreeNode

```java
/**
 * 树型节点抽象基类
 * 为TestSample和TestSampleGroupInfoPO提供统一的树型结构能力
 */
@MappedSuperclass
public abstract class TreeNode {
    
    /** 节点ID */
    @Id
    private String id;
    
    /** 节点名称 */
    @Column(name = "name", nullable = false, length = 100)
    private String name;
    
    /** 节点编码 */
    @Column(name = "code", unique = true, nullable = false, length = 50)
    private String code;
    
    /** 父节点ID */
    @Column(name = "parent_id")
    private String parentId;
    
    /** 节点层级深度 */
    @Column(name = "level", nullable = false)
    private Integer level;
    
    /** 节点类型 */
    @Enumerated(EnumType.STRING)
    @Column(name = "node_type", nullable = false)
    private NodeType nodeType;
    
    /** 排序序号 */
    @Column(name = "sort_order")
    private Integer sortOrder;
    
    /** 节点状态 */
    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private NodeStatus status;
    
    /** 扩展属性 */
    @Column(name = "properties", columnDefinition = "JSON")
    private String properties;
    
    /** 创建信息 */
    @Column(name = "created_by", nullable = false)
    private String createdBy;
    
    @Column(name = "created_date", nullable = false)
    private Date createdDate;
    
    @Column(name = "modified_by")
    private String modifiedBy;
    
    @Column(name = "modified_date")
    private Date modifiedDate;
    
    // 抽象方法，由子类实现
    public abstract NodeType getNodeType();
    public abstract List<TreeNode> getChildren();
    public abstract void addChild(TreeNode child);
    public abstract void removeChild(TreeNode child);
}
```

### 2. 重新设计的TestSampleGroupInfoPO

```java
/**
 * 测试样本分组信息PO - 树型结构中的分组节点
 * 继承TreeNode，具备完整的树型结构能力
 */
@Entity
@Table(name = "test_sample_group_info")
public class TestSampleGroupInfoPO extends TreeNode {
    
    /** 分组类型 */
    @Enumerated(EnumType.STRING)
    @Column(name = "group_type", nullable = false)
    private GroupType groupType;
    
    /** 分组描述 */
    @Column(name = "description", length = 500)
    private String description;
    
    /** 分组配置 */
    @Column(name = "group_config", columnDefinition = "JSON")
    private String groupConfig;
    
    /** 是否允许包含子分组 */
    @Column(name = "allow_sub_groups")
    private Boolean allowSubGroups = true;
    
    /** 是否允许包含样本 */
    @Column(name = "allow_samples")
    private Boolean allowSamples = true;
    
    /** 最大子节点数量限制 */
    @Column(name = "max_children_count")
    private Integer maxChildrenCount;
    
    /** 子节点集合（延迟加载） */
    @Transient
    private List<TreeNode> children = new ArrayList<>();
    
    @Override
    public NodeType getNodeType() {
        return NodeType.GROUP;
    }
    
    @Override
    public List<TreeNode> getChildren() {
        if (children.isEmpty()) {
            // 延迟加载子节点
            loadChildren();
        }
        return children;
    }
    
    @Override
    public void addChild(TreeNode child) {
        if (child != null) {
            child.setParentId(this.getId());
            child.setLevel(this.getLevel() + 1);
            children.add(child);
        }
    }
    
    @Override
    public void removeChild(TreeNode child) {
        if (child != null) {
            children.remove(child);
            child.setParentId(null);
        }
    }
    
    /**
     * 延迟加载子节点
     */
    private void loadChildren() {
        // 通过Repository加载子节点
        // children = treeNodeRepository.findByParentId(this.getId());
    }
}
```

### 3. 重新设计的TestSample

```java
/**
 * 测试样本PO - 树型结构中的样本节点
 * 继承TreeNode，具备完整的树型结构能力
 */
@Entity
@Table(name = "test_sample")
public class TestSample extends TreeNode {
    
    /** 样本类型 */
    @Enumerated(EnumType.STRING)
    @Column(name = "sample_type", nullable = false)
    private SampleType sampleType;
    
    /** 测试数据 */
    @Column(name = "test_data", columnDefinition = "JSON")
    private String testData;
    
    /** 测试配置 */
    @Column(name = "test_config", columnDefinition = "JSON")
    private String testConfig;
    
    /** 执行状态 */
    @Enumerated(EnumType.STRING)
    @Column(name = "execution_status")
    private ExecutionStatus executionStatus;
    
    /** 测试结果 */
    @Column(name = "test_result", columnDefinition = "JSON")
    private String testResult;
    
    /** 是否为叶子节点 */
    @Column(name = "is_leaf")
    private Boolean isLeaf = false;
    
    /** 子节点集合（延迟加载） */
    @Transient
    private List<TreeNode> children = new ArrayList<>();
    
    @Override
    public NodeType getNodeType() {
        return NodeType.SAMPLE;
    }
    
    @Override
    public List<TreeNode> getChildren() {
        if (!isLeaf && children.isEmpty()) {
            // 延迟加载子节点
            loadChildren();
        }
        return children;
    }
    
    @Override
    public void addChild(TreeNode child) {
        if (!isLeaf && child != null) {
            child.setParentId(this.getId());
            child.setLevel(this.getLevel() + 1);
            children.add(child);
        }
    }
    
    @Override
    public void removeChild(TreeNode child) {
        if (child != null) {
            children.remove(child);
            child.setParentId(null);
        }
    }
    
    /**
     * 延迟加载子节点
     */
    private void loadChildren() {
        // 通过Repository加载子节点
        // children = treeNodeRepository.findByParentId(this.getId());
    }
}
```

### 4. 枚举定义

```java
/**
 * 节点类型枚举
 */
public enum NodeType {
    GROUP("分组节点"),
    SAMPLE("样本节点");
    
    private final String description;
    
    NodeType(String description) {
        this.description = description;
    }
    
    public String getDescription() {
        return description;
    }
}

/**
 * 分组类型枚举
 */
public enum GroupType {
    SHARE("共享型分组"),
    MIX("混合型分组"),
    CATEGORY("分类分组"),
    FUNCTIONAL("功能分组");
    
    private final String description;
    
    GroupType(String description) {
        this.description = description;
    }
}

/**
 * 样本类型枚举
 */
public enum SampleType {
    TEST_CASE("测试用例"),
    TEST_DATA("测试数据"),
    TEST_SUITE("测试套件"),
    TEMPLATE("模板样本");
    
    private final String description;
    
    SampleType(String description) {
        this.description = description;
    }
}

/**
 * 节点状态枚举
 */
public enum NodeStatus {
    ACTIVE("激活"),
    INACTIVE("停用"),
    DRAFT("草稿"),
    ARCHIVED("归档");
    
    private final String description;
    
    NodeStatus(String description) {
        this.description = description;
    }
}

/**
 * 执行状态枚举
 */
public enum ExecutionStatus {
    PENDING("待执行"),
    RUNNING("执行中"),
    SUCCESS("执行成功"),
    FAILED("执行失败"),
    SKIPPED("已跳过");
    
    private final String description;
    
    ExecutionStatus(String description) {
        this.description = description;
    }
}
```

---

## 树型结构管理服务

### TreeNodeService

```java
/**
 * 树型节点管理服务
 * 提供统一的树型结构操作能力
 */
@Service
public class TreeNodeService {
    
    @Autowired
    private TreeNodeRepository treeNodeRepository;
    
    /**
     * 创建子节点
     */
    public <T extends TreeNode> T createChild(String parentId, T childNode) {
        // 1. 验证父节点
        TreeNode parent = findById(parentId);
        if (parent == null) {
            throw new BusinessException("父节点不存在");
        }
        
        // 2. 验证层级深度
        if (parent.getLevel() >= MAX_DEPTH) {
            throw new BusinessException("已达到最大层级深度");
        }
        
        // 3. 验证节点类型兼容性
        validateNodeTypeCompatibility(parent, childNode);
        
        // 4. 设置层级关系
        childNode.setParentId(parentId);
        childNode.setLevel(parent.getLevel() + 1);
        
        // 5. 保存节点
        return (T) treeNodeRepository.save(childNode);
    }
    
    /**
     * 移动节点
     */
    public boolean moveNode(String nodeId, String newParentId) {
        TreeNode node = findById(nodeId);
        TreeNode newParent = findById(newParentId);
        
        // 验证移动的合法性
        validateMoveOperation(node, newParent);
        
        // 更新节点层级关系
        updateNodeHierarchy(node, newParent);
        
        return true;
    }
    
    /**
     * 删除节点（级联删除）
     */
    public boolean deleteNode(String nodeId) {
        TreeNode node = findById(nodeId);
        if (node == null) {
            return false;
        }
        
        // 获取所有子孙节点
        List<TreeNode> descendants = findAllDescendants(nodeId);
        
        // 级联删除
        treeNodeRepository.deleteAll(descendants);
        treeNodeRepository.delete(node);
        
        return true;
    }
    
    /**
     * 获取完整树结构
     */
    public List<TreeNode> getFullTree(String rootId) {
        TreeNode root = findById(rootId);
        if (root == null) {
            return Collections.emptyList();
        }
        
        return buildTreeStructure(root);
    }
    
    /**
     * 深度优先遍历
     */
    public void traverseDepthFirst(String rootId, Consumer<TreeNode> visitor) {
        TreeNode root = findById(rootId);
        if (root != null) {
            visitor.accept(root);
            root.getChildren().forEach(child -> 
                traverseDepthFirst(child.getId(), visitor));
        }
    }
    
    /**
     * 广度优先遍历
     */
    public void traverseBreadthFirst(String rootId, Consumer<TreeNode> visitor) {
        Queue<TreeNode> queue = new LinkedList<>();
        TreeNode root = findById(rootId);
        
        if (root != null) {
            queue.offer(root);
            while (!queue.isEmpty()) {
                TreeNode current = queue.poll();
                visitor.accept(current);
                queue.addAll(current.getChildren());
            }
        }
    }
    
    /**
     * 查找路径
     */
    public List<TreeNode> findPathToRoot(String nodeId) {
        List<TreeNode> path = new ArrayList<>();
        TreeNode current = findById(nodeId);
        
        while (current != null) {
            path.add(0, current);
            current = current.getParentId() != null ? 
                      findById(current.getParentId()) : null;
        }
        
        return path;
    }
    
    // 私有辅助方法
    private TreeNode findById(String id) {
        return treeNodeRepository.findById(id).orElse(null);
    }
    
    private void validateNodeTypeCompatibility(TreeNode parent, TreeNode child) {
        // 实现节点类型兼容性验证逻辑
        // 例如：GROUP节点可以包含GROUP和SAMPLE节点
        // SAMPLE节点只能包含SAMPLE节点
    }
    
    private void validateMoveOperation(TreeNode node, TreeNode newParent) {
        // 实现移动操作的合法性验证
        // 防止循环引用、层级深度超限等
    }
    
    private void updateNodeHierarchy(TreeNode node, TreeNode newParent) {
        // 实现节点层级关系的更新
        // 包括更新所有子孙节点的层级
    }
    
    private List<TreeNode> findAllDescendants(String nodeId) {
        // 实现查找所有子孙节点的逻辑
        return treeNodeRepository.findAllDescendants(nodeId);
    }
    
    private List<TreeNode> buildTreeStructure(TreeNode root) {
        // 实现构建完整树结构的逻辑
        List<TreeNode> result = new ArrayList<>();
        result.add(root);
        root.getChildren().forEach(child -> 
            result.addAll(buildTreeStructure(child)));
        return result;
    }
}
```

---

## 数据库表结构设计

```sql
-- 统一的树型节点表
CREATE TABLE tree_node (
    id VARCHAR(32) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(50) UNIQUE NOT NULL,
    parent_id VARCHAR(32),
    level INT NOT NULL DEFAULT 0,
    node_type VARCHAR(20) NOT NULL,
    sort_order INT DEFAULT 0,
    status VARCHAR(20) DEFAULT 'ACTIVE',
    properties JSON,
    created_by VARCHAR(50) NOT NULL,
    created_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    modified_by VARCHAR(50),
    modified_date TIMESTAMP,
    
    INDEX idx_parent_id (parent_id),
    INDEX idx_level (level),
    INDEX idx_node_type (node_type),
    INDEX idx_status (status),
    INDEX idx_code (code),
    FOREIGN KEY (parent_id) REFERENCES tree_node(id) ON DELETE CASCADE
);

-- 测试样本分组信息表
CREATE TABLE test_sample_group_info (
    id VARCHAR(32) PRIMARY KEY,
    group_type VARCHAR(20) NOT NULL,
    description VARCHAR(500),
    group_config JSON,
    allow_sub_groups BOOLEAN DEFAULT TRUE,
    allow_samples BOOLEAN DEFAULT TRUE,
    max_children_count INT,
    
    FOREIGN KEY (id) REFERENCES tree_node(id) ON DELETE CASCADE,
    INDEX idx_group_type (group_type)
);

-- 测试样本表
CREATE TABLE test_sample (
    id VARCHAR(32) PRIMARY KEY,
    sample_type VARCHAR(20) NOT NULL,
    test_data JSON,
    test_config JSON,
    execution_status VARCHAR(20),
    test_result JSON,
    is_leaf BOOLEAN DEFAULT FALSE,
    
    FOREIGN KEY (id) REFERENCES tree_node(id) ON DELETE CASCADE,
    INDEX idx_sample_type (sample_type),
    INDEX idx_execution_status (execution_status),
    INDEX idx_is_leaf (is_leaf)
);
```
