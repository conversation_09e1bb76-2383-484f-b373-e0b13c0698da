<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.otsnotes.dbstorages.mybatis.mapper.CustomerConclusionReferenceInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.otsnotes.dbstorages.mybatis.model.CustomerConclusionReferenceInfoPO" >
    <id column="ID" property="ID" jdbcType="VARCHAR" />
    <result column="ConclusionID" property="conclusionID" jdbcType="VARCHAR" />
    <result column="CustomerGroupCode" property="customerGroupCode" jdbcType="VARCHAR" />
    <result column="CustomerGroupName" property="customerGroupName" jdbcType="VARCHAR" />
    <result column="DisplayDescription" property="displayDescription" jdbcType="VARCHAR" />
    <result column="OverallDescription" property="overallDescription" jdbcType="VARCHAR" />
    <result column="ConclusionDescription" property="conclusionDescription" jdbcType="VARCHAR" />
    <result column="ConclusionInterpretation" property="conclusionInterpretation" jdbcType="VARCHAR" />
    <result column="ConclusionType" property="conclusionType" jdbcType="INTEGER" />
    <result column="ActiveIndicator" property="activeIndicator" jdbcType="BIT" />
    <result column="CreatedBy" property="createdBy" jdbcType="VARCHAR" />
    <result column="CreatedDate" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="ModifiedBy" property="modifiedBy" jdbcType="VARCHAR" />
    <result column="ModifiedDate" property="modifiedDate" jdbcType="TIMESTAMP" />
    <result column="ProductLineCode" property="productLineCode" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    ID, ConclusionID, CustomerGroupCode, CustomerGroupName, DisplayDescription, OverallDescription, 
    ConclusionDescription, ConclusionInterpretation, ConclusionType, ActiveIndicator, 
    CreatedBy, CreatedDate, ModifiedBy, ModifiedDate, ProductLineCode
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.CustomerConclusionReferenceInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_customer_conclusion_reference
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from tb_customer_conclusion_reference
    where ID = #{ID,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from tb_customer_conclusion_reference
    where ID = #{ID,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.CustomerConclusionReferenceInfoExample" >
    delete from tb_customer_conclusion_reference
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.CustomerConclusionReferenceInfoPO" >
    insert into tb_customer_conclusion_reference (ID, ConclusionID, CustomerGroupCode, 
      CustomerGroupName, DisplayDescription, 
      OverallDescription, ConclusionDescription, 
      ConclusionInterpretation, ConclusionType, 
      ActiveIndicator, CreatedBy, CreatedDate, 
      ModifiedBy, ModifiedDate, ProductLineCode
      )
    values (#{ID,jdbcType=VARCHAR}, #{conclusionID,jdbcType=VARCHAR}, #{customerGroupCode,jdbcType=VARCHAR}, 
      #{customerGroupName,jdbcType=VARCHAR}, #{displayDescription,jdbcType=VARCHAR}, 
      #{overallDescription,jdbcType=VARCHAR}, #{conclusionDescription,jdbcType=VARCHAR}, 
      #{conclusionInterpretation,jdbcType=VARCHAR}, #{conclusionType,jdbcType=INTEGER}, 
      #{activeIndicator,jdbcType=BIT}, #{createdBy,jdbcType=VARCHAR}, #{createdDate,jdbcType=TIMESTAMP}, 
      #{modifiedBy,jdbcType=VARCHAR}, #{modifiedDate,jdbcType=TIMESTAMP}, #{productLineCode,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.CustomerConclusionReferenceInfoPO" >
    insert into tb_customer_conclusion_reference
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="ID != null" >
        ID,
      </if>
      <if test="conclusionID != null" >
        ConclusionID,
      </if>
      <if test="customerGroupCode != null" >
        CustomerGroupCode,
      </if>
      <if test="customerGroupName != null" >
        CustomerGroupName,
      </if>
      <if test="displayDescription != null" >
        DisplayDescription,
      </if>
      <if test="overallDescription != null" >
        OverallDescription,
      </if>
      <if test="conclusionDescription != null" >
        ConclusionDescription,
      </if>
      <if test="conclusionInterpretation != null" >
        ConclusionInterpretation,
      </if>
      <if test="conclusionType != null" >
        ConclusionType,
      </if>
      <if test="activeIndicator != null" >
        ActiveIndicator,
      </if>
      <if test="createdBy != null" >
        CreatedBy,
      </if>
      <if test="createdDate != null" >
        CreatedDate,
      </if>
      <if test="modifiedBy != null" >
        ModifiedBy,
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate,
      </if>
      <if test="productLineCode != null" >
        ProductLineCode,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="ID != null" >
        #{ID,jdbcType=VARCHAR},
      </if>
      <if test="conclusionID != null" >
        #{conclusionID,jdbcType=VARCHAR},
      </if>
      <if test="customerGroupCode != null" >
        #{customerGroupCode,jdbcType=VARCHAR},
      </if>
      <if test="customerGroupName != null" >
        #{customerGroupName,jdbcType=VARCHAR},
      </if>
      <if test="displayDescription != null" >
        #{displayDescription,jdbcType=VARCHAR},
      </if>
      <if test="overallDescription != null" >
        #{overallDescription,jdbcType=VARCHAR},
      </if>
      <if test="conclusionDescription != null" >
        #{conclusionDescription,jdbcType=VARCHAR},
      </if>
      <if test="conclusionInterpretation != null" >
        #{conclusionInterpretation,jdbcType=VARCHAR},
      </if>
      <if test="conclusionType != null" >
        #{conclusionType,jdbcType=INTEGER},
      </if>
      <if test="activeIndicator != null" >
        #{activeIndicator,jdbcType=BIT},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="productLineCode != null" >
        #{productLineCode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.CustomerConclusionReferenceInfoExample" resultType="java.lang.Integer" >
    select count(*) from tb_customer_conclusion_reference
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_customer_conclusion_reference
    <set >
      <if test="record.ID != null" >
        ID = #{record.ID,jdbcType=VARCHAR},
      </if>
      <if test="record.conclusionID != null" >
        ConclusionID = #{record.conclusionID,jdbcType=VARCHAR},
      </if>
      <if test="record.customerGroupCode != null" >
        CustomerGroupCode = #{record.customerGroupCode,jdbcType=VARCHAR},
      </if>
      <if test="record.customerGroupName != null" >
        CustomerGroupName = #{record.customerGroupName,jdbcType=VARCHAR},
      </if>
      <if test="record.displayDescription != null" >
        DisplayDescription = #{record.displayDescription,jdbcType=VARCHAR},
      </if>
      <if test="record.overallDescription != null" >
        OverallDescription = #{record.overallDescription,jdbcType=VARCHAR},
      </if>
      <if test="record.conclusionDescription != null" >
        ConclusionDescription = #{record.conclusionDescription,jdbcType=VARCHAR},
      </if>
      <if test="record.conclusionInterpretation != null" >
        ConclusionInterpretation = #{record.conclusionInterpretation,jdbcType=VARCHAR},
      </if>
      <if test="record.conclusionType != null" >
        ConclusionType = #{record.conclusionType,jdbcType=INTEGER},
      </if>
      <if test="record.activeIndicator != null" >
        ActiveIndicator = #{record.activeIndicator,jdbcType=BIT},
      </if>
      <if test="record.createdBy != null" >
        CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdDate != null" >
        CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedBy != null" >
        ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.modifiedDate != null" >
        ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.productLineCode != null" >
        ProductLineCode = #{record.productLineCode,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_customer_conclusion_reference
    set ID = #{record.ID,jdbcType=VARCHAR},
      ConclusionID = #{record.conclusionID,jdbcType=VARCHAR},
      CustomerGroupCode = #{record.customerGroupCode,jdbcType=VARCHAR},
      CustomerGroupName = #{record.customerGroupName,jdbcType=VARCHAR},
      DisplayDescription = #{record.displayDescription,jdbcType=VARCHAR},
      OverallDescription = #{record.overallDescription,jdbcType=VARCHAR},
      ConclusionDescription = #{record.conclusionDescription,jdbcType=VARCHAR},
      ConclusionInterpretation = #{record.conclusionInterpretation,jdbcType=VARCHAR},
      ConclusionType = #{record.conclusionType,jdbcType=INTEGER},
      ActiveIndicator = #{record.activeIndicator,jdbcType=BIT},
      CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      ProductLineCode = #{record.productLineCode,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.CustomerConclusionReferenceInfoPO" >
    update tb_customer_conclusion_reference
    <set >
      <if test="conclusionID != null" >
        ConclusionID = #{conclusionID,jdbcType=VARCHAR},
      </if>
      <if test="customerGroupCode != null" >
        CustomerGroupCode = #{customerGroupCode,jdbcType=VARCHAR},
      </if>
      <if test="customerGroupName != null" >
        CustomerGroupName = #{customerGroupName,jdbcType=VARCHAR},
      </if>
      <if test="displayDescription != null" >
        DisplayDescription = #{displayDescription,jdbcType=VARCHAR},
      </if>
      <if test="overallDescription != null" >
        OverallDescription = #{overallDescription,jdbcType=VARCHAR},
      </if>
      <if test="conclusionDescription != null" >
        ConclusionDescription = #{conclusionDescription,jdbcType=VARCHAR},
      </if>
      <if test="conclusionInterpretation != null" >
        ConclusionInterpretation = #{conclusionInterpretation,jdbcType=VARCHAR},
      </if>
      <if test="conclusionType != null" >
        ConclusionType = #{conclusionType,jdbcType=INTEGER},
      </if>
      <if test="activeIndicator != null" >
        ActiveIndicator = #{activeIndicator,jdbcType=BIT},
      </if>
      <if test="createdBy != null" >
        CreatedBy = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        ModifiedBy = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="productLineCode != null" >
        ProductLineCode = #{productLineCode,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{ID,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.CustomerConclusionReferenceInfoPO" >
    update tb_customer_conclusion_reference
    set ConclusionID = #{conclusionID,jdbcType=VARCHAR},
      CustomerGroupCode = #{customerGroupCode,jdbcType=VARCHAR},
      CustomerGroupName = #{customerGroupName,jdbcType=VARCHAR},
      DisplayDescription = #{displayDescription,jdbcType=VARCHAR},
      OverallDescription = #{overallDescription,jdbcType=VARCHAR},
      ConclusionDescription = #{conclusionDescription,jdbcType=VARCHAR},
      ConclusionInterpretation = #{conclusionInterpretation,jdbcType=VARCHAR},
      ConclusionType = #{conclusionType,jdbcType=INTEGER},
      ActiveIndicator = #{activeIndicator,jdbcType=BIT},
      CreatedBy = #{createdBy,jdbcType=VARCHAR},
      CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{modifiedBy,jdbcType=VARCHAR},
      ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      ProductLineCode = #{productLineCode,jdbcType=VARCHAR}
    where ID = #{ID,jdbcType=VARCHAR}
  </update>
</mapper>