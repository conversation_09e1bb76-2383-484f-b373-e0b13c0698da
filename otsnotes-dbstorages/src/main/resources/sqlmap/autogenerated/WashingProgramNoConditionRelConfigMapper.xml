<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.otsnotes.dbstorages.mybatis.mapper.WashingProgramNoConditionRelConfigMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.otsnotes.dbstorages.mybatis.model.WashingProgramNoConditionRelConfigPO" >
    <id column="Id" property="id" jdbcType="BIGINT" />
    <result column="WashProgramId" property="washProgramId" jdbcType="BIGINT" />
    <result column="TestConditionTypeId" property="testConditionTypeId" jdbcType="INTEGER" />
    <result column="TestConditionId" property="testConditionId" jdbcType="INTEGER" />
    <result column="ConfigType" property="configType" jdbcType="INTEGER" />
    <result column="ConfigStatus" property="configStatus" jdbcType="INTEGER" />
    <result column="CreatedBy" property="createdBy" jdbcType="VARCHAR" />
    <result column="CreatedDate" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="ModifiedBy" property="modifiedBy" jdbcType="VARCHAR" />
    <result column="ModifiedDate" property="modifiedDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    Id, WashProgramId, TestConditionTypeId, TestConditionId, ConfigType, ConfigStatus, 
    CreatedBy, CreatedDate, ModifiedBy, ModifiedDate
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.WashingProgramNoConditionRelConfigExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_washing_program_no_condition_rel_config
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from tb_washing_program_no_condition_rel_config
    where Id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from tb_washing_program_no_condition_rel_config
    where Id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.WashingProgramNoConditionRelConfigExample" >
    delete from tb_washing_program_no_condition_rel_config
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.WashingProgramNoConditionRelConfigPO" >
    insert into tb_washing_program_no_condition_rel_config (Id, WashProgramId, TestConditionTypeId, 
      TestConditionId, ConfigType, ConfigStatus, 
      CreatedBy, CreatedDate, ModifiedBy, 
      ModifiedDate)
    values (#{id,jdbcType=BIGINT}, #{washProgramId,jdbcType=BIGINT}, #{testConditionTypeId,jdbcType=INTEGER}, 
      #{testConditionId,jdbcType=INTEGER}, #{configType,jdbcType=INTEGER}, #{configStatus,jdbcType=INTEGER}, 
      #{createdBy,jdbcType=VARCHAR}, #{createdDate,jdbcType=TIMESTAMP}, #{modifiedBy,jdbcType=VARCHAR}, 
      #{modifiedDate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.WashingProgramNoConditionRelConfigPO" >
    insert into tb_washing_program_no_condition_rel_config
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        Id,
      </if>
      <if test="washProgramId != null" >
        WashProgramId,
      </if>
      <if test="testConditionTypeId != null" >
        TestConditionTypeId,
      </if>
      <if test="testConditionId != null" >
        TestConditionId,
      </if>
      <if test="configType != null" >
        ConfigType,
      </if>
      <if test="configStatus != null" >
        ConfigStatus,
      </if>
      <if test="createdBy != null" >
        CreatedBy,
      </if>
      <if test="createdDate != null" >
        CreatedDate,
      </if>
      <if test="modifiedBy != null" >
        ModifiedBy,
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="washProgramId != null" >
        #{washProgramId,jdbcType=BIGINT},
      </if>
      <if test="testConditionTypeId != null" >
        #{testConditionTypeId,jdbcType=INTEGER},
      </if>
      <if test="testConditionId != null" >
        #{testConditionId,jdbcType=INTEGER},
      </if>
      <if test="configType != null" >
        #{configType,jdbcType=INTEGER},
      </if>
      <if test="configStatus != null" >
        #{configStatus,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.WashingProgramNoConditionRelConfigExample" resultType="java.lang.Integer" >
    select count(*) from tb_washing_program_no_condition_rel_config
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_washing_program_no_condition_rel_config
    <set >
      <if test="record.id != null" >
        Id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.washProgramId != null" >
        WashProgramId = #{record.washProgramId,jdbcType=BIGINT},
      </if>
      <if test="record.testConditionTypeId != null" >
        TestConditionTypeId = #{record.testConditionTypeId,jdbcType=INTEGER},
      </if>
      <if test="record.testConditionId != null" >
        TestConditionId = #{record.testConditionId,jdbcType=INTEGER},
      </if>
      <if test="record.configType != null" >
        ConfigType = #{record.configType,jdbcType=INTEGER},
      </if>
      <if test="record.configStatus != null" >
        ConfigStatus = #{record.configStatus,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null" >
        CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdDate != null" >
        CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedBy != null" >
        ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.modifiedDate != null" >
        ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_washing_program_no_condition_rel_config
    set Id = #{record.id,jdbcType=BIGINT},
      WashProgramId = #{record.washProgramId,jdbcType=BIGINT},
      TestConditionTypeId = #{record.testConditionTypeId,jdbcType=INTEGER},
      TestConditionId = #{record.testConditionId,jdbcType=INTEGER},
      ConfigType = #{record.configType,jdbcType=INTEGER},
      ConfigStatus = #{record.configStatus,jdbcType=INTEGER},
      CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.WashingProgramNoConditionRelConfigPO" >
    update tb_washing_program_no_condition_rel_config
    <set >
      <if test="washProgramId != null" >
        WashProgramId = #{washProgramId,jdbcType=BIGINT},
      </if>
      <if test="testConditionTypeId != null" >
        TestConditionTypeId = #{testConditionTypeId,jdbcType=INTEGER},
      </if>
      <if test="testConditionId != null" >
        TestConditionId = #{testConditionId,jdbcType=INTEGER},
      </if>
      <if test="configType != null" >
        ConfigType = #{configType,jdbcType=INTEGER},
      </if>
      <if test="configStatus != null" >
        ConfigStatus = #{configStatus,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null" >
        CreatedBy = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        ModifiedBy = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where Id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.WashingProgramNoConditionRelConfigPO" >
    update tb_washing_program_no_condition_rel_config
    set WashProgramId = #{washProgramId,jdbcType=BIGINT},
      TestConditionTypeId = #{testConditionTypeId,jdbcType=INTEGER},
      TestConditionId = #{testConditionId,jdbcType=INTEGER},
      ConfigType = #{configType,jdbcType=INTEGER},
      ConfigStatus = #{configStatus,jdbcType=INTEGER},
      CreatedBy = #{createdBy,jdbcType=VARCHAR},
      CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{modifiedBy,jdbcType=VARCHAR},
      ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP}
    where Id = #{id,jdbcType=BIGINT}
  </update>
</mapper>