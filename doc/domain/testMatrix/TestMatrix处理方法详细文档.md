# TestMatrix处理方法详细文档

## 文档概述

本文档基于实际代码分析，详细梳理了TestMatrix对象的所有处理方法，包括方法签名、权限控制、触发动作、触发规则等，严格基于代码事实。

**【事实】** 基于以下核心文件的实际代码分析：
- `TestMatrixService.java` - TestMatrix核心业务服务 (2597行)
- `TestMatrixCopyService.java` - TestMatrix复制服务
- `TestMatrixFacadeImpl.java` - TestMatrix门面实现
- `TestMatrixBizServiceImpl.java` - TestMatrix业务处理服务

## 方法分类统计总览

**【事实】** TestMatrix处理方法统计表：

| 分类 | 方法数量 | 占比 | 主要功能 | 权限要求 |
|------|----------|------|----------|----------|
| **Matrix核心操作** | 4个 | 25.0% | 确认、删除、复制、取消Matrix | 报告状态控制 |
| **Matrix查询相关** | 4个 | 25.0% | 查询Matrix信息和备注 | 基础查询权限 |
| **Matrix数据管理** | 3个 | 18.8% | 更新备注、条件组、状态 | 数据修改权限 |
| **Matrix分包相关** | 2个 | 12.5% | 分包Matrix查询和处理 | 分包操作权限 |
| **Matrix辅助工具** | 2个 | 12.5% | 样品校验、数据验证 | 基础验证权限 |
| **Matrix业务集成** | 1个 | 6.2% | 与外部系统集成 | 系统集成权限 |
| **总计** | **16个** | **100%** | 完整的Matrix管理功能 | 多层次权限控制 |

## 详细方法分类表格

### 1. Matrix核心操作方法 (4个)

**【事实】** 基于TestMatrixService的核心业务方法：

| 方法名 | 对象 | 权限控制 | 触发动作 | 主要触发规则 | 关联对象影响 | 代码位置 |
|--------|------|----------|----------|-------------|-------------|----------|
| **confirmMatrix** | TestMatrix | @AccessRule(reportStatus={Approved,Cancelled}, disGroupKey="Analyte_Repeat") | 确认测试矩阵，创建Matrix记录并建立关联关系 | 样品全部分配、TestLine状态为Typing/Entered/SubContracted/DR、有确认权限 | **TestMatrix**: 新增Matrix记录<br/>**ReportMatrix**: 报告关联创建<br/>**TestCondition**: 测试条件关联<br/>**Limit**: 限值数据创建<br/>**LimitGroup**: 限值组创建<br/>**TestLine**: 状态可能更新<br/>**TestSample**: 样品分配确认 | TestMatrixService.java:527 |
| **deleteTest** | TestMatrix | @AccessRule(reportStatus={Approved,Cancelled}) | 删除指定MatrixGroupId的所有Matrix及关联数据 | TestLine状态为Typing（分包订单除外）、数据未锁定、有删除权限 | **TestMatrix**: Matrix记录删除<br/>**ReportMatrix**: 报告关联删除<br/>**TestCondition**: 测试条件清理<br/>**Limit**: 限值数据删除<br/>**LimitGroup**: 限值组删除<br/>**TestData**: 测试数据清理<br/>**TestLine**: 状态可能回退 | TestMatrixService.java:2169 |
| **saveCopyTest** | TestMatrix | @AccessRule(reportStatus={Approved,Cancelled}, subContractType=CopyTestLine, testLinePendingType=TestLineInstanceId) | 复制现有Matrix到新样品，创建新Matrix记录 | TestLine状态为Typing/Entered、有复制权限、目标样品有效 | **TestMatrix**: 新增复制Matrix<br/>**ReportMatrix**: 复制报告关联<br/>**TestCondition**: 复制测试条件<br/>**Limit**: 复制限值数据<br/>**TestSample**: 新样品分配关系<br/>**TestData**: 数据模板复制 | TestMatrixService.java:2273 |
| **cancelAssignSample** | TestMatrix | @AccessRule(subContractType=CancelAssignSample) | 取消或恢复Matrix的样品分配，切换activeIndicator状态 | TestSampleId和TestLineInstanceId非空、Matrix记录存在、有取消权限 | **TestMatrix**: activeIndicator状态切换<br/>**TestSample**: 样品分配状态调整<br/>**ReportMatrix**: 报告关联状态更新<br/>**TestData**: 数据有效性标记<br/>**TestLine**: 状态可能调整 | TestMatrixService.java:2084 |

### 2. Matrix查询相关方法 (4个)

**【事实】** 基于TestMatrixService的查询方法：

| 方法名 | 对象 | 触发动作 | 主要触发规则 | 关联对象影响 | 代码位置 |
|--------|------|----------|-------------|-------------|----------|
| **getTestMatrixRemark** | TestMatrix | 获取测试矩阵备注信息，执行混测校验并返回不通过的Matrix列表 | testLineInstanceId和matrixGroupId非空、Matrix记录存在 | **TestMatrix**: Matrix备注查询<br/>**TestLine**: 测试线关联验证<br/>**MixTestValidation**: 混测校验执行 | TestMatrixService.java:346 |
| **getMatrixListByReportNos** | TestMatrix | 根据报告号列表查询PP Matrix信息，包含TestLine别名处理 | 报告号列表非空、报告记录存在 | **TestMatrix**: PP Matrix查询<br/>**Report**: 报告关联验证<br/>**TestLine**: 别名信息处理<br/>**PPTestLineRel**: PP关系查询 | TestMatrixService.java:2344 |
| **getSubContractTestMatrixList** | TestMatrix | 获取分包测试矩阵列表，包含PP基础信息关联 | 分包号非空、分包记录存在 | **TestMatrix**: 分包Matrix查询<br/>**SubContract**: 分包关联验证<br/>**PPTestLineRel**: PP基础信息关联<br/>**TestLine**: 测试线信息获取 | TestMatrixService.java:2538 |
| **getTestMatrixInfoByTestLineInstanceIds** | TestMatrix | 根据测试线实例ID列表批量获取Matrix信息 | testLineInstanceIds列表非空、TestLine记录存在 | **TestMatrix**: 批量Matrix查询<br/>**TestLine**: 测试线关联验证<br/>**TestSample**: 样品分配信息<br/>**TestCondition**: 测试条件关联 | TestMatrixBizServiceImpl.java:26 |

### 3. Matrix数据管理方法 (3个)

**【事实】** 基于TestMatrixService的数据管理方法：

| 方法名 | 对象 | 触发动作 | 主要触发规则 | 关联对象影响 | 代码位置 |
|--------|------|----------|-------------|-------------|----------|
| **updateTestMatrixRemark** | TestMatrix | 更新测试矩阵的备注信息，记录修改人和修改时间 | testMatrixId非空、用户登录有效、备注信息有效 | **TestMatrix**: 备注信息更新<br/>**TestMatrixRemark**: 备注历史记录<br/>**User**: 修改人信息记录<br/>**AuditLog**: 操作审计日志 | TestMatrixService.java:308 |
| **updateTestConditionGroupIdByTestLineId** | TestMatrix | 更新Matrix的测试条件组ID，清空原有关联 | TestLineInstanceID非空、用户权限有效 | **TestMatrix**: 条件组ID更新<br/>**TestCondition**: 测试条件关联清理<br/>**TestLine**: 测试线关联验证<br/>**ConditionGroup**: 条件组关系重建 | TestMatrixService.java:1805 |
| **updateMatrixStatus** | TestMatrix | 批量更新Matrix状态，支持状态同步和条件ID更新 | Matrix列表非空、状态映射有效、操作类型明确 | **TestMatrix**: 批量状态更新<br/>**TestLine**: 测试线状态同步<br/>**TestData**: 数据状态联动<br/>**ReportMatrix**: 报告关联状态更新<br/>**TestCondition**: 条件ID批量更新 | TestMatrixCopyService.java:526 |

### 4. Matrix分包相关方法 (2个)

**【事实】** 基于分包业务的Matrix处理方法：

| 方法名 | 对象 | 触发动作 | 主要触发规则 | 代码位置 |
|--------|------|----------|-------------|----------|
| **getSubContractTestMatrixList** | SubContractMatrix | 获取分包测试矩阵列表，关联PP基础信息和Citation信息 | 分包号非空、分包状态有效 | TestMatrixService.java:2538 |
| **checkTLSubDataLock** | SubContractMatrix | 检查分包测试线数据锁定状态，防止并发修改 | TestLineInstanceId非空、分包记录存在 | TestMatrixService.java内部调用 |

### 5. Matrix辅助工具方法 (2个)

**【事实】** 基于TestMatrixService的辅助工具方法：

| 方法名 | 对象 | 触发动作 | 主要触发规则 | 代码位置 |
|--------|------|----------|-------------|----------|
| **checkSample** | TestMatrix | 校验样品是否全部被使用，确保Matrix确认前的数据完整性 | orderNo非空、matrixType有效、样品分配完整 | TestMatrixService.java内部调用 |
| **performMixSampleValidation** | TestMatrix | 执行混测样品校验，识别不符合混测要求的样品 | testMatrixInfoPOList非空、productLineCode有效、orderNo存在 | TestMatrixService.java:361 |

### 6. Matrix业务集成方法 (1个)

**【事实】** 基于外部系统集成的Matrix方法：

| 方法名 | 对象 | 触发动作 | 主要触发规则 | 代码位置 |
|--------|------|----------|-------------|----------|
| **getTestMatrixListForCopy** | TestMatrix | 获取用于复制的测试矩阵列表，支持跨报告复制 | CopyMatrixReportReq参数有效、源报告存在、目标报告有效 | TestMatrixFacadeImpl.java:35 |

## Matrix状态管理集成

**【事实】** TestMatrix方法与状态管理的集成关系：

### 状态转换触发方法
| 方法名 | 状态变更类型 | 状态转换逻辑 | 外部系统联动 |
|--------|-------------|-------------|-------------|
| **confirmMatrix** | 创建时状态设置 | 新Matrix状态 = TestLine当前状态 | 可能触发订单状态变更 |
| **saveCopyTest** | 复制时状态继承 | 新Matrix状态 = 源TestLine状态 | 无 |
| **cancelAssignSample** | 激活状态切换 | activeIndicator切换，状态相应变更 | TestData状态联动 |
| **updateMatrixStatus** | 批量状态同步 | Matrix状态 = TestLine状态（活跃Matrix） | 分包状态同步 |

### 状态约束检查方法
| 方法名 | 状态检查逻辑 | 约束条件 | 违规处理 |
|--------|-------------|----------|----------|
| **deleteTest** | TestLine状态必须为Typing | 分包订单例外 | 返回错误信息 |
| **saveCopyTest** | TestLine状态必须为Typing/Entered | 无例外 | 返回错误信息 |
| **confirmMatrix** | TestLine状态必须为Typing/Entered/SubContracted/DR | 报告状态同时检查 | 返回错误信息 |

## 权限控制矩阵

**【事实】** 基于@AccessRule注解的权限控制：

### 报告状态权限
| 权限类型 | 允许的报告状态 | 适用方法 | 业务含义 |
|----------|---------------|----------|----------|
| **reportStatus={Approved,Cancelled}** | 已批准、已取消 | confirmMatrix, deleteTest, saveCopyTest | 只有稳定状态的报告才能进行Matrix操作 |

### 分包操作权限
| 权限类型 | 操作类型 | 适用方法 | 业务含义 |
|----------|----------|----------|----------|
| **subContractType=CopyTestLine** | 复制测试线 | saveCopyTest | 分包场景下的Matrix复制权限 |
| **subContractType=CancelAssignSample** | 取消样品分配 | cancelAssignSample | 分包场景下的样品取消权限 |

### 特殊权限控制
| 权限类型 | 控制参数 | 适用方法 | 业务含义 |
|----------|----------|----------|----------|
| **disGroupKey="Analyte_Repeat"** | 分析物重复控制 | confirmMatrix | 防止重复分析物的Matrix确认 |
| **testLinePendingType=TestLineInstanceId** | 测试线挂起控制 | saveCopyTest | 基于TestLineInstanceId的挂起控制 |

## 事务处理和数据一致性

**【事实】** 基于代码中的事务处理机制：

### 事务范围控制
- **confirmMatrix**: 在事务中创建Matrix记录和ReportMatrixRelationship
- **deleteTest**: 在事务中删除Matrix及所有关联数据（Conclusion、TestCondition、TestPosition）
- **saveCopyTest**: 在事务中创建新Matrix和建立关联关系
- **cancelAssignSample**: 在事务中更新Matrix状态和TestData状态

### 数据一致性保证
- **关联数据同步**: Matrix状态变更时同步更新相关TestData状态
- **序号重排**: 删除Matrix时自动重排GroupId保持连续性
- **状态联动**: Matrix状态与TestLine状态保持一致性
- **回滚机制**: 任何步骤失败时完整回滚所有变更

## 错误处理和异常机制

**【事实】** 基于代码中的错误处理模式：

### 常见错误类型和处理
- **参数验证错误**: "分包号不能为空"、"Params Error"
- **状态检查错误**: "Only testline in Typing state can delete test"
- **权限验证错误**: 用户无相应操作权限
- **数据锁定错误**: "data locked,Please click unlock button first"
- **业务规则违反**: 样品未全部分配、Matrix不存在等

### 异常处理策略
- **立即返回**: 参数验证失败时立即返回CustomResult.fail()
- **事务回滚**: 业务操作失败时设置tranStatus.setRollbackOnly()
- **日志记录**: 使用logger.error()记录详细错误信息
- **状态保护**: 错误情况下保持原有状态不变

---

**文档生成时间**: 2025-09-28  
**基于代码版本**: 当前项目代码  
**维护责任**: TestMatrix管理模块开发团队
