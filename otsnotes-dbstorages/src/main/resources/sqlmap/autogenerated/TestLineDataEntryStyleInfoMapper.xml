<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.otsnotes.dbstorages.mybatis.mapper.TestLineDataEntryStyleInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineDataEntryStyleInfoPO" >
    <id column="StyleVersionID" property="styleVersionID" jdbcType="INTEGER" />
    <result column="ID" property="ID" jdbcType="VARCHAR" />
    <result column="TestLineID" property="testLineID" jdbcType="INTEGER" />
    <result column="DataEntryStyle" property="dataEntryStyle" jdbcType="INTEGER" />
    <result column="FormulaID" property="formulaID" jdbcType="INTEGER" />
    <result column="RoundingCodeID" property="roundingCodeID" jdbcType="INTEGER" />
    <result column="Remark" property="remark" jdbcType="VARCHAR" />
    <result column="TestItem" property="testItem" jdbcType="VARCHAR" />
    <result column="IsAutoCalculation" property="isAutoCalculation" jdbcType="BIT" />
    <result column="ActiveIndicator" property="activeIndicator" jdbcType="BIT" />
    <result column="CreatedDate" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="CreatedBy" property="createdBy" jdbcType="VARCHAR" />
    <result column="ModifiedDate" property="modifiedDate" jdbcType="TIMESTAMP" />
    <result column="ModifiedBy" property="modifiedBy" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    StyleVersionID, ID, TestLineID, DataEntryStyle, FormulaID, RoundingCodeID, Remark, 
    TestItem, IsAutoCalculation, ActiveIndicator, CreatedDate, CreatedBy, ModifiedDate, 
    ModifiedBy
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineDataEntryStyleInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_test_line_data_entry_style
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from tb_test_line_data_entry_style
    where StyleVersionID = #{styleVersionID,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from tb_test_line_data_entry_style
    where StyleVersionID = #{styleVersionID,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineDataEntryStyleInfoExample" >
    delete from tb_test_line_data_entry_style
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineDataEntryStyleInfoPO" >
    insert into tb_test_line_data_entry_style (StyleVersionID, ID, TestLineID, 
      DataEntryStyle, FormulaID, RoundingCodeID, 
      Remark, TestItem, IsAutoCalculation, 
      ActiveIndicator, CreatedDate, CreatedBy, 
      ModifiedDate, ModifiedBy)
    values (#{styleVersionID,jdbcType=INTEGER}, #{ID,jdbcType=VARCHAR}, #{testLineID,jdbcType=INTEGER}, 
      #{dataEntryStyle,jdbcType=INTEGER}, #{formulaID,jdbcType=INTEGER}, #{roundingCodeID,jdbcType=INTEGER}, 
      #{remark,jdbcType=VARCHAR}, #{testItem,jdbcType=VARCHAR}, #{isAutoCalculation,jdbcType=BIT}, 
      #{activeIndicator,jdbcType=BIT}, #{createdDate,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=VARCHAR}, 
      #{modifiedDate,jdbcType=TIMESTAMP}, #{modifiedBy,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineDataEntryStyleInfoPO" >
    insert into tb_test_line_data_entry_style
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="styleVersionID != null" >
        StyleVersionID,
      </if>
      <if test="ID != null" >
        ID,
      </if>
      <if test="testLineID != null" >
        TestLineID,
      </if>
      <if test="dataEntryStyle != null" >
        DataEntryStyle,
      </if>
      <if test="formulaID != null" >
        FormulaID,
      </if>
      <if test="roundingCodeID != null" >
        RoundingCodeID,
      </if>
      <if test="remark != null" >
        Remark,
      </if>
      <if test="testItem != null" >
        TestItem,
      </if>
      <if test="isAutoCalculation != null" >
        IsAutoCalculation,
      </if>
      <if test="activeIndicator != null" >
        ActiveIndicator,
      </if>
      <if test="createdDate != null" >
        CreatedDate,
      </if>
      <if test="createdBy != null" >
        CreatedBy,
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate,
      </if>
      <if test="modifiedBy != null" >
        ModifiedBy,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="styleVersionID != null" >
        #{styleVersionID,jdbcType=INTEGER},
      </if>
      <if test="ID != null" >
        #{ID,jdbcType=VARCHAR},
      </if>
      <if test="testLineID != null" >
        #{testLineID,jdbcType=INTEGER},
      </if>
      <if test="dataEntryStyle != null" >
        #{dataEntryStyle,jdbcType=INTEGER},
      </if>
      <if test="formulaID != null" >
        #{formulaID,jdbcType=INTEGER},
      </if>
      <if test="roundingCodeID != null" >
        #{roundingCodeID,jdbcType=INTEGER},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="testItem != null" >
        #{testItem,jdbcType=VARCHAR},
      </if>
      <if test="isAutoCalculation != null" >
        #{isAutoCalculation,jdbcType=BIT},
      </if>
      <if test="activeIndicator != null" >
        #{activeIndicator,jdbcType=BIT},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineDataEntryStyleInfoExample" resultType="java.lang.Integer" >
    select count(*) from tb_test_line_data_entry_style
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_test_line_data_entry_style
    <set >
      <if test="record.styleVersionID != null" >
        StyleVersionID = #{record.styleVersionID,jdbcType=INTEGER},
      </if>
      <if test="record.ID != null" >
        ID = #{record.ID,jdbcType=VARCHAR},
      </if>
      <if test="record.testLineID != null" >
        TestLineID = #{record.testLineID,jdbcType=INTEGER},
      </if>
      <if test="record.dataEntryStyle != null" >
        DataEntryStyle = #{record.dataEntryStyle,jdbcType=INTEGER},
      </if>
      <if test="record.formulaID != null" >
        FormulaID = #{record.formulaID,jdbcType=INTEGER},
      </if>
      <if test="record.roundingCodeID != null" >
        RoundingCodeID = #{record.roundingCodeID,jdbcType=INTEGER},
      </if>
      <if test="record.remark != null" >
        Remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.testItem != null" >
        TestItem = #{record.testItem,jdbcType=VARCHAR},
      </if>
      <if test="record.isAutoCalculation != null" >
        IsAutoCalculation = #{record.isAutoCalculation,jdbcType=BIT},
      </if>
      <if test="record.activeIndicator != null" >
        ActiveIndicator = #{record.activeIndicator,jdbcType=BIT},
      </if>
      <if test="record.createdDate != null" >
        CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdBy != null" >
        CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.modifiedDate != null" >
        ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedBy != null" >
        ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_test_line_data_entry_style
    set StyleVersionID = #{record.styleVersionID,jdbcType=INTEGER},
      ID = #{record.ID,jdbcType=VARCHAR},
      TestLineID = #{record.testLineID,jdbcType=INTEGER},
      DataEntryStyle = #{record.dataEntryStyle,jdbcType=INTEGER},
      FormulaID = #{record.formulaID,jdbcType=INTEGER},
      RoundingCodeID = #{record.roundingCodeID,jdbcType=INTEGER},
      Remark = #{record.remark,jdbcType=VARCHAR},
      TestItem = #{record.testItem,jdbcType=VARCHAR},
      IsAutoCalculation = #{record.isAutoCalculation,jdbcType=BIT},
      ActiveIndicator = #{record.activeIndicator,jdbcType=BIT},
      CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineDataEntryStyleInfoPO" >
    update tb_test_line_data_entry_style
    <set >
      <if test="ID != null" >
        ID = #{ID,jdbcType=VARCHAR},
      </if>
      <if test="testLineID != null" >
        TestLineID = #{testLineID,jdbcType=INTEGER},
      </if>
      <if test="dataEntryStyle != null" >
        DataEntryStyle = #{dataEntryStyle,jdbcType=INTEGER},
      </if>
      <if test="formulaID != null" >
        FormulaID = #{formulaID,jdbcType=INTEGER},
      </if>
      <if test="roundingCodeID != null" >
        RoundingCodeID = #{roundingCodeID,jdbcType=INTEGER},
      </if>
      <if test="remark != null" >
        Remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="testItem != null" >
        TestItem = #{testItem,jdbcType=VARCHAR},
      </if>
      <if test="isAutoCalculation != null" >
        IsAutoCalculation = #{isAutoCalculation,jdbcType=BIT},
      </if>
      <if test="activeIndicator != null" >
        ActiveIndicator = #{activeIndicator,jdbcType=BIT},
      </if>
      <if test="createdDate != null" >
        CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        CreatedBy = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        ModifiedBy = #{modifiedBy,jdbcType=VARCHAR},
      </if>
    </set>
    where StyleVersionID = #{styleVersionID,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineDataEntryStyleInfoPO" >
    update tb_test_line_data_entry_style
    set ID = #{ID,jdbcType=VARCHAR},
      TestLineID = #{testLineID,jdbcType=INTEGER},
      DataEntryStyle = #{dataEntryStyle,jdbcType=INTEGER},
      FormulaID = #{formulaID,jdbcType=INTEGER},
      RoundingCodeID = #{roundingCodeID,jdbcType=INTEGER},
      Remark = #{remark,jdbcType=VARCHAR},
      TestItem = #{testItem,jdbcType=VARCHAR},
      IsAutoCalculation = #{isAutoCalculation,jdbcType=BIT},
      ActiveIndicator = #{activeIndicator,jdbcType=BIT},
      CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      CreatedBy = #{createdBy,jdbcType=VARCHAR},
      ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{modifiedBy,jdbcType=VARCHAR}
    where StyleVersionID = #{styleVersionID,jdbcType=INTEGER}
  </update>
</mapper>