<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.otsnotes.dbstorages.mybatis.mapper.TestConditionInstanceMultipleLanguageMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.otsnotes.dbstorages.mybatis.model.TestConditionInstanceMultipleLanguagePO" >
    <id column="ID" property="ID" jdbcType="VARCHAR" />
    <result column="TestConditionInstanceID" property="testConditionInstanceID" jdbcType="VARCHAR" />
    <result column="TestConditionName" property="testConditionName" jdbcType="VARCHAR" />
    <result column="TestConditionTypeName" property="testConditionTypeName" jdbcType="VARCHAR" />
    <result column="TestConditionDesc" property="testConditionDesc" jdbcType="VARCHAR" />
    <result column="LanguageId" property="languageId" jdbcType="INTEGER" />
    <result column="CreatedDate" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="CreatedBy" property="createdBy" jdbcType="VARCHAR" />
    <result column="ModifiedDate" property="modifiedDate" jdbcType="TIMESTAMP" />
    <result column="ModifiedBy" property="modifiedBy" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    ID, TestConditionInstanceID, TestConditionName, TestConditionTypeName, TestConditionDesc, 
    LanguageId, CreatedDate, CreatedBy, ModifiedDate, ModifiedBy
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestConditionInstanceMultipleLanguageExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_test_condition_instance_multiplelanguage
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from tb_test_condition_instance_multiplelanguage
    where ID = #{ID,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from tb_test_condition_instance_multiplelanguage
    where ID = #{ID,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestConditionInstanceMultipleLanguageExample" >
    delete from tb_test_condition_instance_multiplelanguage
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestConditionInstanceMultipleLanguagePO" >
    insert into tb_test_condition_instance_multiplelanguage (ID, TestConditionInstanceID, TestConditionName, 
      TestConditionTypeName, TestConditionDesc, 
      LanguageId, CreatedDate, CreatedBy, 
      ModifiedDate, ModifiedBy)
    values (#{ID,jdbcType=VARCHAR}, #{testConditionInstanceID,jdbcType=VARCHAR}, #{testConditionName,jdbcType=VARCHAR}, 
      #{testConditionTypeName,jdbcType=VARCHAR}, #{testConditionDesc,jdbcType=VARCHAR}, 
      #{languageId,jdbcType=INTEGER}, #{createdDate,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=VARCHAR}, 
      #{modifiedDate,jdbcType=TIMESTAMP}, #{modifiedBy,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestConditionInstanceMultipleLanguagePO" >
    insert into tb_test_condition_instance_multiplelanguage
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="ID != null" >
        ID,
      </if>
      <if test="testConditionInstanceID != null" >
        TestConditionInstanceID,
      </if>
      <if test="testConditionName != null" >
        TestConditionName,
      </if>
      <if test="testConditionTypeName != null" >
        TestConditionTypeName,
      </if>
      <if test="testConditionDesc != null" >
        TestConditionDesc,
      </if>
      <if test="languageId != null" >
        LanguageId,
      </if>
      <if test="createdDate != null" >
        CreatedDate,
      </if>
      <if test="createdBy != null" >
        CreatedBy,
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate,
      </if>
      <if test="modifiedBy != null" >
        ModifiedBy,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="ID != null" >
        #{ID,jdbcType=VARCHAR},
      </if>
      <if test="testConditionInstanceID != null" >
        #{testConditionInstanceID,jdbcType=VARCHAR},
      </if>
      <if test="testConditionName != null" >
        #{testConditionName,jdbcType=VARCHAR},
      </if>
      <if test="testConditionTypeName != null" >
        #{testConditionTypeName,jdbcType=VARCHAR},
      </if>
      <if test="testConditionDesc != null" >
        #{testConditionDesc,jdbcType=VARCHAR},
      </if>
      <if test="languageId != null" >
        #{languageId,jdbcType=INTEGER},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestConditionInstanceMultipleLanguageExample" resultType="java.lang.Integer" >
    select count(*) from tb_test_condition_instance_multiplelanguage
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_test_condition_instance_multiplelanguage
    <set >
      <if test="record.ID != null" >
        ID = #{record.ID,jdbcType=VARCHAR},
      </if>
      <if test="record.testConditionInstanceID != null" >
        TestConditionInstanceID = #{record.testConditionInstanceID,jdbcType=VARCHAR},
      </if>
      <if test="record.testConditionName != null" >
        TestConditionName = #{record.testConditionName,jdbcType=VARCHAR},
      </if>
      <if test="record.testConditionTypeName != null" >
        TestConditionTypeName = #{record.testConditionTypeName,jdbcType=VARCHAR},
      </if>
      <if test="record.testConditionDesc != null" >
        TestConditionDesc = #{record.testConditionDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.languageId != null" >
        LanguageId = #{record.languageId,jdbcType=INTEGER},
      </if>
      <if test="record.createdDate != null" >
        CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdBy != null" >
        CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.modifiedDate != null" >
        ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedBy != null" >
        ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_test_condition_instance_multiplelanguage
    set ID = #{record.ID,jdbcType=VARCHAR},
      TestConditionInstanceID = #{record.testConditionInstanceID,jdbcType=VARCHAR},
      TestConditionName = #{record.testConditionName,jdbcType=VARCHAR},
      TestConditionTypeName = #{record.testConditionTypeName,jdbcType=VARCHAR},
      TestConditionDesc = #{record.testConditionDesc,jdbcType=VARCHAR},
      LanguageId = #{record.languageId,jdbcType=INTEGER},
      CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestConditionInstanceMultipleLanguagePO" >
    update tb_test_condition_instance_multiplelanguage
    <set >
      <if test="testConditionInstanceID != null" >
        TestConditionInstanceID = #{testConditionInstanceID,jdbcType=VARCHAR},
      </if>
      <if test="testConditionName != null" >
        TestConditionName = #{testConditionName,jdbcType=VARCHAR},
      </if>
      <if test="testConditionTypeName != null" >
        TestConditionTypeName = #{testConditionTypeName,jdbcType=VARCHAR},
      </if>
      <if test="testConditionDesc != null" >
        TestConditionDesc = #{testConditionDesc,jdbcType=VARCHAR},
      </if>
      <if test="languageId != null" >
        LanguageId = #{languageId,jdbcType=INTEGER},
      </if>
      <if test="createdDate != null" >
        CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        CreatedBy = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        ModifiedBy = #{modifiedBy,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{ID,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestConditionInstanceMultipleLanguagePO" >
    update tb_test_condition_instance_multiplelanguage
    set TestConditionInstanceID = #{testConditionInstanceID,jdbcType=VARCHAR},
      TestConditionName = #{testConditionName,jdbcType=VARCHAR},
      TestConditionTypeName = #{testConditionTypeName,jdbcType=VARCHAR},
      TestConditionDesc = #{testConditionDesc,jdbcType=VARCHAR},
      LanguageId = #{languageId,jdbcType=INTEGER},
      CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      CreatedBy = #{createdBy,jdbcType=VARCHAR},
      ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{modifiedBy,jdbcType=VARCHAR}
    where ID = #{ID,jdbcType=VARCHAR}
  </update>
</mapper>