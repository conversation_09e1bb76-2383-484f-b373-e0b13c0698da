<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.otsnotes.dbstorages.mybatis.mapper.UsageTypePositionLanguageInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.otsnotes.dbstorages.mybatis.model.UsageTypePositionLanguageInfoPO" >
    <id column="Id" property="id" jdbcType="BIGINT" />
    <result column="UsageTypePositionRelId" property="usageTypePositionRelId" jdbcType="BIGINT" />
    <result column="PositionId" property="positionId" jdbcType="BIGINT" />
    <result column="PositionName" property="positionName" jdbcType="VARCHAR" />
    <result column="PositionDescription" property="positionDescription" jdbcType="VARCHAR" />
    <result column="LanguageId" property="languageId" jdbcType="INTEGER" />
    <result column="BizVersionId" property="bizVersionId" jdbcType="CHAR" />
    <result column="LangStatus" property="langStatus" jdbcType="INTEGER" />
    <result column="CreatedDate" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="ModifiedDate" property="modifiedDate" jdbcType="BIGINT" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    Id, UsageTypePositionRelId, PositionId, PositionName, PositionDescription, LanguageId, 
    BizVersionId, LangStatus, CreatedDate, ModifiedDate
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.UsageTypePositionLanguageInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_trims_usagetype_position_language
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from tb_trims_usagetype_position_language
    where Id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from tb_trims_usagetype_position_language
    where Id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.UsageTypePositionLanguageInfoExample" >
    delete from tb_trims_usagetype_position_language
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.UsageTypePositionLanguageInfoPO" >
    insert into tb_trims_usagetype_position_language (Id, UsageTypePositionRelId, PositionId,
      PositionName, PositionDescription, LanguageId, 
      BizVersionId, LangStatus, CreatedDate, 
      ModifiedDate)
    values (#{id,jdbcType=BIGINT}, #{usageTypePositionRelId,jdbcType=BIGINT}, #{positionId,jdbcType=BIGINT}, 
      #{positionName,jdbcType=VARCHAR}, #{positionDescription,jdbcType=VARCHAR}, #{languageId,jdbcType=INTEGER}, 
      #{bizVersionId,jdbcType=CHAR}, #{langStatus,jdbcType=INTEGER}, #{createdDate,jdbcType=TIMESTAMP}, 
      #{modifiedDate,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.UsageTypePositionLanguageInfoPO" >
    insert into tb_trims_usagetype_position_language
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        Id,
      </if>
      <if test="usageTypePositionRelId != null" >
        UsageTypePositionRelId,
      </if>
      <if test="positionId != null" >
        PositionId,
      </if>
      <if test="positionName != null" >
        PositionName,
      </if>
      <if test="positionDescription != null" >
        PositionDescription,
      </if>
      <if test="languageId != null" >
        LanguageId,
      </if>
      <if test="bizVersionId != null" >
        BizVersionId,
      </if>
      <if test="langStatus != null" >
        LangStatus,
      </if>
      <if test="createdDate != null" >
        CreatedDate,
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="usageTypePositionRelId != null" >
        #{usageTypePositionRelId,jdbcType=BIGINT},
      </if>
      <if test="positionId != null" >
        #{positionId,jdbcType=BIGINT},
      </if>
      <if test="positionName != null" >
        #{positionName,jdbcType=VARCHAR},
      </if>
      <if test="positionDescription != null" >
        #{positionDescription,jdbcType=VARCHAR},
      </if>
      <if test="languageId != null" >
        #{languageId,jdbcType=INTEGER},
      </if>
      <if test="bizVersionId != null" >
        #{bizVersionId,jdbcType=CHAR},
      </if>
      <if test="langStatus != null" >
        #{langStatus,jdbcType=INTEGER},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.UsageTypePositionLanguageInfoExample" resultType="java.lang.Integer" >
    select count(*) from tb_trims_usagetype_position_language
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_trims_usagetype_position_language
    <set >
      <if test="record.id != null" >
        Id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.usageTypePositionRelId != null" >
        UsageTypePositionRelId = #{record.usageTypePositionRelId,jdbcType=BIGINT},
      </if>
      <if test="record.positionId != null" >
        PositionId = #{record.positionId,jdbcType=BIGINT},
      </if>
      <if test="record.positionName != null" >
        PositionName = #{record.positionName,jdbcType=VARCHAR},
      </if>
      <if test="record.positionDescription != null" >
        PositionDescription = #{record.positionDescription,jdbcType=VARCHAR},
      </if>
      <if test="record.languageId != null" >
        LanguageId = #{record.languageId,jdbcType=INTEGER},
      </if>
      <if test="record.bizVersionId != null" >
        BizVersionId = #{record.bizVersionId,jdbcType=CHAR},
      </if>
      <if test="record.langStatus != null" >
        LangStatus = #{record.langStatus,jdbcType=INTEGER},
      </if>
      <if test="record.createdDate != null" >
        CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedDate != null" >
        ModifiedDate = #{record.modifiedDate,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_trims_usagetype_position_language
    set Id = #{record.id,jdbcType=BIGINT},
      UsageTypePositionRelId = #{record.usageTypePositionRelId,jdbcType=BIGINT},
      PositionId = #{record.positionId,jdbcType=BIGINT},
      PositionName = #{record.positionName,jdbcType=VARCHAR},
      PositionDescription = #{record.positionDescription,jdbcType=VARCHAR},
      LanguageId = #{record.languageId,jdbcType=INTEGER},
      BizVersionId = #{record.bizVersionId,jdbcType=CHAR},
      LangStatus = #{record.langStatus,jdbcType=INTEGER},
      CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      ModifiedDate = #{record.modifiedDate,jdbcType=BIGINT}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.UsageTypePositionLanguageInfoPO" >
    update tb_trims_usagetype_position_language
    <set >
      <if test="usageTypePositionRelId != null" >
        UsageTypePositionRelId = #{usageTypePositionRelId,jdbcType=BIGINT},
      </if>
      <if test="positionId != null" >
        PositionId = #{positionId,jdbcType=BIGINT},
      </if>
      <if test="positionName != null" >
        PositionName = #{positionName,jdbcType=VARCHAR},
      </if>
      <if test="positionDescription != null" >
        PositionDescription = #{positionDescription,jdbcType=VARCHAR},
      </if>
      <if test="languageId != null" >
        LanguageId = #{languageId,jdbcType=INTEGER},
      </if>
      <if test="bizVersionId != null" >
        BizVersionId = #{bizVersionId,jdbcType=CHAR},
      </if>
      <if test="langStatus != null" >
        LangStatus = #{langStatus,jdbcType=INTEGER},
      </if>
      <if test="createdDate != null" >
        CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate = #{modifiedDate,jdbcType=BIGINT},
      </if>
    </set>
    where Id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.UsageTypePositionLanguageInfoPO" >
    update tb_trims_usagetype_position_language
    set UsageTypePositionRelId = #{usageTypePositionRelId,jdbcType=BIGINT},
      PositionId = #{positionId,jdbcType=BIGINT},
      PositionName = #{positionName,jdbcType=VARCHAR},
      PositionDescription = #{positionDescription,jdbcType=VARCHAR},
      LanguageId = #{languageId,jdbcType=INTEGER},
      BizVersionId = #{bizVersionId,jdbcType=CHAR},
      LangStatus = #{langStatus,jdbcType=INTEGER},
      CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      ModifiedDate = #{modifiedDate,jdbcType=BIGINT}
    where Id = #{id,jdbcType=BIGINT}
  </update>
</mapper>