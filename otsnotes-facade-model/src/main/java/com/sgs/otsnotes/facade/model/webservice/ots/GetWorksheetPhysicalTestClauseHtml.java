/**
 * GetWorksheetPhysicalTestClauseHtml.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class GetWorksheetPhysicalTestClauseHtml  implements java.io.Serializable {
    private TemplateInstanceInfo[] worksheetInfos;

    private String errMsg;

    public GetWorksheetPhysicalTestClauseHtml() {
    }

    public GetWorksheetPhysicalTestClauseHtml(
           TemplateInstanceInfo[] worksheetInfos,
           String errMsg) {
           this.worksheetInfos = worksheetInfos;
           this.errMsg = errMsg;
    }


    /**
     * Gets the worksheetInfos value for this GetWorksheetPhysicalTestClauseHtml.
     * 
     * @return worksheetInfos
     */
    public TemplateInstanceInfo[] getWorksheetInfos() {
        return worksheetInfos;
    }


    /**
     * Sets the worksheetInfos value for this GetWorksheetPhysicalTestClauseHtml.
     * 
     * @param worksheetInfos
     */
    public void setWorksheetInfos(TemplateInstanceInfo[] worksheetInfos) {
        this.worksheetInfos = worksheetInfos;
    }


    /**
     * Gets the errMsg value for this GetWorksheetPhysicalTestClauseHtml.
     * 
     * @return errMsg
     */
    public String getErrMsg() {
        return errMsg;
    }


    /**
     * Sets the errMsg value for this GetWorksheetPhysicalTestClauseHtml.
     * 
     * @param errMsg
     */
    public void setErrMsg(String errMsg) {
        this.errMsg = errMsg;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof GetWorksheetPhysicalTestClauseHtml)) {
            return false;
        }
        GetWorksheetPhysicalTestClauseHtml other = (GetWorksheetPhysicalTestClauseHtml) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.worksheetInfos==null && other.getWorksheetInfos()==null) || 
             (this.worksheetInfos!=null &&
              java.util.Arrays.equals(this.worksheetInfos, other.getWorksheetInfos()))) &&
            ((this.errMsg==null && other.getErrMsg()==null) || 
             (this.errMsg!=null &&
              this.errMsg.equals(other.getErrMsg())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getWorksheetInfos() != null) {
            for (int i=0;
                 i<java.lang.reflect.Array.getLength(getWorksheetInfos());
                 i++) {
                Object obj = java.lang.reflect.Array.get(getWorksheetInfos(), i);
                if (obj != null &&
                    !obj.getClass().isArray()) {
                    _hashCode += obj.hashCode();
                }
            }
        }
        if (getErrMsg() != null) {
            _hashCode += getErrMsg().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(GetWorksheetPhysicalTestClauseHtml.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", ">GetWorksheetPhysicalTestClauseHtml"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("worksheetInfos");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "worksheetInfos"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "TemplateInstanceInfo"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        elemField.setItemQName(new javax.xml.namespace.QName("http://ots.sgs.com/", "TemplateInstanceInfo"));
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("errMsg");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "errMsg"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
