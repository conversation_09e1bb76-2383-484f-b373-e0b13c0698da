<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.otsnotes.dbstorages.mybatis.mapper.WorkInstructionLanguageInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.otsnotes.dbstorages.mybatis.model.WorkInstructionLanguageInfoPO" >
    <id column="Id" property="id" jdbcType="BIGINT" />
    <result column="WorkInstructionBaseId" property="workInstructionBaseId" jdbcType="BIGINT" />
    <result column="WorkInstructionId" property="workInstructionId" jdbcType="INTEGER" />
    <result column="CategoryName" property="categoryName" jdbcType="VARCHAR" />
    <result column="WorkInstructionName" property="workInstructionName" jdbcType="VARCHAR" />
    <result column="WorkInstructionText" property="workInstructionText" jdbcType="VARCHAR" />
    <result column="WorkInstructionShortDesc" property="workInstructionShortDesc" jdbcType="VARCHAR" />
    <result column="LanguageId" property="languageId" jdbcType="INTEGER" />
    <result column="BizVersionId" property="bizVersionId" jdbcType="CHAR" />
    <result column="Status" property="status" jdbcType="INTEGER" />
    <result column="CreatedDate" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="ModifiedDate" property="modifiedDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    Id, WorkInstructionBaseId, WorkInstructionId, CategoryName, WorkInstructionName, 
    WorkInstructionText, WorkInstructionShortDesc, LanguageId, BizVersionId, `Status`, 
    CreatedDate, ModifiedDate
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.WorkInstructionLanguageInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_trims_work_instruction_language
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from tb_trims_work_instruction_language
    where Id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from tb_trims_work_instruction_language
    where Id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.WorkInstructionLanguageInfoExample" >
    delete from tb_trims_work_instruction_language
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.WorkInstructionLanguageInfoPO" >
    insert into tb_trims_work_instruction_language (Id, WorkInstructionBaseId, WorkInstructionId,
      CategoryName, WorkInstructionName, WorkInstructionText, 
      WorkInstructionShortDesc, LanguageId, BizVersionId, 
      `Status`, CreatedDate, ModifiedDate
      )
    values (#{id,jdbcType=BIGINT}, #{workInstructionBaseId,jdbcType=BIGINT}, #{workInstructionId,jdbcType=INTEGER}, 
      #{categoryName,jdbcType=VARCHAR}, #{workInstructionName,jdbcType=VARCHAR}, #{workInstructionText,jdbcType=VARCHAR}, 
      #{workInstructionShortDesc,jdbcType=VARCHAR}, #{languageId,jdbcType=INTEGER}, #{bizVersionId,jdbcType=CHAR}, 
      #{status,jdbcType=INTEGER}, #{createdDate,jdbcType=TIMESTAMP}, #{modifiedDate,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.WorkInstructionLanguageInfoPO" >
    insert into tb_trims_work_instruction_language
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        Id,
      </if>
      <if test="workInstructionBaseId != null" >
        WorkInstructionBaseId,
      </if>
      <if test="workInstructionId != null" >
        WorkInstructionId,
      </if>
      <if test="categoryName != null" >
        CategoryName,
      </if>
      <if test="workInstructionName != null" >
        WorkInstructionName,
      </if>
      <if test="workInstructionText != null" >
        WorkInstructionText,
      </if>
      <if test="workInstructionShortDesc != null" >
        WorkInstructionShortDesc,
      </if>
      <if test="languageId != null" >
        LanguageId,
      </if>
      <if test="bizVersionId != null" >
        BizVersionId,
      </if>
      <if test="status != null" >
        `Status`,
      </if>
      <if test="createdDate != null" >
        CreatedDate,
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="workInstructionBaseId != null" >
        #{workInstructionBaseId,jdbcType=BIGINT},
      </if>
      <if test="workInstructionId != null" >
        #{workInstructionId,jdbcType=INTEGER},
      </if>
      <if test="categoryName != null" >
        #{categoryName,jdbcType=VARCHAR},
      </if>
      <if test="workInstructionName != null" >
        #{workInstructionName,jdbcType=VARCHAR},
      </if>
      <if test="workInstructionText != null" >
        #{workInstructionText,jdbcType=VARCHAR},
      </if>
      <if test="workInstructionShortDesc != null" >
        #{workInstructionShortDesc,jdbcType=VARCHAR},
      </if>
      <if test="languageId != null" >
        #{languageId,jdbcType=INTEGER},
      </if>
      <if test="bizVersionId != null" >
        #{bizVersionId,jdbcType=CHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.WorkInstructionLanguageInfoExample" resultType="java.lang.Integer" >
    select count(*) from tb_trims_work_instruction_language
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_trims_work_instruction_language
    <set >
      <if test="record.id != null" >
        Id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.workInstructionBaseId != null" >
        WorkInstructionBaseId = #{record.workInstructionBaseId,jdbcType=BIGINT},
      </if>
      <if test="record.workInstructionId != null" >
        WorkInstructionId = #{record.workInstructionId,jdbcType=INTEGER},
      </if>
      <if test="record.categoryName != null" >
        CategoryName = #{record.categoryName,jdbcType=VARCHAR},
      </if>
      <if test="record.workInstructionName != null" >
        WorkInstructionName = #{record.workInstructionName,jdbcType=VARCHAR},
      </if>
      <if test="record.workInstructionText != null" >
        WorkInstructionText = #{record.workInstructionText,jdbcType=VARCHAR},
      </if>
      <if test="record.workInstructionShortDesc != null" >
        WorkInstructionShortDesc = #{record.workInstructionShortDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.languageId != null" >
        LanguageId = #{record.languageId,jdbcType=INTEGER},
      </if>
      <if test="record.bizVersionId != null" >
        BizVersionId = #{record.bizVersionId,jdbcType=CHAR},
      </if>
      <if test="record.status != null" >
        `Status` = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.createdDate != null" >
        CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedDate != null" >
        ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_trims_work_instruction_language
    set Id = #{record.id,jdbcType=BIGINT},
      WorkInstructionBaseId = #{record.workInstructionBaseId,jdbcType=BIGINT},
      WorkInstructionId = #{record.workInstructionId,jdbcType=INTEGER},
      CategoryName = #{record.categoryName,jdbcType=VARCHAR},
      WorkInstructionName = #{record.workInstructionName,jdbcType=VARCHAR},
      WorkInstructionText = #{record.workInstructionText,jdbcType=VARCHAR},
      WorkInstructionShortDesc = #{record.workInstructionShortDesc,jdbcType=VARCHAR},
      LanguageId = #{record.languageId,jdbcType=INTEGER},
      BizVersionId = #{record.bizVersionId,jdbcType=CHAR},
      `Status` = #{record.status,jdbcType=INTEGER},
      CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.WorkInstructionLanguageInfoPO" >
    update tb_trims_work_instruction_language
    <set >
      <if test="workInstructionBaseId != null" >
        WorkInstructionBaseId = #{workInstructionBaseId,jdbcType=BIGINT},
      </if>
      <if test="workInstructionId != null" >
        WorkInstructionId = #{workInstructionId,jdbcType=INTEGER},
      </if>
      <if test="categoryName != null" >
        CategoryName = #{categoryName,jdbcType=VARCHAR},
      </if>
      <if test="workInstructionName != null" >
        WorkInstructionName = #{workInstructionName,jdbcType=VARCHAR},
      </if>
      <if test="workInstructionText != null" >
        WorkInstructionText = #{workInstructionText,jdbcType=VARCHAR},
      </if>
      <if test="workInstructionShortDesc != null" >
        WorkInstructionShortDesc = #{workInstructionShortDesc,jdbcType=VARCHAR},
      </if>
      <if test="languageId != null" >
        LanguageId = #{languageId,jdbcType=INTEGER},
      </if>
      <if test="bizVersionId != null" >
        BizVersionId = #{bizVersionId,jdbcType=CHAR},
      </if>
      <if test="status != null" >
        `Status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="createdDate != null" >
        CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where Id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.WorkInstructionLanguageInfoPO" >
    update tb_trims_work_instruction_language
    set WorkInstructionBaseId = #{workInstructionBaseId,jdbcType=BIGINT},
      WorkInstructionId = #{workInstructionId,jdbcType=INTEGER},
      CategoryName = #{categoryName,jdbcType=VARCHAR},
      WorkInstructionName = #{workInstructionName,jdbcType=VARCHAR},
      WorkInstructionText = #{workInstructionText,jdbcType=VARCHAR},
      WorkInstructionShortDesc = #{workInstructionShortDesc,jdbcType=VARCHAR},
      LanguageId = #{languageId,jdbcType=INTEGER},
      BizVersionId = #{bizVersionId,jdbcType=CHAR},
      `Status` = #{status,jdbcType=INTEGER},
      CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP}
    where Id = #{id,jdbcType=BIGINT}
  </update>
</mapper>