<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.otsnotes.dbstorages.mybatis.mapper.ServiceItemSampleSizeRelInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.otsnotes.dbstorages.mybatis.model.ServiceItemSampleSizeRelInfoPO" >
    <id column="Id" property="id" jdbcType="BIGINT" />
    <result column="ItemBaseId" property="itemBaseId" jdbcType="BIGINT" />
    <result column="SampleSizeId" property="sampleSizeId" jdbcType="INTEGER" />
    <result column="SampleSize" property="sampleSize" jdbcType="VARCHAR" />
    <result column="UnitId" property="unitId" jdbcType="INTEGER" />
    <result column="Remark" property="remark" jdbcType="VARCHAR" />
    <result column="BizVersionId" property="bizVersionId" jdbcType="CHAR" />
    <result column="Status" property="status" jdbcType="INTEGER" />
    <result column="CreatedDate" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="ModifiedDate" property="modifiedDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    Id, ItemBaseId, SampleSizeId, SampleSize, UnitId, Remark, BizVersionId, `Status`, 
    CreatedDate, ModifiedDate
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.ServiceItemSampleSizeRelInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_trims_serviceitem_samplesize_relationship
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from tb_trims_serviceitem_samplesize_relationship
    where Id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from tb_trims_serviceitem_samplesize_relationship
    where Id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.ServiceItemSampleSizeRelInfoExample" >
    delete from tb_trims_serviceitem_samplesize_relationship
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.ServiceItemSampleSizeRelInfoPO" >
    insert into tb_trims_serviceitem_samplesize_relationship (Id, ItemBaseId, SampleSizeId,
      SampleSize, UnitId, Remark, 
      BizVersionId, `Status`, CreatedDate, 
      ModifiedDate)
    values (#{id,jdbcType=BIGINT}, #{itemBaseId,jdbcType=BIGINT}, #{sampleSizeId,jdbcType=INTEGER}, 
      #{sampleSize,jdbcType=VARCHAR}, #{unitId,jdbcType=INTEGER}, #{remark,jdbcType=VARCHAR}, 
      #{bizVersionId,jdbcType=CHAR}, #{status,jdbcType=INTEGER}, #{createdDate,jdbcType=TIMESTAMP}, 
      #{modifiedDate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.ServiceItemSampleSizeRelInfoPO" >
    insert into tb_trims_serviceitem_samplesize_relationship
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        Id,
      </if>
      <if test="itemBaseId != null" >
        ItemBaseId,
      </if>
      <if test="sampleSizeId != null" >
        SampleSizeId,
      </if>
      <if test="sampleSize != null" >
        SampleSize,
      </if>
      <if test="unitId != null" >
        UnitId,
      </if>
      <if test="remark != null" >
        Remark,
      </if>
      <if test="bizVersionId != null" >
        BizVersionId,
      </if>
      <if test="status != null" >
        `Status`,
      </if>
      <if test="createdDate != null" >
        CreatedDate,
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="itemBaseId != null" >
        #{itemBaseId,jdbcType=BIGINT},
      </if>
      <if test="sampleSizeId != null" >
        #{sampleSizeId,jdbcType=INTEGER},
      </if>
      <if test="sampleSize != null" >
        #{sampleSize,jdbcType=VARCHAR},
      </if>
      <if test="unitId != null" >
        #{unitId,jdbcType=INTEGER},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="bizVersionId != null" >
        #{bizVersionId,jdbcType=CHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.ServiceItemSampleSizeRelInfoExample" resultType="java.lang.Integer" >
    select count(*) from tb_trims_serviceitem_samplesize_relationship
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_trims_serviceitem_samplesize_relationship
    <set >
      <if test="record.id != null" >
        Id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.itemBaseId != null" >
        ItemBaseId = #{record.itemBaseId,jdbcType=BIGINT},
      </if>
      <if test="record.sampleSizeId != null" >
        SampleSizeId = #{record.sampleSizeId,jdbcType=INTEGER},
      </if>
      <if test="record.sampleSize != null" >
        SampleSize = #{record.sampleSize,jdbcType=VARCHAR},
      </if>
      <if test="record.unitId != null" >
        UnitId = #{record.unitId,jdbcType=INTEGER},
      </if>
      <if test="record.remark != null" >
        Remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.bizVersionId != null" >
        BizVersionId = #{record.bizVersionId,jdbcType=CHAR},
      </if>
      <if test="record.status != null" >
        `Status` = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.createdDate != null" >
        CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedDate != null" >
        ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_trims_serviceitem_samplesize_relationship
    set Id = #{record.id,jdbcType=BIGINT},
      ItemBaseId = #{record.itemBaseId,jdbcType=BIGINT},
      SampleSizeId = #{record.sampleSizeId,jdbcType=INTEGER},
      SampleSize = #{record.sampleSize,jdbcType=VARCHAR},
      UnitId = #{record.unitId,jdbcType=INTEGER},
      Remark = #{record.remark,jdbcType=VARCHAR},
      BizVersionId = #{record.bizVersionId,jdbcType=CHAR},
      `Status` = #{record.status,jdbcType=INTEGER},
      CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.ServiceItemSampleSizeRelInfoPO" >
    update tb_trims_serviceitem_samplesize_relationship
    <set >
      <if test="itemBaseId != null" >
        ItemBaseId = #{itemBaseId,jdbcType=BIGINT},
      </if>
      <if test="sampleSizeId != null" >
        SampleSizeId = #{sampleSizeId,jdbcType=INTEGER},
      </if>
      <if test="sampleSize != null" >
        SampleSize = #{sampleSize,jdbcType=VARCHAR},
      </if>
      <if test="unitId != null" >
        UnitId = #{unitId,jdbcType=INTEGER},
      </if>
      <if test="remark != null" >
        Remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="bizVersionId != null" >
        BizVersionId = #{bizVersionId,jdbcType=CHAR},
      </if>
      <if test="status != null" >
        `Status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="createdDate != null" >
        CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where Id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.ServiceItemSampleSizeRelInfoPO" >
    update tb_trims_serviceitem_samplesize_relationship
    set ItemBaseId = #{itemBaseId,jdbcType=BIGINT},
      SampleSizeId = #{sampleSizeId,jdbcType=INTEGER},
      SampleSize = #{sampleSize,jdbcType=VARCHAR},
      UnitId = #{unitId,jdbcType=INTEGER},
      Remark = #{remark,jdbcType=VARCHAR},
      BizVersionId = #{bizVersionId,jdbcType=CHAR},
      `Status` = #{status,jdbcType=INTEGER},
      CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP}
    where Id = #{id,jdbcType=BIGINT}
  </update>
</mapper>