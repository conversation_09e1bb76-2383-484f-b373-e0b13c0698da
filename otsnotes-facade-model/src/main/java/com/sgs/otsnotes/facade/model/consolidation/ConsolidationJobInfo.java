package com.sgs.otsnotes.facade.model.consolidation;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

/**
 * @ClassName ConsolidationJobInfo
 * @Description TODO
 * <AUTHOR>
 * @Date 7/23/2020
 */
public class ConsolidationJobInfo extends PrintFriendliness {
    private String  jobNo;
    private String  orderNo;
    private Integer  jobStatus;
    private String  labSectionCode;
    private String  labSectionId;
    private String  labSectionBaseId;

    public String getJobNo() {
        return jobNo;
    }

    public void setJobNo(String jobNo) {
        this.jobNo = jobNo;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getJobStatus() {
        return jobStatus;
    }

    public void setJobStatus(Integer jobStatus) {
        this.jobStatus = jobStatus;
    }

    public String getLabSectionCode() {
        return labSectionCode;
    }

    public void setLabSectionCode(String labSectionCode) {
        this.labSectionCode = labSectionCode;
    }

    public String getLabSectionId() {
        return labSectionId;
    }

    public void setLabSectionId(String labSectionId) {
        this.labSectionId = labSectionId;
    }

    public String getLabSectionBaseId() {
        return labSectionBaseId;
    }

    public void setLabSectionBaseId(String labSectionBaseId) {
        this.labSectionBaseId = labSectionBaseId;
    }
}
