# OTSNotes服务 Wiki 目录索引

本Wiki包含 **398** 个文档文件，总大小 **5451.5KB**

## 📚 文档目录

- [安全设计](./content/安全设计.md)
- [开发指南](./content/开发指南.md)
- [技术栈与依赖](./content/技术栈与依赖.md)
- [数据录入XML映射](./content/数据录入XML映射.md)
- [数据录入服务](./content/数据录入服务.md)
- [部署与运维](./content/部署与运维.md)
- **API接口参考**
  - **分包API**
    - [同步分包数据](./content/API接口参考/分包API/同步分包数据.md)
    - [更新分包状态](./content/API接口参考/分包API/更新分包状态.md)
    - [查询分包状态](./content/API接口参考/分包API/查询分包状态.md)
  - **报告API**
    - [报告回传校验API](./content/API接口参考/报告API/报告回传校验API.md)
    - [报告审批API](./content/API接口参考/报告API/报告审批API.md)
    - [报告归档API](./content/API接口参考/报告API/报告归档API.md)
    - [报告状态查询API](./content/API接口参考/报告API/报告状态查询API.md)
  - **测试项API**
    - [测试项复杂操作API](./content/API接口参考/测试项API/测试项复杂操作API.md)
    - [测试项更新API](./content/API接口参考/测试项API/测试项更新API.md)
    - [测试项查询API](./content/API接口参考/测试项API/测试项查询API.md)
  - **订单API**
    - [修改订单](./content/API接口参考/订单API/修改订单.md)
    - [查询订单](./content/API接口参考/订单API/查询订单.md)
    - [订单状态管理](./content/API接口参考/订单API/订单状态管理.md)
    - [订单集成接口](./content/API接口参考/订单API/订单集成接口.md)
- **业务逻辑层**
  - **分包服务**
    - **分包创建与配置**
      - [分包创建与配置](./content/业务逻辑层/分包服务/分包创建与配置/分包创建与配置.md)
      - [分包创建流程](./content/业务逻辑层/分包服务/分包创建与配置/分包创建流程.md)
      - [分包模板管理](./content/业务逻辑层/分包服务/分包创建与配置/分包模板管理.md)
      - [分包规则配置](./content/业务逻辑层/分包服务/分包创建与配置/分包规则配置.md)
    - **分包数据同步**
      - [分包数据同步](./content/业务逻辑层/分包服务/分包数据同步/分包数据同步.md)
      - [同步协议与数据格式](./content/业务逻辑层/分包服务/分包数据同步/同步协议与数据格式.md)
      - [同步状态管理](./content/业务逻辑层/分包服务/分包数据同步/同步状态管理.md)
      - [错误处理与重试机制](./content/业务逻辑层/分包服务/分包数据同步/错误处理与重试机制.md)
    - **分包结果处理**
      - [分包结果处理](./content/业务逻辑层/分包服务/分包结果处理/分包结果处理.md)
      - **异常处理**
        - [异常处理](./content/业务逻辑层/分包服务/分包结果处理/异常处理/异常处理.md)
        - [异常检测](./content/业务逻辑层/分包服务/分包结果处理/异常处理/异常检测.md)
        - [监控告警](./content/业务逻辑层/分包服务/分包结果处理/异常处理/监控告警.md)
        - [补偿机制](./content/业务逻辑层/分包服务/分包结果处理/异常处理/补偿机制.md)
      - **数据映射**
        - [单位转换](./content/业务逻辑层/分包服务/分包结果处理/数据映射/单位转换.md)
        - [数据映射](./content/业务逻辑层/分包服务/分包结果处理/数据映射/数据映射.md)
        - [测试项映射](./content/业务逻辑层/分包服务/分包结果处理/数据映射/测试项映射.md)
        - [结果合并](./content/业务逻辑层/分包服务/分包结果处理/数据映射/结果合并.md)
      - **状态更新**
        - [事件驱动](./content/业务逻辑层/分包服务/分包结果处理/状态更新/事件驱动.md)
        - [事务管理](./content/业务逻辑层/分包服务/分包结果处理/状态更新/事务管理.md)
        - [状态更新](./content/业务逻辑层/分包服务/分包结果处理/状态更新/状态更新.md)
        - [状态流转](./content/业务逻辑层/分包服务/分包结果处理/状态更新/状态流转.md)
      - **结果解析**
        - [字段映射](./content/业务逻辑层/分包服务/分包结果处理/结果解析/字段映射.md)
        - [异常处理](./content/业务逻辑层/分包服务/分包结果处理/结果解析/异常处理.md)
        - [数据格式解析](./content/业务逻辑层/分包服务/分包结果处理/结果解析/数据格式解析.md)
        - [结果解析](./content/业务逻辑层/分包服务/分包结果处理/结果解析/结果解析.md)
  - **报告服务**
    - **报告分发与归档**
      - [分发渠道管理](./content/业务逻辑层/报告服务/报告分发与归档/分发渠道管理.md)
      - [外部系统集成](./content/业务逻辑层/报告服务/报告分发与归档/外部系统集成.md)
      - [归档策略](./content/业务逻辑层/报告服务/报告分发与归档/归档策略.md)
      - [报告分发与归档](./content/业务逻辑层/报告服务/报告分发与归档/报告分发与归档.md)
    - **报告审批流程**
      - [审批规则配置](./content/业务逻辑层/报告服务/报告审批流程/审批规则配置.md)
      - [报告状态机管理](./content/业务逻辑层/报告服务/报告审批流程/报告状态机管理.md)
      - [跨系统审批同步](./content/业务逻辑层/报告服务/报告审批流程/跨系统审批同步.md)
    - **报告生成与模板**
      - [报告生成与模板](./content/业务逻辑层/报告服务/报告生成与模板/报告生成与模板.md)
      - [数据填充引擎](./content/业务逻辑层/报告服务/报告生成与模板/数据填充引擎.md)
      - [文件格式转换](./content/业务逻辑层/报告服务/报告生成与模板/文件格式转换.md)
      - [模板管理](./content/业务逻辑层/报告服务/报告生成与模板/模板管理.md)
  - **订单服务**
    - **订单创建与修改**
      - [订单修改机制](./content/业务逻辑层/订单服务/订单创建与修改/订单修改机制.md)
      - [订单创建与修改](./content/业务逻辑层/订单服务/订单创建与修改/订单创建与修改.md)
      - [订单创建流程](./content/业务逻辑层/订单服务/订单创建与修改/订单创建流程.md)
      - [订单复制功能](./content/业务逻辑层/订单服务/订单创建与修改/订单复制功能.md)
    - **订单查询与检索**
      - [分页与排序](./content/业务逻辑层/订单服务/订单查询与检索/分页与排序.md)
      - [查询性能优化](./content/业务逻辑层/订单服务/订单查询与检索/查询性能优化.md)
      - [查询条件处理](./content/业务逻辑层/订单服务/订单查询与检索/查询条件处理.md)
      - [订单查询与检索](./content/业务逻辑层/订单服务/订单查询与检索/订单查询与检索.md)
    - **订单状态管理**
      - [订单状态事件处理](./content/业务逻辑层/订单服务/订单状态管理/订单状态事件处理.md)
      - [订单状态定义](./content/业务逻辑层/订单服务/订单状态管理/订单状态定义.md)
      - [订单状态管理](./content/业务逻辑层/订单服务/订单状态管理/订单状态管理.md)
      - [订单状态转换规则](./content/业务逻辑层/订单服务/订单状态管理/订单状态转换规则.md)
- **基础设施**
  - [基础设施](./content/基础设施/基础设施.md)
  - [线程池管理](./content/基础设施/线程池管理.md)
  - **消息队列**
    - [Kafka生产者](./content/基础设施/消息队列/Kafka生产者.md)
    - [消息队列](./content/基础设施/消息队列/消息队列.md)
    - **Kafka消费者**
      - [Kafka消费者](./content/基础设施/消息队列/Kafka消费者/Kafka消费者.md)
      - **Kafka延迟消息**
        - [Kafka延迟消息](./content/基础设施/消息队列/Kafka消费者/Kafka延迟消息/Kafka延迟消息.md)
        - [使用实践](./content/基础设施/消息队列/Kafka消费者/Kafka延迟消息/使用实践.md)
        - [延迟消息原理](./content/基础设施/消息队列/Kafka消费者/Kafka延迟消息/延迟消息原理.md)
        - [核心组件解析](./content/基础设施/消息队列/Kafka消费者/Kafka延迟消息/核心组件解析.md)
      - **Kafka消费者基础**
        - [Kafka消费者基础](./content/基础设施/消息队列/Kafka消费者/Kafka消费者基础/Kafka消费者基础.md)
        - [消息处理流程](./content/基础设施/消息队列/Kafka消费者/Kafka消费者基础/消息处理流程.md)
        - [消费者配置管理](./content/基础设施/消息队列/Kafka消费者/Kafka消费者基础/消费者配置管理.md)
        - [错误处理机制](./content/基础设施/消息队列/Kafka消费者/Kafka消费者基础/错误处理机制.md)
  - **缓存管理**
    - [本地缓存](./content/基础设施/缓存管理/本地缓存.md)
    - [缓存管理](./content/基础设施/缓存管理/缓存管理.md)
    - **Redis缓存**
      - [Redis缓存](./content/基础设施/缓存管理/Redis缓存/Redis缓存.md)
      - **Redis使用实践**
        - [Redis使用实践](./content/基础设施/缓存管理/Redis缓存/Redis使用实践/Redis使用实践.md)
        - [分布式锁实现](./content/基础设施/缓存管理/Redis缓存/Redis使用实践/分布式锁实现.md)
        - [缓存操作实践](./content/基础设施/缓存管理/Redis缓存/Redis使用实践/缓存操作实践.md)
      - **Redis配置管理**
        - [Redission客户端配置](./content/基础设施/缓存管理/Redis缓存/Redis配置管理/Redission客户端配置.md)
        - [Redis部署模式配置](./content/基础设施/缓存管理/Redis缓存/Redis配置管理/Redis部署模式配置.md)
        - [Redis配置管理](./content/基础设施/缓存管理/Redis缓存/Redis配置管理/Redis配置管理.md)
  - **配置管理**
    - [配置管理](./content/基础设施/配置管理/配置管理.md)
    - **核心配置**
      - [事件总线配置](./content/基础设施/配置管理/核心配置/事件总线配置.md)
      - [定时任务配置](./content/基础设施/配置管理/核心配置/定时任务配置.md)
      - [核心配置](./content/基础设施/配置管理/核心配置/核心配置.md)
      - [线程池配置](./content/基础设施/配置管理/核心配置/线程池配置.md)
      - [缓存配置](./content/基础设施/配置管理/核心配置/缓存配置.md)
    - **环境配置**
      - [多环境管理策略](./content/基础设施/配置管理/环境配置/多环境管理策略.md)
      - [环境配置](./content/基础设施/配置管理/环境配置/环境配置.md)
      - [配置加载机制](./content/基础设施/配置管理/环境配置/配置加载机制.md)
    - **第三方服务配置**
      - [Kafka配置](./content/基础设施/配置管理/第三方服务配置/Kafka配置.md)
      - [StarLims集成配置](./content/基础设施/配置管理/第三方服务配置/StarLims集成配置.md)
      - [分包服务配置](./content/基础设施/配置管理/第三方服务配置/分包服务配置.md)
      - [第三方服务配置](./content/基础设施/配置管理/第三方服务配置/第三方服务配置.md)
- **外部系统集成**
  - [外部系统集成](./content/外部系统集成/外部系统集成.md)
  - **GPO集成**
    - [GPO集成](./content/外部系统集成/GPO集成/GPO集成.md)
    - **GPO业务流程处理**
      - [GPO业务流程处理](./content/外部系统集成/GPO集成/GPO业务流程处理/GPO业务流程处理.md)
      - [主机订单构建流程](./content/外部系统集成/GPO集成/GPO业务流程处理/主机订单构建流程.md)
      - [客户买家信息构建流程](./content/外部系统集成/GPO集成/GPO业务流程处理/客户买家信息构建流程.md)
      - [客户申请人构建流程](./content/外部系统集成/GPO集成/GPO业务流程处理/客户申请人构建流程.md)
    - **GPO状态同步**
      - [GPO状态同步](./content/外部系统集成/GPO集成/GPO状态同步/GPO状态同步.md)
      - [分包状态同步](./content/外部系统集成/GPO集成/GPO状态同步/分包状态同步.md)
      - [同步机制与策略](./content/外部系统集成/GPO集成/GPO状态同步/同步机制与策略.md)
      - [报告状态同步](./content/外部系统集成/GPO集成/GPO状态同步/报告状态同步.md)
      - [订单状态同步](./content/外部系统集成/GPO集成/GPO状态同步/订单状态同步.md)
    - **GPO订单集成**
      - [GPO订单创建](./content/外部系统集成/GPO集成/GPO订单集成/GPO订单创建.md)
      - [GPO订单更新](./content/外部系统集成/GPO集成/GPO订单集成/GPO订单更新.md)
      - [GPO订单查询](./content/外部系统集成/GPO集成/GPO订单集成/GPO订单查询.md)
      - [GPO订单错误处理](./content/外部系统集成/GPO集成/GPO订单集成/GPO订单错误处理.md)
      - [GPO订单集成](./content/外部系统集成/GPO集成/GPO订单集成/GPO订单集成.md)
  - **PreOrder集成**
    - [PreOrder客户端](./content/外部系统集成/PreOrder集成/PreOrder客户端.md)
    - [PreOrder消息消费者](./content/外部系统集成/PreOrder集成/PreOrder消息消费者.md)
    - [PreOrder集成](./content/外部系统集成/PreOrder集成/PreOrder集成.md)
  - **SLIM集成**
    - [SLIM配置集成](./content/外部系统集成/SLIM集成/SLIM配置集成.md)
    - [SLIM集成](./content/外部系统集成/SLIM集成/SLIM集成.md)
    - [SLIM集成错误处理](./content/外部系统集成/SLIM集成/SLIM集成错误处理.md)
    - **SLIM数据同步**
      - [SLIM数据同步](./content/外部系统集成/SLIM集成/SLIM数据同步/SLIM数据同步.md)
      - [样品同步](./content/外部系统集成/SLIM集成/SLIM数据同步/样品同步.md)
      - [测试线同步](./content/外部系统集成/SLIM集成/SLIM数据同步/测试线同步.md)
      - [状态同步](./content/外部系统集成/SLIM集成/SLIM数据同步/状态同步.md)
  - **StarLims集成**
    - [StarLims客户端调用](./content/外部系统集成/StarLims集成/StarLims客户端调用.md)
    - [StarLims消息集成](./content/外部系统集成/StarLims集成/StarLims消息集成.md)
    - [StarLims集成](./content/外部系统集成/StarLims集成/StarLims集成.md)
  - **TRIMSLocal集成**
    - [TRIMSLocal集成](./content/外部系统集成/TRIMSLocal集成/TRIMSLocal集成.md)
    - **数据同步**
      - [冲突解决](./content/外部系统集成/TRIMSLocal集成/数据同步/冲突解决.md)
      - [增量同步](./content/外部系统集成/TRIMSLocal集成/数据同步/增量同步.md)
      - [批量同步](./content/外部系统集成/TRIMSLocal集成/数据同步/批量同步.md)
      - [数据同步](./content/外部系统集成/TRIMSLocal集成/数据同步/数据同步.md)
    - **状态管理**
      - [报告状态同步](./content/外部系统集成/TRIMSLocal集成/状态管理/报告状态同步.md)
      - [测试线状态同步](./content/外部系统集成/TRIMSLocal集成/状态管理/测试线状态同步.md)
      - [状态管理](./content/外部系统集成/TRIMSLocal集成/状态管理/状态管理.md)
      - [订单状态同步](./content/外部系统集成/TRIMSLocal集成/状态管理/订单状态同步.md)
    - **通用工具支持**
      - [HTTP调用工具](./content/外部系统集成/TRIMSLocal集成/通用工具支持/HTTP调用工具.md)
      - [数据转换工具](./content/外部系统集成/TRIMSLocal集成/通用工具支持/数据转换工具.md)
      - [通用工具支持](./content/外部系统集成/TRIMSLocal集成/通用工具支持/通用工具支持.md)
      - [配置管理](./content/外部系统集成/TRIMSLocal集成/通用工具支持/配置管理.md)
- **外部集成**
  - **Kafka消息集成**
    - [延迟消息处理机制](./content/外部集成/Kafka消息集成/延迟消息处理机制.md)
    - [普通消息生产与消费](./content/外部集成/Kafka消息集成/普通消息生产与消费.md)
- **性能优化**
  - [异步任务](./content/性能优化/异步任务.md)
  - [性能优化](./content/性能优化/性能优化.md)
  - [批量处理](./content/性能优化/批量处理.md)
  - **数据库优化**
    - [数据库优化](./content/性能优化/数据库优化/数据库优化.md)
    - **SQL优化**
      - [SQL优化](./content/性能优化/数据库优化/SQL优化/SQL优化.md)
      - [执行计划分析](./content/性能优化/数据库优化/SQL优化/执行计划分析.md)
      - [索引优化策略](./content/性能优化/数据库优化/SQL优化/索引优化策略.md)
    - **数据源管理**
      - [数据源管理](./content/性能优化/数据库优化/数据源管理/数据源管理.md)
      - [数据源路由策略](./content/性能优化/数据库优化/数据源管理/数据源路由策略.md)
      - [读写分离实现](./content/性能优化/数据库优化/数据源管理/读写分离实现.md)
  - **缓存优化**
    - [Guava本地缓存](./content/性能优化/缓存优化/Guava本地缓存.md)
    - [Redis分布式缓存](./content/性能优化/缓存优化/Redis分布式缓存.md)
    - [缓存优化](./content/性能优化/缓存优化/缓存优化.md)
- **接口定义与实现**
  - [接口定义与实现](./content/接口定义与实现/接口定义与实现.md)
  - **接口实现**
    - [接口实现](./content/接口定义与实现/接口实现/接口实现.md)
    - **实现机制**
      - [事务管理](./content/接口定义与实现/接口实现/实现机制/事务管理.md)
      - [实现机制](./content/接口定义与实现/接口实现/实现机制/实现机制.md)
      - [异常处理](./content/接口定义与实现/接口实现/实现机制/异常处理.md)
      - [注解机制](./content/接口定义与实现/接口实现/实现机制/注解机制.md)
    - **调用流程**
      - [数据转换机制](./content/接口定义与实现/接口实现/调用流程/数据转换机制.md)
      - [服务委托机制](./content/接口定义与实现/接口实现/调用流程/服务委托机制.md)
      - [调用流程](./content/接口定义与实现/接口实现/调用流程/调用流程.md)
      - [跨服务协调](./content/接口定义与实现/接口实现/调用流程/跨服务协调.md)
  - **接口规范**
    - [接口规范](./content/接口定义与实现/接口规范/接口规范.md)
    - **接口定义**
      - [任务管理接口](./content/接口定义与实现/接口规范/接口定义/任务管理接口.md)
      - [分包管理接口](./content/接口定义与实现/接口规范/接口定义/分包管理接口.md)
      - [报告管理接口](./content/接口定义与实现/接口规范/接口定义/报告管理接口.md)
      - [接口定义](./content/接口定义与实现/接口规范/接口定义/接口定义.md)
      - [数据录入接口](./content/接口定义与实现/接口规范/接口定义/数据录入接口.md)
      - [测试线管理接口](./content/接口定义与实现/接口规范/接口定义/测试线管理接口.md)
      - [矩阵复制接口](./content/接口定义与实现/接口规范/接口定义/矩阵复制接口.md)
      - [结论管理接口](./content/接口定义与实现/接口规范/接口定义/结论管理接口.md)
      - [订单管理接口](./content/接口定义与实现/接口规范/接口定义/订单管理接口.md)
    - **数据传输对象**
      - [TRIMS集成数据传输对象](./content/接口定义与实现/接口规范/数据传输对象/TRIMS集成数据传输对象.md)
      - [分包数据传输对象](./content/接口定义与实现/接口规范/数据传输对象/分包数据传输对象.md)
      - [分析物数据传输对象](./content/接口定义与实现/接口规范/数据传输对象/分析物数据传输对象.md)
      - [数据传输对象](./content/接口定义与实现/接口规范/数据传输对象/数据传输对象.md)
      - [数据录入数据传输对象](./content/接口定义与实现/接口规范/数据传输对象/数据录入数据传输对象.md)
      - [核心业务实体数据传输对象](./content/接口定义与实现/接口规范/数据传输对象/核心业务实体数据传输对象.md)
      - [测试数据传输对象](./content/接口定义与实现/接口规范/数据传输对象/测试数据传输对象.md)
      - [结论数据传输对象](./content/接口定义与实现/接口规范/数据传输对象/结论数据传输对象.md)
    - **枚举契约**
      - [操作枚举](./content/接口定义与实现/接口规范/枚举契约/操作枚举.md)
      - [枚举契约](./content/接口定义与实现/接口规范/枚举契约/枚举契约.md)
      - **业务响应枚举**
        - [业务代码枚举](./content/接口定义与实现/接口规范/枚举契约/业务响应枚举/业务代码枚举.md)
        - [业务响应枚举](./content/接口定义与实现/接口规范/枚举契约/业务响应枚举/业务响应枚举.md)
        - [响应代码枚举](./content/接口定义与实现/接口规范/枚举契约/业务响应枚举/响应代码枚举.md)
      - **状态枚举**
        - [任务状态](./content/接口定义与实现/接口规范/枚举契约/状态枚举/任务状态.md)
        - [报告状态](./content/接口定义与实现/接口规范/枚举契约/状态枚举/报告状态.md)
        - [测试线状态](./content/接口定义与实现/接口规范/枚举契约/状态枚举/测试线状态.md)
        - [状态枚举](./content/接口定义与实现/接口规范/枚举契约/状态枚举/状态枚举.md)
        - [订单状态](./content/接口定义与实现/接口规范/枚举契约/状态枚举/订单状态.md)
- **数据模型与ORM映射**
  - [数据模型与ORM映射](./content/数据模型与ORM映射/数据模型与ORM映射.md)
  - **MyBatis映射实现**
    - [MyBatis映射实现](./content/数据模型与ORM映射/MyBatis映射实现/MyBatis映射实现.md)
    - **XML映射文件**
      - [XML映射文件](./content/数据模型与ORM映射/MyBatis映射实现/XML映射文件/XML映射文件.md)
      - [分包XML映射](./content/数据模型与ORM映射/MyBatis映射实现/XML映射文件/分包XML映射.md)
      - [报告XML映射](./content/数据模型与ORM映射/MyBatis映射实现/XML映射文件/报告XML映射.md)
      - [订单XML映射](./content/数据模型与ORM映射/MyBatis映射实现/XML映射文件/订单XML映射.md)
    - **核心Mapper接口**
      - [分包Mapper接口](./content/数据模型与ORM映射/MyBatis映射实现/核心Mapper接口/分包Mapper接口.md)
      - [报告Mapper接口](./content/数据模型与ORM映射/MyBatis映射实现/核心Mapper接口/报告Mapper接口.md)
      - [样品Mapper接口](./content/数据模型与ORM映射/MyBatis映射实现/核心Mapper接口/样品Mapper接口.md)
      - [核心Mapper接口](./content/数据模型与ORM映射/MyBatis映射实现/核心Mapper接口/核心Mapper接口.md)
      - [测试线Mapper接口](./content/数据模型与ORM映射/MyBatis映射实现/核心Mapper接口/测试线Mapper接口.md)
      - [结论Mapper接口](./content/数据模型与ORM映射/MyBatis映射实现/核心Mapper接口/结论Mapper接口.md)
      - [订单Mapper接口](./content/数据模型与ORM映射/MyBatis映射实现/核心Mapper接口/订单Mapper接口.md)
  - **分包数据模型**
    - [分包测试数据模型](./content/数据模型与ORM映射/分包数据模型/分包测试数据模型.md)
    - [分包需求模型](./content/数据模型与ORM映射/分包数据模型/分包需求模型.md)
  - **报告数据模型**
    - [报告文件模型](./content/数据模型与ORM映射/报告数据模型/报告文件模型.md)
    - [报告模板模型](./content/数据模型与ORM映射/报告数据模型/报告模板模型.md)
    - [报告签名模型](./content/数据模型与ORM映射/报告数据模型/报告签名模型.md)
  - **核心实体模型**
    - [任务实体(Job)](./content/数据模型与ORM映射/核心实体模型/任务实体(Job).md)
    - [分包单实体(SubContract)](./content/数据模型与ORM映射/核心实体模型/分包单实体(SubContract).md)
    - [报告实体(Report)](./content/数据模型与ORM映射/核心实体模型/报告实体(Report).md)
    - [样品实体(Sample)](./content/数据模型与ORM映射/核心实体模型/样品实体(Sample).md)
    - [核心实体模型](./content/数据模型与ORM映射/核心实体模型/核心实体模型.md)
    - [测试线实体(TestLine)](./content/数据模型与ORM映射/核心实体模型/测试线实体(TestLine).md)
    - [结论实体(Conclusion)](./content/数据模型与ORM映射/核心实体模型/结论实体(Conclusion).md)
    - [订单实体(Order)](./content/数据模型与ORM映射/核心实体模型/订单实体(Order).md)
  - **测试项数据模型**
    - [测试项主表模型](./content/数据模型与ORM映射/测试项数据模型/测试项主表模型.md)
    - [测试项分析物模型](./content/数据模型与ORM映射/测试项数据模型/测试项分析物模型.md)
    - [测试项实例模型](./content/数据模型与ORM映射/测试项数据模型/测试项实例模型.md)
    - [测试项条件模型](./content/数据模型与ORM映射/测试项数据模型/测试项条件模型.md)
    - [测试项限制模型](./content/数据模型与ORM映射/测试项数据模型/测试项限制模型.md)
  - **订单数据模型**
    - [订单主表模型](./content/数据模型与ORM映射/订单数据模型/订单主表模型.md)
    - [订单分包模型](./content/数据模型与ORM映射/订单数据模型/订单分包模型.md)
    - [订单实例模型](./content/数据模型与ORM映射/订单数据模型/订单实例模型.md)
    - [订单引用模型](./content/数据模型与ORM映射/订单数据模型/订单引用模型.md)
    - [订单语言模型](./content/数据模型与ORM映射/订单数据模型/订单语言模型.md)
- **核心业务功能**
  - [核心业务功能](./content/核心业务功能/核心业务功能.md)
  - **分包管理**
    - [分包创建](./content/核心业务功能/分包管理/分包创建.md)
    - [分包状态管理](./content/核心业务功能/分包管理/分包状态管理.md)
    - [分包管理](./content/核心业务功能/分包管理/分包管理.md)
    - [外部系统同步](./content/核心业务功能/分包管理/外部系统同步.md)
  - **报告管理**
    - [报告文件管理](./content/核心业务功能/报告管理/报告文件管理.md)
    - [报告状态管理](./content/核心业务功能/报告管理/报告状态管理.md)
    - [报告生成](./content/核心业务功能/报告管理/报告生成.md)
    - [报告管理](./content/核心业务功能/报告管理/报告管理.md)
  - **样品管理**
    - [样品信息管理](./content/核心业务功能/样品管理/样品信息管理.md)
    - [样品分组管理](./content/核心业务功能/样品管理/样品分组管理.md)
    - [样品管理](./content/核心业务功能/样品管理/样品管理.md)
    - [混合样品处理](./content/核心业务功能/样品管理/混合样品处理.md)
  - **测试线管理**
    - [测试线创建](./content/核心业务功能/测试线管理/测试线创建.md)
    - [测试线数据同步](./content/核心业务功能/测试线管理/测试线数据同步.md)
    - [测试线状态管理](./content/核心业务功能/测试线管理/测试线状态管理.md)
    - [测试线管理](./content/核心业务功能/测试线管理/测试线管理.md)
  - **结论管理**
    - [数据录入管理](./content/核心业务功能/结论管理/数据录入管理.md)
    - [结论生成](./content/核心业务功能/结论管理/结论生成.md)
    - [结论管理](./content/核心业务功能/结论管理/结论管理.md)
    - [结论计算](./content/核心业务功能/结论管理/结论计算.md)
  - **订单管理**
    - [订单创建](./content/核心业务功能/订单管理/订单创建.md)
    - [订单取消](./content/核心业务功能/订单管理/订单取消.md)
    - [订单同步](./content/核心业务功能/订单管理/订单同步.md)
    - [订单查询](./content/核心业务功能/订单管理/订单查询.md)
    - [订单管理](./content/核心业务功能/订单管理/订单管理.md)
- **模块架构**
  - [模块架构](./content/模块架构/模块架构.md)
  - **Web表现层**
    - [Web控制器](./content/模块架构/Web表现层/Web控制器.md)
    - [Web表现层](./content/模块架构/Web表现层/Web表现层.md)
    - [应用配置](./content/模块架构/Web表现层/应用配置.md)
    - [资源管理](./content/模块架构/Web表现层/资源管理.md)
  - **基础设施层**
    - [基础设施层](./content/模块架构/基础设施层/基础设施层.md)
    - [线程池管理](./content/模块架构/基础设施层/线程池管理.md)
    - **外部系统集成**
      - [外部系统集成](./content/模块架构/基础设施层/外部系统集成/外部系统集成.md)
      - **安全认证**
        - [令牌管理机制](./content/模块架构/基础设施层/外部系统集成/安全认证/令牌管理机制.md)
        - [安全认证](./content/模块架构/基础设施层/外部系统集成/安全认证/安全认证.md)
        - [认证协议集成](./content/模块架构/基础设施层/外部系统集成/安全认证/认证协议集成.md)
        - [配置安全管理](./content/模块架构/基础设施层/外部系统集成/安全认证/配置安全管理.md)
      - **异步回调与事件驱动**
        - [事件发布机制](./content/模块架构/基础设施层/外部系统集成/异步回调与事件驱动/事件发布机制.md)
        - [幂等性与一致性保障](./content/模块架构/基础设施层/外部系统集成/异步回调与事件驱动/幂等性与一致性保障.md)
        - [异步回调与事件驱动](./content/模块架构/基础设施层/外部系统集成/异步回调与事件驱动/异步回调与事件驱动.md)
        - [异步回调机制详解](./content/模块架构/基础设施层/外部系统集成/异步回调与事件驱动/异步回调机制详解.md)
        - [监控与追踪](./content/模块架构/基础设施层/外部系统集成/异步回调与事件驱动/监控与追踪.md)
      - **数据转换与映射**
        - [数据格式适配与协议转换](./content/模块架构/基础设施层/外部系统集成/数据转换与映射/数据格式适配与协议转换.md)
        - [数据转换与映射](./content/模块架构/基础设施层/外部系统集成/数据转换与映射/数据转换与映射.md)
        - [模型转换与领域映射](./content/模块架构/基础设施层/外部系统集成/数据转换与映射/模型转换与领域映射.md)
        - [转换性能优化与缓存策略](./content/模块架构/基础设施层/外部系统集成/数据转换与映射/转换性能优化与缓存策略.md)
      - **通信机制**
        - [异步处理](./content/模块架构/基础设施层/外部系统集成/通信机制/异步处理.md)
        - [数据序列化](./content/模块架构/基础设施层/外部系统集成/通信机制/数据序列化.md)
        - [连接管理](./content/模块架构/基础设施层/外部系统集成/通信机制/连接管理.md)
        - [通信机制](./content/模块架构/基础设施层/外部系统集成/通信机制/通信机制.md)
        - [错误处理](./content/模块架构/基础设施层/外部系统集成/通信机制/错误处理.md)
      - **错误处理与容错**
        - [异常分类与捕获](./content/模块架构/基础设施层/外部系统集成/错误处理与容错/异常分类与捕获.md)
        - [熔断与降级机制](./content/模块架构/基础设施层/外部系统集成/错误处理与容错/熔断与降级机制.md)
        - [重试机制与策略](./content/模块架构/基础设施层/外部系统集成/错误处理与容错/重试机制与策略.md)
        - [错误处理与容错](./content/模块架构/基础设施层/外部系统集成/错误处理与容错/错误处理与容错.md)
        - [错误码映射与用户友好提示](./content/模块架构/基础设施层/外部系统集成/错误处理与容错/错误码映射与用户友好提示.md)
    - **消息队列系统**
      - [Kafka消费者](./content/模块架构/基础设施层/消息队列系统/Kafka消费者.md)
      - [Kafka生产者](./content/模块架构/基础设施层/消息队列系统/Kafka生产者.md)
      - [延迟消息处理](./content/模块架构/基础设施层/消息队列系统/延迟消息处理.md)
      - [消息队列系统](./content/模块架构/基础设施层/消息队列系统/消息队列系统.md)
    - **缓存管理**
      - [Redis配置](./content/模块架构/基础设施层/缓存管理/Redis配置.md)
      - [本地缓存](./content/模块架构/基础设施层/缓存管理/本地缓存.md)
      - [缓存策略](./content/模块架构/基础设施层/缓存管理/缓存策略.md)
      - [缓存管理](./content/模块架构/基础设施层/缓存管理/缓存管理.md)
  - **数据访问层**
    - [实体映射](./content/模块架构/数据访问层/实体映射.md)
    - [数据访问层](./content/模块架构/数据访问层/数据访问层.md)
    - **Mapper实现**
      - [Mapper实现](./content/模块架构/数据访问层/Mapper实现/Mapper实现.md)
      - [Mapper接口设计](./content/模块架构/数据访问层/Mapper实现/Mapper接口设计.md)
      - [SQL映射实现](./content/模块架构/数据访问层/Mapper实现/SQL映射实现.md)
    - **数据源管理**
      - [AOP集成与切换](./content/模块架构/数据访问层/数据源管理/AOP集成与切换.md)
      - [数据源管理](./content/模块架构/数据访问层/数据源管理/数据源管理.md)
      - [数据源路由机制](./content/模块架构/数据访问层/数据源管理/数据源路由机制.md)
      - [数据源配置管理](./content/模块架构/数据访问层/数据源管理/数据源配置管理.md)
    - **数据访问模式**
      - [事务管理](./content/模块架构/数据访问层/数据访问模式/事务管理.md)
      - [批量处理](./content/模块架构/数据访问层/数据访问模式/批量处理.md)
      - [数据访问模式](./content/模块架构/数据访问层/数据访问模式/数据访问模式.md)
      - [查询优化](./content/模块架构/数据访问层/数据访问模式/查询优化.md)
  - **核心模块**
    - [常量定义](./content/模块架构/核心模块/常量定义.md)
    - [核心工具类](./content/模块架构/核心模块/核心工具类.md)
    - [核心模块](./content/模块架构/核心模块/核心模块.md)
    - [核心注解](./content/模块架构/核心模块/核心注解.md)
    - [缓存机制](./content/模块架构/核心模块/缓存机制.md)
    - [配置管理](./content/模块架构/核心模块/配置管理.md)
  - **门面接口层**
    - [门面接口层](./content/模块架构/门面接口层/门面接口层.md)
    - **接口定义**
      - [分包门面接口](./content/模块架构/门面接口层/接口定义/分包门面接口.md)
      - [报告门面接口](./content/模块架构/门面接口层/接口定义/报告门面接口.md)
      - [接口定义](./content/模块架构/门面接口层/接口定义/接口定义.md)
      - [测试线门面接口](./content/模块架构/门面接口层/接口定义/测试线门面接口.md)
      - [订单门面接口](./content/模块架构/门面接口层/接口定义/订单门面接口.md)
    - **接口实现机制**
      - [分包接口实现](./content/模块架构/门面接口层/接口实现机制/分包接口实现.md)
      - [报告接口实现](./content/模块架构/门面接口层/接口实现机制/报告接口实现.md)
      - [接口实现机制](./content/模块架构/门面接口层/接口实现机制/接口实现机制.md)
      - [测试线接口实现](./content/模块架构/门面接口层/接口实现机制/测试线接口实现.md)
      - [订单接口实现](./content/模块架构/门面接口层/接口实现机制/订单接口实现.md)
    - **数据传输对象(DTO)**
      - [分包数据传输对象](./content/模块架构/门面接口层/数据传输对象(DTO)/分包数据传输对象.md)
      - [报告数据传输对象](./content/模块架构/门面接口层/数据传输对象(DTO)/报告数据传输对象.md)
      - [数据传输对象(DTO)](./content/模块架构/门面接口层/数据传输对象(DTO)/数据传输对象(DTO).md)
      - [测试线数据传输对象](./content/模块架构/门面接口层/数据传输对象(DTO)/测试线数据传输对象.md)
      - [订单数据传输对象](./content/模块架构/门面接口层/数据传输对象(DTO)/订单数据传输对象.md)
    - **枚举与常量**
      - [分包状态枚举](./content/模块架构/门面接口层/枚举与常量/分包状态枚举.md)
      - [报告状态枚举](./content/模块架构/门面接口层/枚举与常量/报告状态枚举.md)
      - [枚举与常量](./content/模块架构/门面接口层/枚举与常量/枚举与常量.md)
      - [测试线状态枚举](./content/模块架构/门面接口层/枚举与常量/测试线状态枚举.md)
      - [订单状态枚举](./content/模块架构/门面接口层/枚举与常量/订单状态枚举.md)
  - **领域服务层**
    - [领域服务层](./content/模块架构/领域服务层/领域服务层.md)
    - **Kafka集成**
      - [Kafka消费者实现](./content/模块架构/领域服务层/Kafka集成/Kafka消费者实现.md)
      - [Kafka生产者集成](./content/模块架构/领域服务层/Kafka集成/Kafka生产者集成.md)
      - [Kafka集成](./content/模块架构/领域服务层/Kafka集成/Kafka集成.md)
      - [异常处理与故障恢复](./content/模块架构/领域服务层/Kafka集成/异常处理与故障恢复.md)
      - [监控与运维](./content/模块架构/领域服务层/Kafka集成/监控与运维.md)
    - **分包服务**
      - [分包创建](./content/模块架构/领域服务层/分包服务/分包创建.md)
      - [分包数据同步](./content/模块架构/领域服务层/分包服务/分包数据同步.md)
      - [分包服务](./content/模块架构/领域服务层/分包服务/分包服务.md)
      - [分包状态管理](./content/模块架构/领域服务层/分包服务/分包状态管理.md)
    - **报告服务**
      - [报告服务](./content/模块架构/领域服务层/报告服务/报告服务.md)
      - [报告状态管理](./content/模块架构/领域服务层/报告服务/报告状态管理.md)
      - [报告生成](./content/模块架构/领域服务层/报告服务/报告生成.md)
      - [报告返工处理](./content/模块架构/领域服务层/报告服务/报告返工处理.md)
    - **订单服务**
      - [订单创建](./content/模块架构/领域服务层/订单服务/订单创建.md)
      - [订单取消](./content/模块架构/领域服务层/订单服务/订单取消.md)
      - [订单同步](./content/模块架构/领域服务层/订单服务/订单同步.md)
      - [订单服务](./content/模块架构/领域服务层/订单服务/订单服务.md)
      - [订单查询](./content/模块架构/领域服务层/订单服务/订单查询.md)
- **系统架构**
  - **事件驱动与消息队列**
    - **事件驱动应用场景**
      - [事件驱动应用场景](./content/系统架构/事件驱动与消息队列/事件驱动应用场景/事件驱动应用场景.md)
      - [分包管理](./content/系统架构/事件驱动与消息队列/事件驱动应用场景/分包管理.md)
      - [报告同步](./content/系统架构/事件驱动与消息队列/事件驱动应用场景/报告同步.md)
      - [订单状态更新](./content/系统架构/事件驱动与消息队列/事件驱动应用场景/订单状态更新.md)
    - **消息消费处理**
      - [幂等性保证机制](./content/系统架构/事件驱动与消息队列/消息消费处理/幂等性保证机制.md)
      - [消息消费处理](./content/系统架构/事件驱动与消息队列/消息消费处理/消息消费处理.md)
      - [消息监听器实现](./content/系统架构/事件驱动与消息队列/消息消费处理/消息监听器实现.md)
      - [消费者配置](./content/系统架构/事件驱动与消息队列/消息消费处理/消费者配置.md)
      - [错误处理与死信队列](./content/系统架构/事件驱动与消息队列/消息消费处理/错误处理与死信队列.md)
    - **消息生产机制**
      - [客户端封装](./content/系统架构/事件驱动与消息队列/消息生产机制/客户端封装.md)
      - [消息生产机制](./content/系统架构/事件驱动与消息队列/消息生产机制/消息生产机制.md)
      - [消息类型设计](./content/系统架构/事件驱动与消息队列/消息生产机制/消息类型设计.md)
      - [生产者实现](./content/系统架构/事件驱动与消息队列/消息生产机制/生产者实现.md)
    - **配置管理**
      - [安全配置](./content/系统架构/事件驱动与消息队列/配置管理/安全配置.md)
      - [消费者配置](./content/系统架构/事件驱动与消息队列/配置管理/消费者配置.md)
      - [环境差异化配置](./content/系统架构/事件驱动与消息队列/配置管理/环境差异化配置.md)
      - [生产者配置](./content/系统架构/事件驱动与消息队列/配置管理/生产者配置.md)
      - [配置管理](./content/系统架构/事件驱动与消息队列/配置管理/配置管理.md)
  - **部署架构**
    - [部署拓扑](./content/系统架构/部署架构/部署拓扑.md)
    - **日志与监控**
      - [告警策略](./content/系统架构/部署架构/日志与监控/告警策略.md)
      - [日志与监控](./content/系统架构/部署架构/日志与监控/日志与监控.md)
      - [日志配置](./content/系统架构/部署架构/日志与监控/日志配置.md)
      - [监控指标](./content/系统架构/部署架构/日志与监控/监控指标.md)
    - **环境配置**
      - [UAT环境配置](./content/系统架构/部署架构/环境配置/UAT环境配置.md)
      - [开发环境配置](./content/系统架构/部署架构/环境配置/开发环境配置.md)
      - [测试环境配置](./content/系统架构/部署架构/环境配置/测试环境配置.md)
      - [环境配置](./content/系统架构/部署架构/环境配置/环境配置.md)
      - [环境配置概述](./content/系统架构/部署架构/环境配置/环境配置概述.md)
      - [生产环境配置](./content/系统架构/部署架构/环境配置/生产环境配置.md)
    - **部署脚本**
      - [开发环境启动脚本](./content/系统架构/部署架构/部署脚本/开发环境启动脚本.md)
      - [服务停止脚本](./content/系统架构/部署架构/部署脚本/服务停止脚本.md)
      - [测试环境启动脚本](./content/系统架构/部署架构/部署脚本/测试环境启动脚本.md)
      - [生产环境启动脚本](./content/系统架构/部署架构/部署脚本/生产环境启动脚本.md)
      - [用户验收测试环境启动脚本](./content/系统架构/部署架构/部署脚本/用户验收测试环境启动脚本.md)
      - [部署脚本](./content/系统架构/部署架构/部署脚本/部署脚本.md)
  - **领域驱动设计(DDD)**
    - [限界上下文](./content/系统架构/领域驱动设计(DDD)/限界上下文.md)
    - [领域服务](./content/系统架构/领域驱动设计(DDD)/领域服务.md)
    - **领域模型设计**
      - [实体与值对象](./content/系统架构/领域驱动设计(DDD)/领域模型设计/实体与值对象.md)
      - [聚合设计](./content/系统架构/领域驱动设计(DDD)/领域模型设计/聚合设计.md)
      - [领域模型设计](./content/系统架构/领域驱动设计(DDD)/领域模型设计/领域模型设计.md)

---
*📅 生成时间: 2025-09-19 18:16:38*
