# 分包数据同步

<cite>
**本文档引用的文件**  
- [StarLimsCommonService.java](otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/subcontract/StarLimsCommonService.java)
- [BizFlowFacade.java](otsnotes-subcontract-share/src/main/java/com/sgs/otsnotes/subcontract/tostarlims/matrix/bizprocess/bizprocess/BizFlowFacade.java)
- [StarLimsConfig.java](otsnotes-core/src/main/java/com/sgs/otsnotes/core/config/StarLimsConfig.java)
- [StarLimsReportReceiveServiceImpl.java](otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/subcontract/starlims/impl/StarLimsReportReceiveServiceImpl.java)
- [StarLimsFacadeImpl.java](otsnotes-facade-impl/src/main/java/com/sgs/otsnotes/facade/impl/StarLimsFacadeImpl.java)
- [StarLimsHandler.java](otsnotes-subcontract-share/src/main/java/com/sgs/otsnotes/subcontract/tostarlims/matrix/bizprocess/bizprocess/StarLimsHandler.java)
</cite>

## 目录
1. [简介](#简介)
2. [核心流程](#核心流程)
3. [BizFlowFacade 工作机制](#bizflowfacade-工作机制)
4. [与StarLims系统集成](#与starlims系统集成)
5. [数据校验与完整性检查](#数据校验与完整性检查)
6. [同步状态监控](#同步状态监控)
7. [序列图：同步交互流程](#序列图同步交互流程)
8. [性能优化建议](#性能优化建议)

## 简介
本文档详细描述了OTSNotes系统中分包数据同步的核心机制，重点聚焦于StarLimsCommonService模块的数据打包、协议转换和传输过程。文档解释了BizFlowFacade作为分包业务流程控制器如何协调不同状态的转换和处理，并详细说明了与StarLims系统的集成方式，包括HTTP客户端配置、认证机制和错误重试策略。同时，文档提供了同步过程中的数据校验规则、完整性检查方法以及同步状态监控的实现方式。

## 核心流程
分包数据同步的核心流程由`StarLimsCommonService`类驱动，主要包含数据接收、状态更新和报告处理三个阶段。该服务通过`receiveReportDoc`和`updateTimeTrack`等方法接收来自StarLims系统的回调请求，首先进行参数校验和身份验证，然后根据不同的`trackType`执行相应的业务逻辑。

数据同步流程始于StarLims系统发送状态更新或报告文档。`StarLimsCommonService`接收到请求后，会调用`checkStarLimsData`方法进行基础数据校验，包括验证`authId`、`orderNo`、`subContractNo`等关键字段的有效性。校验通过后，根据`trackType`的值（如`FolderCreation`、`CommitFolder`、`FolderReported`）调用相应的子服务方法，如`confirmSubcontract`、`testingSubcontract`或`completeSubcontract`，以更新分包单的状态。

在报告接收阶段，系统会解析StarLims返回的Word或PDF格式的报告文件，并将其与OTSNotes系统中的订单和分包单进行关联。通过`subReportExtMapper.saveBatchSubReportInfo`方法将报告信息持久化到数据库，并触发后续的结论数据和测试组数据的处理流程。

**Section sources**
- [StarLimsCommonService.java](otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/subcontract/StarLimsCommonService.java#L100-L300)

## BizFlowFacade 工作机制
`BizFlowFacade`作为分包业务流程的控制器，负责协调和执行复杂的业务逻辑链。它采用策略模式和上下文模式，通过`execute`方法接收能力标识（如`NotesCapabilityConstants.GetSubContractChemTestMatrixContext`）和业务上下文对象（如`SubContractChemTestMatrixContext`），然后根据能力标识调用相应的处理器。

`BizFlowFacade`的工作机制基于责任链模式，它维护了一个处理器链，每个处理器负责处理特定类型的业务逻辑。当`execute`方法被调用时，`BizFlowFacade`会遍历处理器链，找到能够处理当前能力标识的处理器，并将上下文对象传递给它。处理器在处理完业务逻辑后，可以修改上下文对象中的数据，这些修改会被后续的处理器所感知。

例如，在获取化学测试矩阵数据的场景中，`getChemTestMatrixList`方法会创建一个`SubContractChemTestMatrixContext`上下文对象，并调用`bizFlowFacade.execute`方法。`BizFlowFacade`会找到负责处理`GetSubContractChemTestMatrixContext`能力的处理器，该处理器会调用外部服务获取数据，并将结果存储在上下文对象的`chemMatrixList`字段中。调用方可以通过`chemTestMatrixContext.getChemMatrixList()`方法获取处理结果。

**Section sources**
- [BizFlowFacade.java](otsnotes-subcontract-share/src/main/java/com/sgs/otsnotes/subcontract/tostarlims/matrix/bizprocess/bizprocess/BizFlowFacade.java#L10-L100)
- [StarLimsCommonService.java](otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/subcontract/StarLimsCommonService.java#L1500-L1550)

## 与StarLims系统集成
与StarLims系统的集成主要通过HTTP客户端实现，配置信息集中在`StarLimsConfig`类中。该类定义了StarLims系统的认证ID（`starLimsAuthId`）、基础URL、超时时间等关键参数。系统使用`starlimsClient`对象发起HTTP请求，该对象由Spring框架自动注入，封装了底层的HTTP通信细节。

认证机制采用基于Header的简单认证，StarLims系统在发起回调时，会在HTTP请求头中携带`authId`字段。`StarLimsCommonService`的`checkStarLimsData`方法会从请求头中提取`authId`，并与配置文件中的`starLimsAuthId`进行比对，只有匹配的请求才会被处理。这种机制确保了只有授权的StarLims系统才能访问OTSNotes的接口。

错误重试策略主要由外部服务的客户端（如`testDataBizClient`）内部实现。当调用StarLims API失败时，客户端会根据预设的重试策略（如指数退避）进行重试，直到成功或达到最大重试次数。此外，关键的业务操作（如报告导入）被包裹在数据库事务中，通过`transactionTemplate.execute`方法执行，确保了数据的一致性和完整性。

**Section sources**
- [StarLimsConfig.java](otsnotes-core/src/main/java/com/sgs/otsnotes/core/config/StarLimsConfig.java#L10-L50)
- [StarLimsCommonService.java](otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/subcontract/StarLimsCommonService.java#L500-L600)

## 数据校验与完整性检查
同步过程中的数据校验分为多个层次。首先是基础参数校验，在`checkReceiveReport`方法中，系统会检查`files`列表是否为空，以及每个文件的`cloudId`、`fileId`、`fileName`和`approveDate`等字段是否为空。任何一项校验失败都会导致整个请求被拒绝。

其次是业务状态校验，`checkSubcontractStatus`方法会查询数据库中的分包单状态，确保其处于允许接收报告的状态（非`Cancelled`且非`Complete`）。对于`receiveReport`操作，如果分包单状态为`Created`，也会被拒绝，以防止过早接收报告。

在数据完整性方面，系统通过`receiveReportDocV2`方法中的事务处理来保证。该方法首先调用`subReportFacade.buildSubReport4Starlims`构建报告数据对象，然后调用`subReportRemoteService.importReport`将其导入系统。这两个操作被包裹在同一个数据库事务中，如果任何一个步骤失败，整个事务都会回滚，确保了数据的原子性。

**Section sources**
- [StarLimsCommonService.java](otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/subcontract/StarLimsCommonService.java#L800-L1000)

## 同步状态监控
同步状态的监控主要通过日志和业务指标来实现。系统在关键的业务节点（如接收到请求、开始事务、操作成功或失败）都会记录详细的日志信息，这些日志包含了`orderNo`、`subContractNo`、`objectNo`等关键标识，便于追踪和排查问题。

例如，在`receiveReportDoc`方法中，系统会记录“接收到请求参数”、“开启事务，进行入库操作”、“判断是否是第一次的最后一份report”等日志。这些日志不仅记录了操作的执行情况，还包含了相关的业务数据，为后续的审计和分析提供了依据。

此外，系统通过`CustomResult`对象返回操作结果，其中包含了`success`标志和`msg`消息，调用方可以根据这些信息判断同步操作的成败。虽然文档中未直接体现，但可以推断系统可能集成了更高级的监控工具（如Prometheus或Grafana），用于收集和展示同步成功率、平均处理时间等关键性能指标。

**Section sources**
- [StarLimsCommonService.java](otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/subcontract/StarLimsCommonService.java#L300-L400)

## 序列图：同步交互流程
```mermaid
sequenceDiagram
participant StarLims as StarLims系统
participant OTSNotes as OTSNotes系统
participant DB as 数据库
StarLims->>OTSNotes : 发送报告回调(receiveReportDoc)
OTSNotes->>OTSNotes : checkStarLimsData(校验authId, orderNo等)
OTSNotes->>OTSNotes : checkReceiveReport(校验文件信息)
OTSNotes->>OTSNotes : checkSubcontractStatus(检查分包单状态)
OTSNotes->>OTSNotes : buildSubReport4Starlims(构建报告数据)
OTSNotes->>OTSNotes : 开启数据库事务
OTSNotes->>DB : saveBatchSubReportInfo(保存报告信息)
OTSNotes->>OTSNotes : receiveReportDocV2(处理报告数据)
OTSNotes->>OTSNotes : importReport(导入报告)
OTSNotes->>OTSNotes : 事务提交
OTSNotes-->>StarLims : 返回成功响应
```

**Diagram sources**
- [StarLimsCommonService.java](otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/subcontract/StarLimsCommonService.java#L400-L800)

## 性能优化建议
1. **批量处理**：对于涉及多个文件或报告的场景，应尽可能使用批量操作API（如`saveBatchSubReportInfo`），减少数据库交互次数。
2. **缓存机制**：频繁查询的数据（如`conclusionList`、`ppArtifact`信息）可以引入Redis缓存，减少对数据库和外部服务的依赖。
3. **异步处理**：非关键路径的操作（如日志记录、通知发送）可以改为异步执行，避免阻塞主业务流程。
4. **连接池优化**：合理配置HTTP客户端和数据库连接池的大小，避免因连接不足导致性能瓶颈。
5. **代码层面优化**：避免在循环中进行数据库查询或远程调用，可以通过一次查询获取所有需要的数据，然后在内存中进行处理。