<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.otsnotes.dbstorages.mybatis.mapper.TestLineLabSectionRelInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineLabSectionRelInfoPO" >
    <id column="Id" property="id" jdbcType="BIGINT" />
    <result column="AccreditationId" property="accreditationId" jdbcType="INTEGER" />
    <result column="AccreditationDetailId" property="accreditationDetailId" jdbcType="INTEGER" />
    <result column="TestLineVersionId" property="testLineVersionId" jdbcType="INTEGER" />
    <result column="LabSectionBaseId" property="labSectionBaseId" jdbcType="BIGINT" />
    <result column="BizVersionId" property="bizVersionId" jdbcType="CHAR" />
    <result column="RelStatus" property="relStatus" jdbcType="INTEGER" />
    <result column="CreatedDate" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="ModifiedDate" property="modifiedDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    Id, AccreditationId, AccreditationDetailId, TestLineVersionId, LabSectionBaseId, 
    BizVersionId, RelStatus, CreatedDate, ModifiedDate
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineLabSectionRelInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tre_trims_testline_labsection_relationship
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from tre_trims_testline_labsection_relationship
    where Id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from tre_trims_testline_labsection_relationship
    where Id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineLabSectionRelInfoExample" >
    delete from tre_trims_testline_labsection_relationship
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineLabSectionRelInfoPO" >
    insert into tre_trims_testline_labsection_relationship (Id, AccreditationId, AccreditationDetailId,
      TestLineVersionId, LabSectionBaseId, BizVersionId, 
      RelStatus, CreatedDate, ModifiedDate
      )
    values (#{id,jdbcType=BIGINT}, #{accreditationId,jdbcType=INTEGER}, #{accreditationDetailId,jdbcType=INTEGER}, 
      #{testLineVersionId,jdbcType=INTEGER}, #{labSectionBaseId,jdbcType=BIGINT}, #{bizVersionId,jdbcType=CHAR}, 
      #{relStatus,jdbcType=INTEGER}, #{createdDate,jdbcType=TIMESTAMP}, #{modifiedDate,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineLabSectionRelInfoPO" >
    insert into tre_trims_testline_labsection_relationship
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        Id,
      </if>
      <if test="accreditationId != null" >
        AccreditationId,
      </if>
      <if test="accreditationDetailId != null" >
        AccreditationDetailId,
      </if>
      <if test="testLineVersionId != null" >
        TestLineVersionId,
      </if>
      <if test="labSectionBaseId != null" >
        LabSectionBaseId,
      </if>
      <if test="bizVersionId != null" >
        BizVersionId,
      </if>
      <if test="relStatus != null" >
        RelStatus,
      </if>
      <if test="createdDate != null" >
        CreatedDate,
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="accreditationId != null" >
        #{accreditationId,jdbcType=INTEGER},
      </if>
      <if test="accreditationDetailId != null" >
        #{accreditationDetailId,jdbcType=INTEGER},
      </if>
      <if test="testLineVersionId != null" >
        #{testLineVersionId,jdbcType=INTEGER},
      </if>
      <if test="labSectionBaseId != null" >
        #{labSectionBaseId,jdbcType=BIGINT},
      </if>
      <if test="bizVersionId != null" >
        #{bizVersionId,jdbcType=CHAR},
      </if>
      <if test="relStatus != null" >
        #{relStatus,jdbcType=INTEGER},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineLabSectionRelInfoExample" resultType="java.lang.Integer" >
    select count(*) from tre_trims_testline_labsection_relationship
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tre_trims_testline_labsection_relationship
    <set >
      <if test="record.id != null" >
        Id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.accreditationId != null" >
        AccreditationId = #{record.accreditationId,jdbcType=INTEGER},
      </if>
      <if test="record.accreditationDetailId != null" >
        AccreditationDetailId = #{record.accreditationDetailId,jdbcType=INTEGER},
      </if>
      <if test="record.testLineVersionId != null" >
        TestLineVersionId = #{record.testLineVersionId,jdbcType=INTEGER},
      </if>
      <if test="record.labSectionBaseId != null" >
        LabSectionBaseId = #{record.labSectionBaseId,jdbcType=BIGINT},
      </if>
      <if test="record.bizVersionId != null" >
        BizVersionId = #{record.bizVersionId,jdbcType=CHAR},
      </if>
      <if test="record.relStatus != null" >
        RelStatus = #{record.relStatus,jdbcType=INTEGER},
      </if>
      <if test="record.createdDate != null" >
        CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedDate != null" >
        ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tre_trims_testline_labsection_relationship
    set Id = #{record.id,jdbcType=BIGINT},
      AccreditationId = #{record.accreditationId,jdbcType=INTEGER},
      AccreditationDetailId = #{record.accreditationDetailId,jdbcType=INTEGER},
      TestLineVersionId = #{record.testLineVersionId,jdbcType=INTEGER},
      LabSectionBaseId = #{record.labSectionBaseId,jdbcType=BIGINT},
      BizVersionId = #{record.bizVersionId,jdbcType=CHAR},
      RelStatus = #{record.relStatus,jdbcType=INTEGER},
      CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineLabSectionRelInfoPO" >
    update tre_trims_testline_labsection_relationship
    <set >
      <if test="accreditationId != null" >
        AccreditationId = #{accreditationId,jdbcType=INTEGER},
      </if>
      <if test="accreditationDetailId != null" >
        AccreditationDetailId = #{accreditationDetailId,jdbcType=INTEGER},
      </if>
      <if test="testLineVersionId != null" >
        TestLineVersionId = #{testLineVersionId,jdbcType=INTEGER},
      </if>
      <if test="labSectionBaseId != null" >
        LabSectionBaseId = #{labSectionBaseId,jdbcType=BIGINT},
      </if>
      <if test="bizVersionId != null" >
        BizVersionId = #{bizVersionId,jdbcType=CHAR},
      </if>
      <if test="relStatus != null" >
        RelStatus = #{relStatus,jdbcType=INTEGER},
      </if>
      <if test="createdDate != null" >
        CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where Id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineLabSectionRelInfoPO" >
    update tre_trims_testline_labsection_relationship
    set AccreditationId = #{accreditationId,jdbcType=INTEGER},
      AccreditationDetailId = #{accreditationDetailId,jdbcType=INTEGER},
      TestLineVersionId = #{testLineVersionId,jdbcType=INTEGER},
      LabSectionBaseId = #{labSectionBaseId,jdbcType=BIGINT},
      BizVersionId = #{bizVersionId,jdbcType=CHAR},
      RelStatus = #{relStatus,jdbcType=INTEGER},
      CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP}
    where Id = #{id,jdbcType=BIGINT}
  </update>
</mapper>