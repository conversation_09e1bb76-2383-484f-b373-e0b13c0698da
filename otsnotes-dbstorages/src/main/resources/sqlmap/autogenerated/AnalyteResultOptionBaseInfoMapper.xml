<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.otsnotes.dbstorages.mybatis.mapper.AnalyteResultOptionBaseInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.otsnotes.dbstorages.mybatis.model.AnalyteResultOptionBaseInfoPO" >
    <id column="Id" property="id" jdbcType="BIGINT" />
    <result column="ResultBaseId" property="resultBaseId" jdbcType="BIGINT" />
    <result column="ResultId" property="resultId" jdbcType="INTEGER" />
    <result column="OptionId" property="optionId" jdbcType="INTEGER" />
    <result column="OptionValue" property="optionValue" jdbcType="VARCHAR" />
    <result column="BizVersionId" property="bizVersionId" jdbcType="CHAR" />
    <result column="Status" property="status" jdbcType="INTEGER" />
    <result column="CreatedDate" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="ModifiedDate" property="modifiedDate" jdbcType="BIGINT" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    Id, ResultBaseId, ResultId, OptionId, OptionValue, BizVersionId, `Status`, CreatedDate, 
    ModifiedDate
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.AnalyteResultOptionBaseInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_trims_analyte_result_option_baseinfo
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from tb_trims_analyte_result_option_baseinfo
    where Id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from tb_trims_analyte_result_option_baseinfo
    where Id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.AnalyteResultOptionBaseInfoExample" >
    delete from tb_trims_analyte_result_option_baseinfo
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.AnalyteResultOptionBaseInfoPO" >
    insert into tb_trims_analyte_result_option_baseinfo (Id, ResultBaseId, ResultId,
      OptionId, OptionValue, BizVersionId, 
      `Status`, CreatedDate, ModifiedDate
      )
    values (#{id,jdbcType=BIGINT}, #{resultBaseId,jdbcType=BIGINT}, #{resultId,jdbcType=INTEGER}, 
      #{optionId,jdbcType=INTEGER}, #{optionValue,jdbcType=VARCHAR}, #{bizVersionId,jdbcType=CHAR}, 
      #{status,jdbcType=INTEGER}, #{createdDate,jdbcType=TIMESTAMP}, #{modifiedDate,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.AnalyteResultOptionBaseInfoPO" >
    insert into tb_trims_analyte_result_option_baseinfo
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        Id,
      </if>
      <if test="resultBaseId != null" >
        ResultBaseId,
      </if>
      <if test="resultId != null" >
        ResultId,
      </if>
      <if test="optionId != null" >
        OptionId,
      </if>
      <if test="optionValue != null" >
        OptionValue,
      </if>
      <if test="bizVersionId != null" >
        BizVersionId,
      </if>
      <if test="status != null" >
        `Status`,
      </if>
      <if test="createdDate != null" >
        CreatedDate,
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="resultBaseId != null" >
        #{resultBaseId,jdbcType=BIGINT},
      </if>
      <if test="resultId != null" >
        #{resultId,jdbcType=INTEGER},
      </if>
      <if test="optionId != null" >
        #{optionId,jdbcType=INTEGER},
      </if>
      <if test="optionValue != null" >
        #{optionValue,jdbcType=VARCHAR},
      </if>
      <if test="bizVersionId != null" >
        #{bizVersionId,jdbcType=CHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.AnalyteResultOptionBaseInfoExample" resultType="java.lang.Integer" >
    select count(*) from tb_trims_analyte_result_option_baseinfo
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_trims_analyte_result_option_baseinfo
    <set >
      <if test="record.id != null" >
        Id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.resultBaseId != null" >
        ResultBaseId = #{record.resultBaseId,jdbcType=BIGINT},
      </if>
      <if test="record.resultId != null" >
        ResultId = #{record.resultId,jdbcType=INTEGER},
      </if>
      <if test="record.optionId != null" >
        OptionId = #{record.optionId,jdbcType=INTEGER},
      </if>
      <if test="record.optionValue != null" >
        OptionValue = #{record.optionValue,jdbcType=VARCHAR},
      </if>
      <if test="record.bizVersionId != null" >
        BizVersionId = #{record.bizVersionId,jdbcType=CHAR},
      </if>
      <if test="record.status != null" >
        `Status` = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.createdDate != null" >
        CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedDate != null" >
        ModifiedDate = #{record.modifiedDate,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_trims_analyte_result_option_baseinfo
    set Id = #{record.id,jdbcType=BIGINT},
      ResultBaseId = #{record.resultBaseId,jdbcType=BIGINT},
      ResultId = #{record.resultId,jdbcType=INTEGER},
      OptionId = #{record.optionId,jdbcType=INTEGER},
      OptionValue = #{record.optionValue,jdbcType=VARCHAR},
      BizVersionId = #{record.bizVersionId,jdbcType=CHAR},
      `Status` = #{record.status,jdbcType=INTEGER},
      CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      ModifiedDate = #{record.modifiedDate,jdbcType=BIGINT}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.AnalyteResultOptionBaseInfoPO" >
    update tb_trims_analyte_result_option_baseinfo
    <set >
      <if test="resultBaseId != null" >
        ResultBaseId = #{resultBaseId,jdbcType=BIGINT},
      </if>
      <if test="resultId != null" >
        ResultId = #{resultId,jdbcType=INTEGER},
      </if>
      <if test="optionId != null" >
        OptionId = #{optionId,jdbcType=INTEGER},
      </if>
      <if test="optionValue != null" >
        OptionValue = #{optionValue,jdbcType=VARCHAR},
      </if>
      <if test="bizVersionId != null" >
        BizVersionId = #{bizVersionId,jdbcType=CHAR},
      </if>
      <if test="status != null" >
        `Status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="createdDate != null" >
        CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate = #{modifiedDate,jdbcType=BIGINT},
      </if>
    </set>
    where Id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.AnalyteResultOptionBaseInfoPO" >
    update tb_trims_analyte_result_option_baseinfo
    set ResultBaseId = #{resultBaseId,jdbcType=BIGINT},
      ResultId = #{resultId,jdbcType=INTEGER},
      OptionId = #{optionId,jdbcType=INTEGER},
      OptionValue = #{optionValue,jdbcType=VARCHAR},
      BizVersionId = #{bizVersionId,jdbcType=CHAR},
      `Status` = #{status,jdbcType=INTEGER},
      CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      ModifiedDate = #{modifiedDate,jdbcType=BIGINT}
    where Id = #{id,jdbcType=BIGINT}
  </update>
</mapper>