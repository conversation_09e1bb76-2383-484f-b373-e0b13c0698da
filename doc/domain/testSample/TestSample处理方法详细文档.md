# TestSample对象处理方法详细文档

## 文档概述

本文档基于实际代码分析，详细梳理了TestSample对象的所有处理方法，包括每个方法的对象、状态、触发动作、触发规则，严格基于代码事实。

**【事实】** 基于以下核心文件的实际代码分析：
- `SampleService.java` - TestSample核心业务服务 (4586行)
- `TestSampleService.java` - TestSample辅助服务
- `SampleCopyService.java` - TestSample复制服务
- `TestSampleBizService.java` - TestSample业务接口服务
- `TestSampleLangService.java` - TestSample多语言服务

## TestSample处理方法分类总览

### 方法统计表

| 分类 | 方法数量 | 主要功能 | 权限要求 |
|------|----------|----------|----------|
| 样品分解管理 | 3 | 样品分解、更新、重置等操作 | 分包操作权限 |
| 样品分配管理 | 4 | 样品分配、取消、检查等操作 | 报告状态控制 |
| 样品查询相关 | 8 | 获取样品信息、列表查询等操作 | 基础查询权限 |
| 样品删除操作 | 1 | 删除原样品操作 | 高级操作权限 |
| 样品验证检查 | 6 | 参数验证、状态检查、业务规则验证 | 验证权限 |
| 样品复制相关 | 2 | 样品复制、获取样品等操作 | 测试线挂起权限 |
| 样品扩展信息 | 4 | 样品扩展信息管理和查询 | 基础编辑权限 |
| 分包相关 | 2 | 分包样品管理和查询 | 分包操作权限 |
| 辅助工具 | 8 | 排序、计算、状态构建等工具方法 | 基础工具权限 |
| 业务统计 | 3 | 统计查询、数据清理等操作 | 统计查询权限 |

### 1. 样品分解管理方法 (3个)

| 方法名 | 对象 | 权限控制 | 触发动作 | 主要触发规则 | 代码位置 |
|--------|------|----------|----------|-------------|----------|
| **updateBreakDown** | TestSample | 无 | 更新样品分解信息、创建Matrix关联、建立PP关系 | 订单存在、样品信息有效、无重复请求锁定 | SampleService.java:309 |
| **updateBreakDownOld** | TestSample | @AccessRule(subContractType=UpdateBreakDown) | 旧版样品分解更新逻辑 | 订单存在、样品信息有效、有分包更新权限 | SampleService.java:361 |
| **resetMixSampleNo** | TestSample | 无 | 重置混合样编号 | 混合样存在、编号规则变更 | SampleService.java:1462 |

### 2. 样品分配管理方法 (4个)

| 方法名 | 对象 | 权限控制 | 触发动作 | 主要触发规则 | 关联对象影响 | 代码位置 |
|--------|------|----------|----------|-------------|-------------|----------|
| **assignSample** | TestSample | @AccessRule(testLinePendingType=AssignSamplePpTlId) | 分配样品到测试线、获取可分配样品列表 | 订单ID有效、PP测试线关系存在、有分配权限 | **TestSample**: 样品状态查询<br/>**PPTestLineRel**: PP测试线关系查询<br/>**AssignSampleTips**: 分配提示生成<br/>**TestLine**: 测试线状态验证 | SampleService.java:2344 |
| **saveAssignSample** | TestSample | @AccessRule(reportStatus={Cancelled,Approved,Completed,Replaced}, disGroupKey="AssignSample_SaveConclusion", isVersionIncr=true) | 保存样品分配、创建TestMatrix、ReportMatrix、PPSampleRel关联 | 报告状态允许、有分配权限、版本递增控制 | **TestMatrix**: 新增Matrix记录<br/>**ReportMatrix**: 报告关联创建<br/>**PPSampleRel**: PP样品关系建立<br/>**TestCondition**: 测试条件关联<br/>**Limit**: 限值数据创建<br/>**LimitGroup**: 限值组创建<br/>**TestLine**: 状态可能更新<br/>**Order**: 版本递增控制 | SampleService.java:2682 |
| **assignSampleCancel** | TestSample | @AccessRule(subContractType=CancelAssignSample) | 取消样品分配、删除Matrix关联、清理相关数据 | 有取消权限、样品已分配 | **TestMatrix**: Matrix记录删除<br/>**ReportMatrix**: 报告关联删除<br/>**PPSampleRel**: PP样品关系删除<br/>**TestCondition**: 测试条件清理<br/>**Limit**: 限值数据删除<br/>**LimitGroup**: 限值组删除<br/>**TestData**: 测试数据清理<br/>**TestLine**: 状态可能回退 | SampleService.java:3422 |
| **checkAssignSample** | TestSample | 无 | 检查样品分配的合法性和完整性 | 测试线ID非空 | **TestLine**: 状态验证<br/>**TestSample**: 分配状态检查<br/>**Matrix**: 关联关系验证<br/>**Report**: 报告状态检查 | SampleService.java:2618 |

### 3. 样品查询相关方法 (8个)

| 方法名 | 对象 | 触发动作 | 主要触发规则 | 关联对象影响 | 代码位置 |
|--------|------|----------|-------------|-------------|----------|
| **getOriginalSampleInfoList** | TestSample(原样) | 获取订单下的所有原样信息 | 订单号有效 | **TestSample**: 原样数据查询<br/>**Order**: 订单关联验证<br/>**SampleType**: 原样类型过滤 | SampleService.java:3717 |
| **getTestSampleListByOrderNo** | TestSample | 获取订单下的所有测试样品 | 订单号有效 | **TestSample**: 测试样品查询<br/>**SampleType**: 样品类型过滤<br/>**Order**: 订单关联验证 | SampleService.java:3725 |
| **getComponentSample** | TestSample(子样) | 获取可用的子样（未分配、未取消、未NC） | 订单号有效、绑定TRF | **TestSample**: 子样状态查询<br/>**TestMatrix**: 分配状态检查<br/>**TestLine**: 测试线状态过滤<br/>**OrderTrfRelationship**: TRF关系验证<br/>**SampleType**: 子样类型过滤 | SampleService.java:4051 |
| **getSubContractOriginalSampleList** | TestSample(分包原样) | 获取分包订单的原样列表 | 分包订单号有效、有查询权限 | **TestSample**: 分包原样查询<br/>**SubContract**: 分包关联验证<br/>**Order**: 分包订单验证<br/>**SampleType**: 原样类型过滤 | SampleService.java:1985 |
| **getTestSampleList** | TestSample(分包) | 获取分包测试样品列表 | 订单号和分包号有效 | **TestSample**: 分包样品查询<br/>**SubContract**: 分包关联验证<br/>**Order**: 订单关联验证<br/>**TestMatrix**: 分包Matrix关联 | SampleService.java:2010 |
| **getSampleExtInfo** | TestSample(扩展信息) | 获取样品扩展信息 | 订单号有效、用户登录有效 | **TestSampleExt**: 扩展信息查询<br/>**TestSample**: 样品基础信息<br/>**User**: 用户权限验证<br/>**ComponentSample**: 组件样品信息 | SampleService.java:3741 |
| **querySampleExtInfo** | TestSample(扩展信息) | 查询样品扩展信息 | 订单号有效 | **TestSampleExt**: 扩展信息查询<br/>**TestSample**: 样品关联验证<br/>**Order**: 订单范围限制 | SampleService.java:4520 |
| **queryTestSampleByTestLineInstanceId** | TestSample | 根据测试线实例ID查询样品类型信息 | 测试线实例ID列表非空 | **TestSample**: 样品类型查询<br/>**TestLine**: 测试线关联验证<br/>**TestMatrix**: Matrix关联查询<br/>**SampleType**: 样品类型信息 | SampleService.java:4533 |

### 4. 样品删除操作方法 (1个)

| 方法名 | 对象 | 权限控制 | 触发动作 | 主要触发规则 | 代码位置 |
|--------|------|----------|----------|-------------|----------|
| **deleteOriginalSample** | TestSample(原样) | @AccessRule(testLinePendingType=TestLineInstanceId) | 物理删除样品记录、清理关联数据 | 样品未分配、无Matrix关联、有删除权限 | SampleService.java:3664 |

### 5. 样品验证检查方法 (6个)

| 方法名 | 对象 | 触发动作 | 主要触发规则 | 代码位置 |
|--------|------|----------|-------------|----------|
| **checkSampleParameters** | TestSample | 验证样品参数的完整性和合法性 | 请求对象非空、参数格式正确 | SampleService.java:1165 |
| **checkSampleNc** | TestSample | 检查样品NC状态和相关约束 | 样品存在、Matrix关联检查 | SampleService.java:1527 |
| **verifyDuplicateSamples** | TestSample | 验证重复样品分配 | 样品ID非空、无重复分配 | SampleService.java:895 |
| **checkMixAssignSample** | TestSample(混合样) | 检查混合样分配状态 | 样品ID或Matrix ID非空 | SampleService.java:4310 |
| **checkSubmitData** | TestSample | 检查样品提交数据的完整性 | 订单号有效、数据完整 | SampleService.java:4226 |
| **validateToSlimSubcontracts** | TestSample(分包) | 验证样品到轻量分包的转换 | 样品存在、分包规则有效 | SampleService.java:2123 |

### 6. 样品复制相关方法 (2个)

| 方法名 | 对象 | 权限控制 | 触发动作 | 主要触发规则 | 代码位置 |
|--------|------|----------|----------|-------------|----------|
| **copyTestLineGetSample** | TestSample | @AccessRule(testLinePendingType=TestLineInstanceId) | 复制测试线时获取样品信息 | 测试线实例ID有效、有复制权限 | SampleService.java:3635 |
| **doCopy** | TestSample | 无 | 执行样品复制操作、处理删除和取消样品 | 复制对象有效、样品数据完整 | SampleCopyService.java:695 |

### 7. 样品扩展信息方法 (4个)

| 方法名 | 对象 | 触发动作 | 主要触发规则 | 代码位置 |
|--------|------|----------|-------------|----------|
| **saveSampleExtInfo** | TestSample(扩展信息) | 保存样品扩展信息 | 用户登录有效、数据非空 | SampleService.java:3956 |
| **updateComponentSampleCategory** | TestSample(子样分类) | 更新子样分类信息 | 订单号有效、分类项目非空 | SampleService.java:4106 |
| **queryTestResultByOrderNo** | TestSample(测试结果) | 查询订单下的测试结果 | 订单号有效 | SampleService.java:3733 |
| **cleanSampleSourceTypeHistory** | TestSample(历史数据) | 清理样品来源类型历史数据 | 开始/结束日期非空 | SampleService.java:4574 |

## 样品类型处理规则

**【事实】** 基于SampleType枚举和TestSampleService的实际处理逻辑：

### 原样处理 (OriginalSample - 101)
- **创建规则**: 直接创建，无父样品依赖
- **查询方法**: getOriginalSampleInfoList、getSubContractOriginalSampleList
- **删除约束**: 检查是否有子样品、是否已分配Matrix
- **特殊处理**: 作为其他样品类型的根节点

### 子样处理 (Sample - 102)
- **创建规则**: 必须有OriginalSample作为父样品
- **层级关系**: 可以生成SubSample
- **查询方法**: getComponentSample、getTestSampleListByOrderNo
- **分配逻辑**: 可以分配给TestLine，建立Matrix关联

### 子子样处理 (SubSample - 103)
- **创建规则**: 父样品必须是Sample或ShareSample
- **层级限制**: 不能再生成下级样品
- **处理逻辑**: 与Sample类似，但层级更深

### 混合样处理 (MixSample - 104)
- **特殊逻辑**: 需要拆分并找到各自的原样
- **PP关联**: 在出报告时只跟在第一个PP下面
- **查询方法**: getOriginalSample中有专门的混合样处理逻辑
- **编号管理**: resetMixSampleNo专门处理混合样编号

### 共享样处理 (ShareSample - 105)
- **创建规则**: 父样品必须是OriginalSample
- **共享逻辑**: 可以被多个测试线共享使用
- **处理方式**: 与Sample类似，但有共享特性

## 权限控制机制

**【事实】** 基于@AccessRule注解的权限控制：

### 报告状态控制
- **saveAssignSample**: reportStatus={Cancelled,Approved,Completed,Replaced}
- **版本控制**: isVersionIncr=true，防止并发冲突
- **分组权限**: disGroupKey="AssignSample_SaveConclusion"

### 分包操作控制
- **assignSampleCancel**: subContractType=CancelAssignSample
- **updateBreakDownOld**: subContractType=UpdateBreakDown

### 测试线挂起控制
- **assignSample**: testLinePendingType=AssignSamplePpTlId
- **copyTestLineGetSample**: testLinePendingType=TestLineInstanceId
- **deleteOriginalSample**: testLinePendingType=TestLineInstanceId

## 数据库事务处理

**【事实】** 样品操作的事务处理机制：

### 事务范围控制
- **样品分配**: 在事务中创建TestMatrix、ReportMatrix、PPSampleRel、Limit、LimitGroup等关联数据
- **样品取消**: 在事务中删除所有相关联的数据，包括Matrix、Condition、Position等
- **样品更新**: 在事务中更新样品信息和相关的语言信息
- **样品删除**: 在事务中删除样品及所有关联数据

### 分布式锁机制
- **updateBreakDown**: 使用Redis锁防止重复请求
- **锁定键格式**: `{className}_{methodName}_{orderNo}`
- **锁定时间**: 防止并发操作冲突

### 数据一致性保证
- **关联数据同步**: 样品操作时同步更新所有关联表数据
- **回滚机制**: 任何步骤失败时完整回滚所有变更
- **版本控制**: 通过版本号控制并发更新

## 错误处理和异常机制

**【事实】** 基于代码中的错误处理模式：

### 常见错误类型
- **参数验证错误**: "请求对象不能为空"、"订单号不能为空"
- **业务规则违反**: "This Sample has Assigned!"、"当前Sample已NC，不能再Assign"
- **权限验证错误**: 用户无相应操作权限
- **数据完整性错误**: 样品关联数据不完整或冲突
- **并发控制错误**: "不能重复请求"、版本冲突

### 异常处理策略
- **立即返回**: 参数验证失败时立即返回CustomResult.fail()
- **事务回滚**: 业务操作失败时设置trans.setRollbackOnly()
- **分布式锁**: 使用Redis锁防止并发操作
- **日志记录**: 详细记录操作历史和错误信息
- **状态保护**: 错误情况下保持原有状态不变

## 辅助工具方法 (8个)

| 方法名 | 对象 | 触发动作 | 主要触发规则 | 代码位置 |
|--------|------|----------|-------------|----------|
| **recalculateSort** | TestSample | 重新计算样品排序 | 样品列表非空、父样品存在 | SampleService.java:1421 |
| **findParentSample** | TestSample | 查找父样品信息 | 样品列表非空、子样品有父ID | SampleService.java:1929 |
| **findParentSampleType** | TestSample | 查找父样品类型 | 样品列表非空、子样品有父ID | SampleService.java:1963 |
| **buildTestLineStatusResult** | TestSample | 构建测试线状态结果 | 报告状态和测试线状态有效 | SampleService.java:999 |
| **matrixUpdateCondition** | TestSample | 更新Matrix条件 | 订单ID有效、测试线列表非空 | SampleService.java:1090 |
| **setOldTestMatrixMaps** | TestSample | 设置旧Matrix映射 | 订单存在、Matrix数据有效 | SampleService.java:814 |
| **getTestSampleGroupInfoPOS** | TestSample | 获取样品组信息 | 订单号有效、样品组存在 | SampleService.java:829 |
| **getOriginalSampleNCMap** | TestSample | 获取原样NC状态映射 | 样品列表非空、样品ID集合有效 | SampleService.java:863 |

## 业务统计方法 (3个)

| 方法名 | 对象 | 触发动作 | 主要触发规则 | 代码位置 |
|--------|------|----------|-------------|----------|
| **convertToSlimSubcontracts** | TestSample(轻量分包) | 转换样品到轻量分包对象 | 订单存在、分包映射有效 | SampleService.java:2194 |
| **handlePPSampleRel** | TestSample(PP关联) | 处理PP样品关联关系 | PP关联映射非空、删除ID列表有效 | SampleService.java:1709 |
| **handleReportMatrixRel** | TestSample(报告Matrix) | 处理报告Matrix关联关系 | 新Matrix列表非空、报告ID有效 | SampleService.java:1753 |

## TestSample与外部系统集成

**【事实】** TestSample与其他业务对象的集成关系：

### 1. 与TestMatrix的集成
- **创建关联**: 样品分配时创建TestMatrix记录
- **状态同步**: 样品状态变更影响Matrix状态
- **删除联动**: 样品删除时清理相关Matrix
- **条件更新**: matrixUpdateCondition方法处理Matrix条件更新

### 2. 与ReportMatrix的集成
- **报告关联**: 通过ReportMatrixRelationship维护样品与报告的关联
- **状态检查**: 报告状态影响样品操作权限
- **数据同步**: 样品变更时同步更新报告相关数据
- **关联处理**: handleReportMatrixRel方法处理报告Matrix关联

### 3. 与PP系统的集成
- **PP关联**: 通过PPSampleRelationship维护样品与PP的关联
- **层级管理**: PP系统中的样品层级关系管理
- **数据同步**: 与TRIMS本地化系统的数据同步
- **关联处理**: handlePPSampleRel方法处理PP样品关联

### 4. 与分包系统的集成
- **分包样品**: 分包订单中的样品有特殊处理逻辑
- **数据锁定**: 分包数据锁定时限制样品操作
- **状态同步**: 分包完成时同步样品状态
- **轻量分包**: convertToSlimSubcontracts方法处理轻量分包转换

### 5. 与TestLine系统的集成
- **样品分配**: assignSample方法处理样品到测试线的分配
- **状态联动**: 测试线状态变更影响样品操作权限
- **挂起控制**: 测试线挂起时限制样品相关操作
- **复制功能**: copyTestLineGetSample方法支持测试线复制时的样品处理

## 样品生命周期管理详解

**【事实】** 基于SampleService中的实际业务流程：

### 1. 样品创建阶段
- **updateBreakDown**: 创建或更新样品记录，建立样品层级关系
- **参数验证**: checkSampleParameters验证创建参数
- **重复检查**: verifyDuplicateSamples防止重复创建
- **排序计算**: recalculateSort重新计算样品排序

### 2. 样品分配阶段
- **分配查询**: assignSample获取可分配的样品列表
- **分配保存**: saveAssignSample创建TestMatrix、ReportMatrix、PPSampleRel关联
- **分配检查**: checkAssignSample验证分配的合法性
- **混合样检查**: checkMixAssignSample处理混合样特殊逻辑

### 3. 样品使用阶段
- **状态监控**: 通过Matrix关联监控样品使用状态
- **扩展信息**: saveSampleExtInfo、getSampleExtInfo管理扩展信息
- **分类更新**: updateComponentSampleCategory更新子样分类
- **数据提交**: checkSubmitData验证提交数据完整性

### 4. 样品取消阶段
- **取消分配**: assignSampleCancel删除Matrix关联，清理相关数据
- **NC处理**: checkSampleNc检查NC状态和约束
- **状态回滚**: 恢复样品到可用状态

### 5. 样品删除阶段
- **删除检查**: 验证样品是否可以删除（无Matrix关联、无子样品）
- **物理删除**: deleteOriginalSample执行物理删除
- **关联清理**: 清理所有相关的数据表记录

## 样品数据处理机制

**【事实】** 基于SampleDataProcessingHandler的处理逻辑：

### 数据处理流程
1. **初始化数据容器**: 准备处理所需的数据结构
2. **准备语言映射**: prepareSampleLanguageData处理多语言数据
3. **处理单个样品**: processSingleSample逐个处理样品
4. **处理删除样品**: processDeletedSamples处理需要删除的样品
5. **处理删除样品组**: processDeletedSampleGroups处理样品组删除
6. **验证新样品ID**: validateNewSampleIds验证新样品ID合法性

### 数据验证机制
- **参数完整性**: 验证必要参数是否完整
- **业务规则**: 验证是否符合业务规则约束
- **数据一致性**: 验证数据之间的一致性
- **权限检查**: 验证操作权限是否足够

---

**文档生成时间**: 2025-09-28
**基于代码版本**: 当前项目代码
**维护责任**: TestSample管理模块开发团队
