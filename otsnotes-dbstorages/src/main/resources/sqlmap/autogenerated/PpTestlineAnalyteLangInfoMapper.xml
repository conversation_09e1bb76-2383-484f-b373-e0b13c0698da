<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.otsnotes.dbstorages.mybatis.mapper.PpTestlineAnalyteLangInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.otsnotes.dbstorages.mybatis.model.PpTestlineAnalyteLangInfoPO" >
    <id column="Id" property="id" jdbcType="BIGINT" />
    <result column="PpVersionId" property="ppVersionId" jdbcType="INTEGER" />
    <result column="ArtifactId" property="artifactId" jdbcType="BIGINT" />
    <result column="TestAnalyteId" property="testAnalyteId" jdbcType="INTEGER" />
    <result column="LanguageId" property="languageId" jdbcType="INTEGER" />
    <result column="DescriptionAlias" property="descriptionAlias" jdbcType="VARCHAR" />
    <result column="BizVersionId" property="bizVersionId" jdbcType="CHAR" />
    <result column="Status" property="status" jdbcType="TINYINT" />
    <result column="CreatedDate" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="ModifiedDate" property="modifiedDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    Id, PpVersionId, ArtifactId, TestAnalyteId, LanguageId, DescriptionAlias, BizVersionId, 
    `Status`, CreatedDate, ModifiedDate
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.PpTestlineAnalyteLangInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_trims_pp_testline_analyte_language
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from tb_trims_pp_testline_analyte_language
    where Id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from tb_trims_pp_testline_analyte_language
    where Id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.PpTestlineAnalyteLangInfoExample" >
    delete from tb_trims_pp_testline_analyte_language
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.PpTestlineAnalyteLangInfoPO" >
    insert into tb_trims_pp_testline_analyte_language (Id, PpVersionId, ArtifactId,
      TestAnalyteId, LanguageId, DescriptionAlias, 
      BizVersionId, `Status`, CreatedDate, 
      ModifiedDate)
    values (#{id,jdbcType=BIGINT}, #{ppVersionId,jdbcType=INTEGER}, #{artifactId,jdbcType=BIGINT}, 
      #{testAnalyteId,jdbcType=INTEGER}, #{languageId,jdbcType=INTEGER}, #{descriptionAlias,jdbcType=VARCHAR}, 
      #{bizVersionId,jdbcType=CHAR}, #{status,jdbcType=TINYINT}, #{createdDate,jdbcType=TIMESTAMP}, 
      #{modifiedDate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.PpTestlineAnalyteLangInfoPO" >
    insert into tb_trims_pp_testline_analyte_language
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        Id,
      </if>
      <if test="ppVersionId != null" >
        PpVersionId,
      </if>
      <if test="artifactId != null" >
        ArtifactId,
      </if>
      <if test="testAnalyteId != null" >
        TestAnalyteId,
      </if>
      <if test="languageId != null" >
        LanguageId,
      </if>
      <if test="descriptionAlias != null" >
        DescriptionAlias,
      </if>
      <if test="bizVersionId != null" >
        BizVersionId,
      </if>
      <if test="status != null" >
        `Status`,
      </if>
      <if test="createdDate != null" >
        CreatedDate,
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="ppVersionId != null" >
        #{ppVersionId,jdbcType=INTEGER},
      </if>
      <if test="artifactId != null" >
        #{artifactId,jdbcType=BIGINT},
      </if>
      <if test="testAnalyteId != null" >
        #{testAnalyteId,jdbcType=INTEGER},
      </if>
      <if test="languageId != null" >
        #{languageId,jdbcType=INTEGER},
      </if>
      <if test="descriptionAlias != null" >
        #{descriptionAlias,jdbcType=VARCHAR},
      </if>
      <if test="bizVersionId != null" >
        #{bizVersionId,jdbcType=CHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=TINYINT},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.PpTestlineAnalyteLangInfoExample" resultType="java.lang.Integer" >
    select count(*) from tb_trims_pp_testline_analyte_language
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_trims_pp_testline_analyte_language
    <set >
      <if test="record.id != null" >
        Id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.ppVersionId != null" >
        PpVersionId = #{record.ppVersionId,jdbcType=INTEGER},
      </if>
      <if test="record.artifactId != null" >
        ArtifactId = #{record.artifactId,jdbcType=BIGINT},
      </if>
      <if test="record.testAnalyteId != null" >
        TestAnalyteId = #{record.testAnalyteId,jdbcType=INTEGER},
      </if>
      <if test="record.languageId != null" >
        LanguageId = #{record.languageId,jdbcType=INTEGER},
      </if>
      <if test="record.descriptionAlias != null" >
        DescriptionAlias = #{record.descriptionAlias,jdbcType=VARCHAR},
      </if>
      <if test="record.bizVersionId != null" >
        BizVersionId = #{record.bizVersionId,jdbcType=CHAR},
      </if>
      <if test="record.status != null" >
        `Status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.createdDate != null" >
        CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedDate != null" >
        ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_trims_pp_testline_analyte_language
    set Id = #{record.id,jdbcType=BIGINT},
      PpVersionId = #{record.ppVersionId,jdbcType=INTEGER},
      ArtifactId = #{record.artifactId,jdbcType=BIGINT},
      TestAnalyteId = #{record.testAnalyteId,jdbcType=INTEGER},
      LanguageId = #{record.languageId,jdbcType=INTEGER},
      DescriptionAlias = #{record.descriptionAlias,jdbcType=VARCHAR},
      BizVersionId = #{record.bizVersionId,jdbcType=CHAR},
      `Status` = #{record.status,jdbcType=TINYINT},
      CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.PpTestlineAnalyteLangInfoPO" >
    update tb_trims_pp_testline_analyte_language
    <set >
      <if test="ppVersionId != null" >
        PpVersionId = #{ppVersionId,jdbcType=INTEGER},
      </if>
      <if test="artifactId != null" >
        ArtifactId = #{artifactId,jdbcType=BIGINT},
      </if>
      <if test="testAnalyteId != null" >
        TestAnalyteId = #{testAnalyteId,jdbcType=INTEGER},
      </if>
      <if test="languageId != null" >
        LanguageId = #{languageId,jdbcType=INTEGER},
      </if>
      <if test="descriptionAlias != null" >
        DescriptionAlias = #{descriptionAlias,jdbcType=VARCHAR},
      </if>
      <if test="bizVersionId != null" >
        BizVersionId = #{bizVersionId,jdbcType=CHAR},
      </if>
      <if test="status != null" >
        `Status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="createdDate != null" >
        CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where Id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.PpTestlineAnalyteLangInfoPO" >
    update tb_trims_pp_testline_analyte_language
    set PpVersionId = #{ppVersionId,jdbcType=INTEGER},
      ArtifactId = #{artifactId,jdbcType=BIGINT},
      TestAnalyteId = #{testAnalyteId,jdbcType=INTEGER},
      LanguageId = #{languageId,jdbcType=INTEGER},
      DescriptionAlias = #{descriptionAlias,jdbcType=VARCHAR},
      BizVersionId = #{bizVersionId,jdbcType=CHAR},
      `Status` = #{status,jdbcType=TINYINT},
      CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP}
    where Id = #{id,jdbcType=BIGINT}
  </update>
</mapper>