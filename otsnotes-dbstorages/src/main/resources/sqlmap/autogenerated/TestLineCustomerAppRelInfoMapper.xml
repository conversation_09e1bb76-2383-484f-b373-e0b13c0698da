<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.otsnotes.dbstorages.mybatis.mapper.TestLineCustomerAppRelInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineCustomerAppRelInfoPO" >
    <id column="Id" property="id" jdbcType="BIGINT" />
    <result column="TestLineVersionId" property="testLineVersionId" jdbcType="INTEGER" />
    <result column="CustomerAccountId" property="customerAccountId" jdbcType="INTEGER" />
    <result column="BizVersionId" property="bizVersionId" jdbcType="CHAR" />
    <result column="RelStatus" property="relStatus" jdbcType="INTEGER" />
    <result column="CreatedDate" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="ModifiedDate" property="modifiedDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineCustomerAppRelInfoWithBLOBs" extends="BaseResultMap" >
    <result column="ReportReferenceNote" property="reportReferenceNote" jdbcType="LONGVARCHAR" />
    <result column="ConditionInstructions" property="conditionInstructions" jdbcType="LONGVARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    Id, TestLineVersionId, CustomerAccountId, BizVersionId, RelStatus, CreatedDate, ModifiedDate
  </sql>
  <sql id="Blob_Column_List" >
    ReportReferenceNote, ConditionInstructions
  </sql>
  <select id="selectByExampleWithBLOBs" resultMap="ResultMapWithBLOBs" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineCustomerAppRelInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from tre_trims_testline_customer_applicability_relationship
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineCustomerAppRelInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tre_trims_testline_customer_applicability_relationship
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from tre_trims_testline_customer_applicability_relationship
    where Id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from tre_trims_testline_customer_applicability_relationship
    where Id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineCustomerAppRelInfoExample" >
    delete from tre_trims_testline_customer_applicability_relationship
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineCustomerAppRelInfoWithBLOBs" >
    insert into tre_trims_testline_customer_applicability_relationship (Id, TestLineVersionId, CustomerAccountId,
      BizVersionId, RelStatus, CreatedDate, 
      ModifiedDate, ReportReferenceNote, 
      ConditionInstructions)
    values (#{id,jdbcType=BIGINT}, #{testLineVersionId,jdbcType=INTEGER}, #{customerAccountId,jdbcType=INTEGER}, 
      #{bizVersionId,jdbcType=CHAR}, #{relStatus,jdbcType=INTEGER}, #{createdDate,jdbcType=TIMESTAMP}, 
      #{modifiedDate,jdbcType=TIMESTAMP}, #{reportReferenceNote,jdbcType=LONGVARCHAR}, 
      #{conditionInstructions,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineCustomerAppRelInfoWithBLOBs" >
    insert into tre_trims_testline_customer_applicability_relationship
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        Id,
      </if>
      <if test="testLineVersionId != null" >
        TestLineVersionId,
      </if>
      <if test="customerAccountId != null" >
        CustomerAccountId,
      </if>
      <if test="bizVersionId != null" >
        BizVersionId,
      </if>
      <if test="relStatus != null" >
        RelStatus,
      </if>
      <if test="createdDate != null" >
        CreatedDate,
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate,
      </if>
      <if test="reportReferenceNote != null" >
        ReportReferenceNote,
      </if>
      <if test="conditionInstructions != null" >
        ConditionInstructions,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="testLineVersionId != null" >
        #{testLineVersionId,jdbcType=INTEGER},
      </if>
      <if test="customerAccountId != null" >
        #{customerAccountId,jdbcType=INTEGER},
      </if>
      <if test="bizVersionId != null" >
        #{bizVersionId,jdbcType=CHAR},
      </if>
      <if test="relStatus != null" >
        #{relStatus,jdbcType=INTEGER},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="reportReferenceNote != null" >
        #{reportReferenceNote,jdbcType=LONGVARCHAR},
      </if>
      <if test="conditionInstructions != null" >
        #{conditionInstructions,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineCustomerAppRelInfoExample" resultType="java.lang.Integer" >
    select count(*) from tre_trims_testline_customer_applicability_relationship
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tre_trims_testline_customer_applicability_relationship
    <set >
      <if test="record.id != null" >
        Id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.testLineVersionId != null" >
        TestLineVersionId = #{record.testLineVersionId,jdbcType=INTEGER},
      </if>
      <if test="record.customerAccountId != null" >
        CustomerAccountId = #{record.customerAccountId,jdbcType=INTEGER},
      </if>
      <if test="record.bizVersionId != null" >
        BizVersionId = #{record.bizVersionId,jdbcType=CHAR},
      </if>
      <if test="record.relStatus != null" >
        RelStatus = #{record.relStatus,jdbcType=INTEGER},
      </if>
      <if test="record.createdDate != null" >
        CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedDate != null" >
        ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.reportReferenceNote != null" >
        ReportReferenceNote = #{record.reportReferenceNote,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.conditionInstructions != null" >
        ConditionInstructions = #{record.conditionInstructions,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map" >
    update tre_trims_testline_customer_applicability_relationship
    set Id = #{record.id,jdbcType=BIGINT},
      TestLineVersionId = #{record.testLineVersionId,jdbcType=INTEGER},
      CustomerAccountId = #{record.customerAccountId,jdbcType=INTEGER},
      BizVersionId = #{record.bizVersionId,jdbcType=CHAR},
      RelStatus = #{record.relStatus,jdbcType=INTEGER},
      CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      ReportReferenceNote = #{record.reportReferenceNote,jdbcType=LONGVARCHAR},
      ConditionInstructions = #{record.conditionInstructions,jdbcType=LONGVARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tre_trims_testline_customer_applicability_relationship
    set Id = #{record.id,jdbcType=BIGINT},
      TestLineVersionId = #{record.testLineVersionId,jdbcType=INTEGER},
      CustomerAccountId = #{record.customerAccountId,jdbcType=INTEGER},
      BizVersionId = #{record.bizVersionId,jdbcType=CHAR},
      RelStatus = #{record.relStatus,jdbcType=INTEGER},
      CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineCustomerAppRelInfoWithBLOBs" >
    update tre_trims_testline_customer_applicability_relationship
    <set >
      <if test="testLineVersionId != null" >
        TestLineVersionId = #{testLineVersionId,jdbcType=INTEGER},
      </if>
      <if test="customerAccountId != null" >
        CustomerAccountId = #{customerAccountId,jdbcType=INTEGER},
      </if>
      <if test="bizVersionId != null" >
        BizVersionId = #{bizVersionId,jdbcType=CHAR},
      </if>
      <if test="relStatus != null" >
        RelStatus = #{relStatus,jdbcType=INTEGER},
      </if>
      <if test="createdDate != null" >
        CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="reportReferenceNote != null" >
        ReportReferenceNote = #{reportReferenceNote,jdbcType=LONGVARCHAR},
      </if>
      <if test="conditionInstructions != null" >
        ConditionInstructions = #{conditionInstructions,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where Id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineCustomerAppRelInfoWithBLOBs" >
    update tre_trims_testline_customer_applicability_relationship
    set TestLineVersionId = #{testLineVersionId,jdbcType=INTEGER},
      CustomerAccountId = #{customerAccountId,jdbcType=INTEGER},
      BizVersionId = #{bizVersionId,jdbcType=CHAR},
      RelStatus = #{relStatus,jdbcType=INTEGER},
      CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      ReportReferenceNote = #{reportReferenceNote,jdbcType=LONGVARCHAR},
      ConditionInstructions = #{conditionInstructions,jdbcType=LONGVARCHAR}
    where Id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineCustomerAppRelInfoPO" >
    update tre_trims_testline_customer_applicability_relationship
    set TestLineVersionId = #{testLineVersionId,jdbcType=INTEGER},
      CustomerAccountId = #{customerAccountId,jdbcType=INTEGER},
      BizVersionId = #{bizVersionId,jdbcType=CHAR},
      RelStatus = #{relStatus,jdbcType=INTEGER},
      CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP}
    where Id = #{id,jdbcType=BIGINT}
  </update>
</mapper>