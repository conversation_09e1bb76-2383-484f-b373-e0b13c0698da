<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.otsnotes.dbstorages.mybatis.mapper.ReportSignerMultipleLanguageMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.otsnotes.dbstorages.mybatis.model.ReportSignerMultipleLanguagePO" >
    <id column="ID" property="ID" jdbcType="VARCHAR" />
    <result column="reportSignerID" property="reportSignerID" jdbcType="VARCHAR" />
    <result column="Displayname" property="displayname" jdbcType="VARCHAR" />
    <result column="CloudID" property="cloudID" jdbcType="VARCHAR" />
    <result column="LanguageId" property="languageId" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    ID, reportSignerID, Displayname, CloudID, LanguageId
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.ReportSignerMultipleLanguageExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_report_signer_multiplelanguage
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from tb_report_signer_multiplelanguage
    where ID = #{ID,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from tb_report_signer_multiplelanguage
    where ID = #{ID,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.ReportSignerMultipleLanguageExample" >
    delete from tb_report_signer_multiplelanguage
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.ReportSignerMultipleLanguagePO" >
    insert into tb_report_signer_multiplelanguage (ID, reportSignerID, Displayname, 
      CloudID, LanguageId)
    values (#{ID,jdbcType=VARCHAR}, #{reportSignerID,jdbcType=VARCHAR}, #{displayname,jdbcType=VARCHAR}, 
      #{cloudID,jdbcType=VARCHAR}, #{languageId,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.ReportSignerMultipleLanguagePO" >
    insert into tb_report_signer_multiplelanguage
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="ID != null" >
        ID,
      </if>
      <if test="reportSignerID != null" >
        reportSignerID,
      </if>
      <if test="displayname != null" >
        Displayname,
      </if>
      <if test="cloudID != null" >
        CloudID,
      </if>
      <if test="languageId != null" >
        LanguageId,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="ID != null" >
        #{ID,jdbcType=VARCHAR},
      </if>
      <if test="reportSignerID != null" >
        #{reportSignerID,jdbcType=VARCHAR},
      </if>
      <if test="displayname != null" >
        #{displayname,jdbcType=VARCHAR},
      </if>
      <if test="cloudID != null" >
        #{cloudID,jdbcType=VARCHAR},
      </if>
      <if test="languageId != null" >
        #{languageId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.ReportSignerMultipleLanguageExample" resultType="java.lang.Integer" >
    select count(*) from tb_report_signer_multiplelanguage
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_report_signer_multiplelanguage
    <set >
      <if test="record.ID != null" >
        ID = #{record.ID,jdbcType=VARCHAR},
      </if>
      <if test="record.reportSignerID != null" >
        reportSignerID = #{record.reportSignerID,jdbcType=VARCHAR},
      </if>
      <if test="record.displayname != null" >
        Displayname = #{record.displayname,jdbcType=VARCHAR},
      </if>
      <if test="record.cloudID != null" >
        CloudID = #{record.cloudID,jdbcType=VARCHAR},
      </if>
      <if test="record.languageId != null" >
        LanguageId = #{record.languageId,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_report_signer_multiplelanguage
    set ID = #{record.ID,jdbcType=VARCHAR},
      reportSignerID = #{record.reportSignerID,jdbcType=VARCHAR},
      Displayname = #{record.displayname,jdbcType=VARCHAR},
      CloudID = #{record.cloudID,jdbcType=VARCHAR},
      LanguageId = #{record.languageId,jdbcType=INTEGER}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.ReportSignerMultipleLanguagePO" >
    update tb_report_signer_multiplelanguage
    <set >
      <if test="reportSignerID != null" >
        reportSignerID = #{reportSignerID,jdbcType=VARCHAR},
      </if>
      <if test="displayname != null" >
        Displayname = #{displayname,jdbcType=VARCHAR},
      </if>
      <if test="cloudID != null" >
        CloudID = #{cloudID,jdbcType=VARCHAR},
      </if>
      <if test="languageId != null" >
        LanguageId = #{languageId,jdbcType=INTEGER},
      </if>
    </set>
    where ID = #{ID,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.ReportSignerMultipleLanguagePO" >
    update tb_report_signer_multiplelanguage
    set reportSignerID = #{reportSignerID,jdbcType=VARCHAR},
      Displayname = #{displayname,jdbcType=VARCHAR},
      CloudID = #{cloudID,jdbcType=VARCHAR},
      LanguageId = #{languageId,jdbcType=INTEGER}
    where ID = #{ID,jdbcType=VARCHAR}
  </update>
</mapper>