# DIG-10175 报告数据复制开关功能 - 提测文档

## 文档信息
- **需求编号**: DIG-10175
- **需求名称**: 报告数据复制开关功能
- **提测版本**: V1.0
- **提测日期**: 2025-09-18
- **开发人员**: wei.qian
- **测试分支**: feature-DIG-10175
- **相关设计文档**: 
  - [DIG-10175 设计文档](./DIG-10175-设计文档.md)
  - [Amend/Split/Rework 分包数据方案](https://sgs-digitalization.feishu.cn/wiki/JOm9w4pPSiTY5wkvP8EcepXGnoh)

---

## 1. 文档资料

### 1.1 需求变更记录
本次为新增功能，无需求变更。功能基于以下背景开发：
- **业务背景**: 基于飞书需求文档《Amend/Split/Rework 分包数据方案》，需要在订单Amend/Split操作时Copy分包数据到新生成的Report
- **技术背景**: 为了解决Buyer Summary多订单关联场景下的数据关联问题，减少外部系统数据关联复杂性
- **配置需求**: 需要提供开关功能，支持动态控制报告数据复制行为

### 1.2 设计文档

#### 1.2.1 系统架构设计

**整体架构图**
```mermaid
graph TB
    subgraph "配置管理层"
        A[配置管理中心]
        B[报告数据复制开关]
    end
    
    subgraph "业务服务层"
        C[订单管理服务]
        D[报告数据服务]
        E[分包管理服务]
    end
    
    subgraph "集成层"
        F[RD系统集成]
        G[StarLIMS集成]
    end
    
    subgraph "数据存储层"
        H[MySQL数据库]
        I[Redis缓存]
    end
    
    A --> B
    B --> C
    B --> D
    B --> E
    C --> F
    D --> F
    E --> G
    C --> H
    D --> H
    E --> H
    D --> I
    
    style B fill:#f9f,stroke:#333,stroke-width:2px
```

#### 1.2.2 模块设计细节
本功能实现基于以下核心模块：

1. **配置常量模块** (`otsnotes-core`)
   - 新增 `GENERAL_SWITCH` 配置组
   - 定义 `ReportDataCopySwitch` 开关常量
   - 支持 `true`/`false` 两种状态值

2. **配置服务模块** (待实现)
   - 基于现有 `BuParamService` 实现
   - 提供开关状态查询和设置接口
   - 支持动态配置更新

3. **业务集成模块** (待实现)
   - 在报告数据处理流程中集成开关检查
   - 支持多种操作场景：Amend-Extract、Split-Sample、Split-Conclusion、Split-TestLine、Amend-Replace、Rework
   - 与RD系统集成实现数据查询和保存

#### 1.2.3 接口设计文档

**配置管理接口**
```java
/**
 * 配置服务接口
 */
public interface ConfigService {
    /**
     * 获取报告数据复制开关状态
     * @return true-开启，false-关闭
     */
    boolean getReportDataCopySwitch();
    
    /**
     * 设置报告数据复制开关状态
     * @param enabled 开关状态
     */
    void setReportDataCopySwitch(boolean enabled);
}
```

**报告数据复制接口**
```java
/**
 * 报告数据服务接口
 */
public interface ReportDataService {
    /**
     * 复制报告数据（新增场景）
     * @param request 复制请求对象
     */
    void copyReportData(ReportDataCopyRequest request);
    
    /**
     * 更新报告数据（Replace场景）
     * @param request 更新请求对象
     */
    void updateReportData(ReportDataUpdateRequest request);
    
    /**
     * 修改报告数据（Rework场景）
     * @param request 修改请求对象
     */
    void modifyReportData(ReportDataModifyRequest request);
}
```

### 1.3 影响范围说明

#### 1.3.1 模块影响范围

##### 1.3.1.1 受影响模块列表
1. **otsnotes-core**: 新增配置常量定义，无破坏性变更
2. **otsnotes-domain**: 需要新增配置服务和业务逻辑集成（待实现）
3. **otsnotes-facade**: 需要新增对外接口支持（待实现）
4. **otsnotes-integration**: 需要集成RD系统接口（待实现）
5. **配置中心**: 需要添加新的配置项

##### 1.3.1.2 模块依赖关系
```mermaid
graph LR
    A[otsnotes-core] --> B[otsnotes-domain]
    B --> C[otsnotes-facade]
    B --> D[otsnotes-integration]
    C --> E[otsnotes-web]
    D --> F[RD系统]
    
    style A fill:#9f9,stroke:#333,stroke-width:2px
    style B fill:#ff9,stroke:#333,stroke-width:2px
    style D fill:#ff9,stroke:#333,stroke-width:2px
```

**依赖关系说明**:
- `otsnotes-core` 提供配置常量，被其他模块引用
- `otsnotes-domain` 实现核心业务逻辑，依赖配置常量
- `otsnotes-integration` 负责与RD系统的数据交互
- 各模块间通过明确的接口契约进行交互

#### 1.3.2 数据影响范围

##### ******* 数据库表变更
**无数据库表结构变更**，只需要添加配置记录：

```sql
-- 添加报告数据复制开关配置
INSERT INTO bu_param (group_code, param_code, param_value, description, created_time, updated_time) 
VALUES ('General', 'ReportDataCopySwitch', 'false', '报告数据复制开关：控制是否允许进行报告数据复制操作', NOW(), NOW());
```

##### ******* 数据迁移或转换
**无需数据迁移**，本功能为新增功能开关，不影响现有数据。
- 默认配置值为 `false`，确保不影响现有业务流程
- 激活开关后，新的数据复制操作将开始生效
- 历史数据保持不变，支持指定时间、指定订单的数据补充

#### 1.3.3 系统功能影响范围

##### ******* 系统功能变更
1. **新增功能**:
   - 报告数据复制开关配置功能
   - 基于开关的业务流程控制功能
   - 支持多种报告操作场景的数据复制

2. **影响的业务流程**:
   - **Amend-Extract**: 修改抽取操作时的数据复制
   - **Split-Sample**: 拆分样品操作时的数据复制
   - **Split-Conclusion**: 拆分结论操作时的数据复制
   - **Split-TestLine**: 拆分测试项操作时的数据复制
   - **Amend-Replace**: 修改替换操作时的数据更新
   - **Rework**: 重工操作时的数据修改

3. **外部系统影响**:
   - **Buyer Summary**: 导出逻辑保持不变
   - **Lululemon报表**: 逻辑保持不变
   - **RD系统**: 需要提供数据查询和保存接口支持
   - **StarLIMS**: 可能需要同步数据复制状态

### 1.4 开发自测报告

#### 1.4.1 自测范围
本次开发主要完成了配置常量的定义，自测覆盖以下内容：
- 配置常量定义的正确性验证
- 配置常量结构的完整性验证
- 代码编译和语法正确性验证

#### 1.4.2 自测用例

| 测试用例ID | 测试描述 | 测试步骤 | 预期结果 | 实际结果 |
|------------|----------|----------|----------|----------|
| TC001 | 验证配置常量定义 | 1. 检查Constants.java文件<br>2. 验证GENERAL_SWITCH接口定义<br>3. 检查REPORT_DATA_COPY_SWITCH常量 | 配置常量正确定义，符合命名规范 | ✅ 通过 |
| TC002 | 验证配置值常量 | 1. 检查VALUES接口<br>2. 验证TRUE/FALSE常量定义 | 配置值常量正确定义 | ✅ 通过 |
| TC003 | 验证代码编译 | 1. 执行Maven编译<br>2. 检查编译错误 | 代码编译成功，无语法错误 | ✅ 通过 |
| TC004 | 验证常量引用 | 1. 尝试引用新增常量<br>2. 验证常量访问路径 | 能够正确引用配置常量 | ✅ 通过 |

#### 1.4.3 自测结果
✅ **开发自测全部通过**
- 配置常量定义正确，符合项目编码规范
- 代码结构清晰，便于后续功能扩展
- 编译无错误，集成测试基础就绪

**注意事项**:
- 当前只完成了配置常量定义，完整功能需要后续迭代实现
- 建议优先实现ConfigService接口，为业务集成提供基础

### 1.5 性能指标文档
本功能主要为配置开关功能，性能指标如下：

| 性能指标 | 目标值 | 说明 |
|----------|--------|------|
| 配置查询响应时间 | < 50ms | 单次配置查询操作 |
| 配置更新响应时间 | < 200ms | 单次配置更新操作 |
| 业务流程影响 | < 10ms | 开关检查对业务流程的影响 |
| 缓存命中率 | > 95% | 配置项缓存命中率 |
| 并发支持 | 1000/s | 配置查询并发请求支持 |

---

## 2. 单元测试

### 2.1 单元测试
**当前状态**: 配置常量定义阶段，单元测试将在后续实现阶段补充

**计划测试内容**:
- ConfigService接口实现的单元测试
- 配置值读取和设置的测试用例
- 异常场景处理的测试覆盖
- Mock外部依赖的集成测试

### 2.2 单元测试通过率
**目标**: 单元测试覆盖率 > 80%，通过率 100%

---

## 3. 代码与版本信息

### 3.1 代码分支
- **仓库**: otsnotes-service
- **分支名称**: `feature-DIG-10175`
- **最新提交**: `f0f9dc0699 feat(otsnotes): 添加报告数据复制开关功能`
- **提交作者**: wei.qian <<EMAIL>>
- **提交时间**: 2025-09-17 17:18:34

### 3.2 代码审查记录

#### 3.2.1 审查人员
- **开发人员**: wei.qian
- **审查人员**: 待指定
- **架构审查**: 待进行

#### 3.2.2 审查意见
**当前状态**: 等待Code Review

**自查结果**:
- ✅ 代码符合项目编码规范
- ✅ 配置常量定义清晰合理
- ✅ 注释完整，便于理解
- ⚠️ 需要补充完整的功能实现
- ⚠️ 需要添加单元测试

---

## 4. 其他事项

### 4.1 风险提示

#### 4.1.1 技术风险
1. **功能完整性风险**
   - **风险描述**: 当前只实现了配置常量定义，完整功能链路尚未实现
   - **影响评估**: 高风险
   - **缓解措施**: 分阶段实施，优先实现核心配置服务

2. **外部依赖风险**
   - **风险描述**: 依赖RD系统提供数据接口支持
   - **影响评估**: 中风险
   - **缓解措施**: 与RD团队提前对接，明确接口需求

3. **数据一致性风险**
   - **风险描述**: 开关状态变更可能影响数据复制行为的一致性
   - **影响评估**: 中风险
   - **缓解措施**: 实现配置变更通知机制，确保及时生效

#### 4.1.2 业务风险
1. **向后兼容性风险**
   - **风险描述**: 新功能激活可能影响现有业务流程
   - **影响评估**: 低风险
   - **缓解措施**: 默认关闭开关，渐进式启用

2. **性能风险**
   - **风险描述**: 数据复制操作可能增加系统负载
   - **影响评估**: 中风险
   - **缓解措施**: 实施性能监控，优化复制算法

### 4.2 特殊说明

#### 4.2.1 实现阶段说明
本次提测为**第一阶段**实现，包含：
- ✅ 配置常量定义
- ⚠️ 配置服务实现（待开发）
- ⚠️ 业务逻辑集成（待开发）
- ⚠️ RD系统集成（待开发）

#### 4.2.2 后续实现计划
1. **阶段二**: 实现ConfigService接口和基础配置管理
2. **阶段三**: 实现业务逻辑集成和开关检查
3. **阶段四**: 实现RD系统集成和数据复制功能
4. **阶段五**: 集成测试和性能优化

#### 4.2.3 测试重点建议
1. **配置功能测试**
   - 配置项的读取和设置功能
   - 配置缓存和刷新机制
   - 配置权限控制

2. **业务流程测试**
   - 各种操作场景下的开关控制效果
   - 开关状态变更对正在进行业务的影响
   - 异常场景下的降级处理

3. **集成测试**
   - 与RD系统的接口对接
   - 数据复制的正确性验证
   - 性能和稳定性测试

#### 4.2.4 部署注意事项
1. **配置初始化**
   - 确保在所有环境中添加必要的配置项
   - 验证配置中心的连通性
   - 确认默认配置值的正确性

2. **监控告警**
   - 配置开关状态变更监控
   - 数据复制操作的性能监控
   - 异常情况的告警机制

3. **回滚准备**
   - 准备配置回滚脚本
   - 确保能够快速禁用新功能
   - 建立应急处理流程

---

## 5. 测试验收标准

### 5.1 功能验收标准
1. ✅ 配置常量定义正确，符合规范
2. ⚠️ 配置服务接口实现完整（待实现）
3. ⚠️ 业务流程集成正确（待实现）
4. ⚠️ 外部系统集成稳定（待实现）

### 5.2 性能验收标准
1. 配置查询响应时间 < 50ms
2. 配置更新响应时间 < 200ms
3. 业务流程影响 < 10ms
4. 系统稳定性无明显下降

### 5.3 安全验收标准
1. 配置修改权限控制完善
2. 配置变更审计日志完整
3. 敏感信息保护到位
4. 系统安全性无回归

---

**提测总结**: 本次提测为DIG-10175功能的第一阶段实现，主要完成了配置常量的定义工作。后续将分阶段实现完整的报告数据复制开关功能，建议测试团队重点关注配置管理和业务流程集成的测试设计。