package com.sgs.otsnotes.dbstorages.mybatis.model;

import java.util.Date;

public class TestSampleGroupInfoPO {
    /**
     * ID VARCHAR(36) 必填<br>
     * ID,Primary key
     */
    private String ID;

    /**
     * SampleGroupID VARCHAR(36)<br>
     * 
     */
    private String sampleGroupID;

    /**
     * SampleID VARCHAR(36)<br>
     * FK tb_TestSample
     */
    private String sampleID;

    /**
     * mainMaterialFlag BIT 默认值[1] 必填<br>
     * 标记是否为主测试样
     */
    private Boolean mainMaterialFlag;

    /**
     * 记录Group样品的添加顺序
     */
    private Integer sequence;

    /**
     * ActiveIndicator BIT 默认值[1] 必填<br>
     * 0: inactive, 1: active
     */
    private Boolean activeIndicator;

    /**
     * CreatedDate TIMESTAMP(19)<br>
     * CreatedDate
     */
    private Date createdDate;

    /**
     * CreatedBy VARCHAR(50)<br>
     * CreatedBy
     */
    private String createdBy;

    /**
     * ModifiedDate TIMESTAMP(19)<br>
     * ModifiedDate
     */
    private Date modifiedDate;

    /**
     * ModifiedBy VARCHAR(50)<br>
     * ModifiedBy
     */
    private String modifiedBy;

    /**
     * ID VARCHAR(36) 必填<br>
     * 获得 ID,Primary key
     */
    public String getID() {
        return ID;
    }

    /**
     * ID VARCHAR(36) 必填<br>
     * 设置 ID,Primary key
     */
    public void setID(String ID) {
        this.ID = ID == null ? null : ID.trim();
    }

    /**
     * SampleGroupID VARCHAR(36)<br>
     * 获得 
     */
    public String getSampleGroupID() {
        return sampleGroupID;
    }

    /**
     * SampleGroupID VARCHAR(36)<br>
     * 设置 
     */
    public void setSampleGroupID(String sampleGroupID) {
        this.sampleGroupID = sampleGroupID == null ? null : sampleGroupID.trim();
    }

    /**
     * SampleID VARCHAR(36)<br>
     * 获得 FK tb_TestSample
     */
    public String getSampleID() {
        return sampleID;
    }

    /**
     * SampleID VARCHAR(36)<br>
     * 设置 FK tb_TestSample
     */
    public void setSampleID(String sampleID) {
        this.sampleID = sampleID == null ? null : sampleID.trim();
    }

    public Boolean getMainMaterialFlag() {
        return mainMaterialFlag;
    }

    public void setMainMaterialFlag(Boolean mainMaterialFlag) {
        this.mainMaterialFlag = mainMaterialFlag;
    }

    public Integer getSequence() {
        return sequence;
    }

    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }

    /**
     * ActiveIndicator BIT 默认值[1] 必填<br>
     * 获得 0: inactive, 1: active
     */
    public Boolean getActiveIndicator() {
        return activeIndicator;
    }

    /**
     * ActiveIndicator BIT 默认值[1] 必填<br>
     * 设置 0: inactive, 1: active
     */
    public void setActiveIndicator(Boolean activeIndicator) {
        this.activeIndicator = activeIndicator;
    }

    /**
     * CreatedDate TIMESTAMP(19)<br>
     * 获得 CreatedDate
     */
    public Date getCreatedDate() {
        return createdDate;
    }

    /**
     * CreatedDate TIMESTAMP(19)<br>
     * 设置 CreatedDate
     */
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    /**
     * CreatedBy VARCHAR(50)<br>
     * 获得 CreatedBy
     */
    public String getCreatedBy() {
        return createdBy;
    }

    /**
     * CreatedBy VARCHAR(50)<br>
     * 设置 CreatedBy
     */
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    /**
     * ModifiedDate TIMESTAMP(19)<br>
     * 获得 ModifiedDate
     */
    public Date getModifiedDate() {
        return modifiedDate;
    }

    /**
     * ModifiedDate TIMESTAMP(19)<br>
     * 设置 ModifiedDate
     */
    public void setModifiedDate(Date modifiedDate) {
        this.modifiedDate = modifiedDate;
    }

    /**
     * ModifiedBy VARCHAR(50)<br>
     * 获得 ModifiedBy
     */
    public String getModifiedBy() {
        return modifiedBy;
    }

    /**
     * ModifiedBy VARCHAR(50)<br>
     * 设置 ModifiedBy
     */
    public void setModifiedBy(String modifiedBy) {
        this.modifiedBy = modifiedBy == null ? null : modifiedBy.trim();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", ID=").append(ID);
        sb.append(", sampleGroupID=").append(sampleGroupID);
        sb.append(", sampleID=").append(sampleID);
        sb.append(", activeIndicator=").append(activeIndicator);
        sb.append(", createdDate=").append(createdDate);
        sb.append(", createdBy=").append(createdBy);
        sb.append(", modifiedDate=").append(modifiedDate);
        sb.append(", modifiedBy=").append(modifiedBy);
        sb.append("]");
        return sb.toString();
    }
}