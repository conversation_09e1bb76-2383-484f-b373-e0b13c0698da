# TestLine对象状态管理规则文档

## 文档概述

本文档详细梳理了TestLine对象在系统中的状态管理规则，包括状态定义、状态转换条件、触发动作和业务规则。

**【事实】** 基于以下核心文件分析：
- `TestLineStatus.java` - 状态枚举定义
- `TestLineModuleType.java` - 模块类型定义
- `TestLineStatusService.java` - 状态管理核心服务
- `ActionTestLineStatusMatrixEnum.java` - 操作权限矩阵
- `TestLineStatusManager.java` - 状态检查管理器

## TestLine状态定义

### 状态枚举表

| 状态值 | 状态名称 | 英文描述 | 中文含义 | 业务说明 |
|--------|----------|----------|----------|----------|
| 701 | Typing | Typing | 录入中 | 正在进行数据录入，可编辑测试数据 |
| 702 | Submit | Submitted | 已提交 | 数据已提交，等待验证 |
| 703 | Completed | Completed | 已完成 | 测试已验证完成，可生成报告 |
| 704 | SubContracted | Subcontracted | 已分包 | 测试线已分包给外部实验室 |
| 705 | Entered | Entered | 已录入 | 测试线已创建并录入系统，等待数据录入 |
| 706 | Cancelled | Cancelled | 已取消 | 测试线已被取消，不再执行 |
| 707 | NC | Not Test | 不测试 | 标记为不需要测试 |
| 708 | DR | Document Review | 待复核 | 数据录入完成，等待复核 |
| 709 | NA | NA | 不适用 | 不适用的测试项目 |

## 状态转换规则总览表

| 模块操作类型 | 操作说明 | 源状态 | 目标状态 | 触发条件 | 关联对象影响 | 外部系统联动 |
|-------------|----------|--------|----------|----------|-------------|-------------|
| **DataEntrySave** | 数据录入保存 | 任意 | Entered(705) | 测试线有效，有录入权限 | **TestMatrix**: 状态同步为Entered<br/>**TestData**: 数据状态更新为可编辑<br/>**ReportMatrix**: 关联状态更新 | 无 |
| **DataEntrySumbit** | 数据录入提交 | 任意 | Submit(702) | 数据完整，有提交权限 | **TestMatrix**: 状态同步为Submit<br/>**Order**: 可能触发订单状态检查<br/>**Report**: 报告状态可能变更<br/>**TestData**: 数据锁定为只读 | 无 |
| **Validate** | 验证完成 | Submit | Completed(703) | 数据验证通过，有验证权限 | **TestMatrix**: 状态同步为Completed<br/>**Order**: 订单完成度检查<br/>**Report**: 报告生成准备<br/>**SubContract**: 分包状态更新<br/>**Conclusion**: 结论状态确认 | 可能触发订单状态变更 |
| **Change** | 一般变更 | Entered/Submit/Completed | Entered(705) | 有变更权限，报告未发布 | **TestMatrix**: 状态回退为Entered<br/>**TestData**: 数据重新编辑<br/>**Conclusion**: 结论状态重置<br/>**Report**: 报告状态可能回退 | 无 |
| **ChangeUpdateCondition** | 更新测试条件 | Submit/Completed | Entered(705) | 有条件更新权限 | **TestMatrix**: 状态回退为Entered<br/>**TestCondition**: 条件信息更新<br/>**TestData**: 数据重新验证<br/>**Order**: 订单状态同步<br/>**SubContract**: 分包状态更新 | 订单状态同步，分包状态更新 |
| **ChangeUpdateStandard** | 更新测试标准 | Submit/Completed | Entered(705) | 有标准更新权限 | **TestMatrix**: 状态回退为Entered<br/>**TestStandard**: 标准信息更新<br/>**Limit**: 限值重新计算<br/>**Order**: 订单状态同步<br/>**SubContract**: 分包状态更新 | 订单状态同步，分包状态更新 |
| **ChangeAddMatrix** | 添加测试矩阵 | Submit/Completed | Entered(705) | 有矩阵添加权限 | **TestMatrix**: 新增Matrix记录<br/>**ReportMatrix**: 新增报告关联<br/>**TestSample**: 样品分配关系<br/>**Order**: 订单状态同步<br/>**SubContract**: 分包状态更新 | 订单状态同步，分包状态更新 |
| **ReTest** | 重新测试 | 任意 | Entered(705) | 有重测权限，重测原因合理 | **TestMatrix**: 状态重置为Entered<br/>**TestData**: 数据清空或标记重测<br/>**Conclusion**: 结论重置<br/>**TestRemark**: 重测原因记录<br/>**Report**: 报告状态可能回退 | 无 |
| **CancelTestLine** | 取消测试线 | 非NC/非Cancelled | Cancelled(706) | 有取消权限，报告状态允许 | **TestMatrix**: 状态同步为Cancelled<br/>**Job**: 相关Job状态更新<br/>**Order**: 订单完成度重新计算<br/>**SubContract**: 分包状态更新<br/>**Report**: 报告内容调整 | Job状态更新 |
| **NCTestLine** | 标记不测试 | Typing | NC(707) | 有NC权限，NC原因明确，未分配样品 | **TestMatrix**: 状态同步为NC<br/>**TestData**: 数据标记为不需要<br/>**Order**: 订单完成度重新计算<br/>**Report**: 报告内容调整<br/>**TestRemark**: NC原因记录 | 无 |
| **ChangeCancelMatrix** | 取消/恢复矩阵 | 任意 | 动态决定 | Matrix状态有效，有取消权限 | **TestMatrix**: activeIndicator状态切换<br/>**TestData**: 数据有效性标记<br/>**ReportMatrix**: 报告关联状态更新<br/>**TestSample**: 样品分配状态调整 | 无 |
| **PPSummaryDataEntry** | PP汇总数据录入 | 任意 | Entered(705) | PP类型测试线，有录入权限 | **TestMatrix**: PP Matrix状态同步<br/>**PPTestLine**: PP关系更新<br/>**TestData**: PP汇总数据更新<br/>**PPSampleRel**: PP样品关系维护 | 无 |

## 详细状态转换规则

### 1. 数据录入相关操作

#### 1.1 数据录入保存 (DataEntrySave)
- **对象**: TestLine
- **状态**: 任意状态 → Entered (705)
- **触发动作**: 保存测试数据，更新TestLine状态为Entered
- **触发规则**: 
  - 测试线必须存在且有效
  - 用户有数据录入权限
  - 测试线未被取消或NC

#### 1.2 数据录入提交 (DataEntrySumbit)
- **对象**: TestLine
- **状态**: 任意状态 → Submit (702)
- **触发动作**: 提交测试数据，更新TestLine状态为Submit
- **触发规则**:
  - 测试线必须存在且有效
  - 测试数据已完整录入
  - 用户有提交权限

#### 1.3 PP汇总数据录入 (PPSummaryDataEntry)
- **对象**: TestLine
- **状态**: 任意状态 → Entered (705)
- **触发动作**: PP汇总数据录入，重置状态为Entered
- **触发规则**:
  - 测试线类型为PP相关
  - 用户有PP数据录入权限

### 2. 验证相关操作

#### 2.1 验证完成 (Validate)
- **对象**: TestLine
- **状态**: Submit (702) → Completed (703)
- **触发动作**: 
  - 更新TestLine状态为Completed
  - 更新Matrix状态
  - 可能触发订单状态变更为Reporting
- **触发规则**:
  - 测试线状态必须为Submit
  - 所有必要的测试数据已提交
  - 验证人员有验证权限
  - 数据验证通过

### 3. 变更相关操作

#### 3.1 一般变更 (Change)
- **对象**: TestLine
- **状态**: Entered/Submit/Completed → Entered (705)
- **触发动作**:
  - 更新TestLine状态为Entered
  - 更新Matrix状态
  - 记录原始状态
- **触发规则**:
  - 当前状态必须为Entered、Submit或Completed
  - 用户有变更权限
  - 报告未发布

#### 3.2 更新测试条件 (ChangeUpdateCondition)
- **对象**: TestLine
- **状态**: Submit/Completed → Entered (705)
- **触发动作**:
  - 更新TestLine状态为Entered
  - 如果原状态为Submit或Completed，触发订单状态从Reporting变为Testing
  - 如果是分包测试线，更新分包状态
- **触发规则**:
  - 测试线状态为Submit或Completed时才触发外部系统同步
  - 用户有条件更新权限
  - 测试条件变更合理

#### 3.3 更新测试标准 (ChangeUpdateStandard)
- **对象**: TestLine
- **状态**: Submit/Completed → Entered (705)
- **触发动作**: 同ChangeUpdateCondition
- **触发规则**: 同ChangeUpdateCondition

#### 3.4 添加测试矩阵 (ChangeAddMatrix)
- **对象**: TestLine
- **状态**: Submit/Completed → Entered (705)
- **触发动作**: 同ChangeUpdateCondition
- **触发规则**: 同ChangeUpdateCondition

### 4. 取消相关操作

#### 4.1 取消测试线 (CancelTestLine)
- **对象**: TestLine
- **状态**: 任意状态 → Cancelled (706)
- **触发动作**:
  - 更新TestLine状态为Cancelled
  - 取消相关Matrix
  - 删除分包关联
  - 可能影响Job状态
- **触发规则**:
  - 测试线未处于NC或已取消状态
  - 用户有取消权限
  - 报告状态允许取消

#### 4.2 取消矩阵 (ChangeCancelMatrix)
- **对象**: TestLine
- **状态**: 根据Matrix状态决定
- **触发动作**:
  - 如果Matrix为Cancelled，恢复TestLine原状态
  - 如果Matrix非Cancelled，设置为Cancelled
  - 更新ActiveIndicator
- **触发规则**:
  - Matrix状态有效
  - 用户有取消权限

### 5. 重测相关操作

#### 5.1 重新测试 (ReTest)
- **对象**: TestLine
- **状态**: 任意状态 → Entered (705)
- **触发动作**:
  - 重置TestLine状态为Entered
  - 清空验证信息
  - 保留测试数据文件
- **触发规则**:
  - 测试线存在且有效
  - 用户有重测权限
  - 重测原因合理

### 6. NC相关操作

#### 6.1 标记为不测试 (NCTestLine)
- **对象**: TestLine
- **状态**: 任意状态 → NC (707)
- **触发动作**:
  - 更新TestLine状态为NC
  - 更新测试线备注
- **触发规则**:
  - 测试线状态为Typing时允许NC操作
  - 用户有NC权限
  - NC原因明确

## 操作权限矩阵

### 基于状态的操作权限

| 操作类型 | 允许的状态 | 说明 |
|----------|------------|------|
| 分配样品 (ASSIGN_SAMPLE) | Typing, DR | 只能在录入中或待复核状态分配样品 |
| 更新标准 (UPDATE_STANDARD) | Typing, DR | 只能在录入中或待复核状态更新标准 |
| 更新条件 (UPDATE_CONDITION) | Typing, DR | 只能在录入中或待复核状态更新条件 |
| 确认条件 (CONFIRM_CONDITION) | Typing, DR | 只能在录入中或待复核状态确认条件 |
| 删除测试线 (DELETE_TL) | Typing, NC, DR | 只能删除录入中、不测试或待复核的测试线 |
| 取消测试线 (CANCEL_TL) | Typing, NC, DR | 只能取消录入中、不测试或待复核的测试线 |
| 标记NC (NC_TL) | Typing | 只能在录入中状态标记为不测试 |
| 复制测试 (COPY_TEST) | Typing | 只能在录入中状态复制测试 |
| 删除测试 (DEL_TEST) | Typing | 只能在录入中状态删除测试 |
| 变更 (CHANGE) | Entered, Submit, Completed | 只能变更已录入、已提交或已完成的测试线 |

## 外部系统联动

### 1. 订单状态同步
- **触发条件**: ChangeUpdateCondition/ChangeUpdateStandard/ChangeAddMatrix操作且原状态为Submit或Completed
- **同步动作**: 订单状态从Reporting变更为Testing
- **实现方式**: 调用StatusClient.insertStatusInfo

### 2. 分包状态同步
- **触发条件**: 变更操作涉及分包类型测试线
- **同步动作**: 更新分包状态为1（激活状态）
- **实现方式**: 调用SubContractMapper.updateSubContractStatus

### 3. 报告状态影响
- **影响场景**: 测试线状态变更可能影响报告生成和发布
- **处理逻辑**: 状态回退时可能需要重新计算报告或标记报告需要重新生成

## 数据库事务处理

所有状态变更操作都在数据库事务中执行，确保数据一致性：

1. **TestLine状态更新**: 更新test_line_instance表的test_line_status字段
2. **Matrix状态更新**: 更新test_matrix表的matrix_status字段  
3. **订单状态同步**: 调用外部状态服务
4. **分包状态更新**: 更新分包相关表

**【推理】** 事务失败时所有操作回滚，保证系统数据一致性。

## 业务约束规则

### 1. 状态转换约束
- 已取消的测试线不能进行任何状态变更
- NC状态的测试线只能取消或删除
- 已完成的测试线变更需要特殊权限

### 2. 数据完整性约束
- 状态变更时必须记录操作人和操作时间
- 重要状态变更需要记录原始状态
- 状态变更需要有明确的业务原因

### 3. 权限控制约束
- 不同操作需要不同的用户权限
- 状态检查在业务操作前执行
- 权限验证失败时操作被拒绝

## 状态转换流程图

### 标准测试流程状态转换

```mermaid
stateDiagram-v2
    [*] --> Entered : 创建测试线
    Entered --> Typing : 开始数据录入
    Typing --> Submit : 数据录入完成提交
    Submit --> Completed : 验证通过
    Completed --> [*] : 测试完成

    %% 变更流程
    Submit --> Entered : Change/UpdateCondition/UpdateStandard
    Completed --> Entered : Change/UpdateCondition/UpdateStandard

    %% 重测流程
    Typing --> Entered : ReTest
    Submit --> Entered : ReTest
    Completed --> Entered : ReTest

    %% 取消流程
    Typing --> Cancelled : Cancel
    Submit --> Cancelled : Cancel
    Completed --> Cancelled : Cancel
    Entered --> Cancelled : Cancel

    %% NC流程
    Typing --> NC : 标记不测试

    %% DR流程
    Typing --> DR : 文档复核
    DR --> Submit : 复核完成
    DR --> Typing : 复核退回
```

### 分包测试流程状态转换

```mermaid
stateDiagram-v2
    [*] --> Entered : 创建分包测试线
    Entered --> SubContracted : 分包处理
    SubContracted --> Completed : 分包完成
    Completed --> [*] : 测试完成

    %% 分包变更流程
    SubContracted --> Entered : 分包变更
    Completed --> Entered : 分包变更
```

## 详细业务场景示例

### 场景1: 标准数据录入流程

**业务描述**: 测试人员进行标准的数据录入和提交流程

**状态变更序列**:
1. **初始状态**: Entered (705) - 测试线已创建
2. **开始录入**: Typing (701) - 测试人员开始录入数据
3. **提交数据**: Submit (702) - 数据录入完成并提交
4. **验证完成**: Completed (703) - 验证人员验证通过

**关键触发条件**:
- 步骤2→3: 数据完整性检查通过
- 步骤3→4: 验证人员确认数据准确性

### 场景2: 测试条件变更流程

**业务描述**: 在测试过程中需要修改测试条件

**状态变更序列**:
1. **当前状态**: Submit (702) 或 Completed (703)
2. **触发变更**: ChangeUpdateCondition操作
3. **状态回退**: Entered (705)
4. **外部同步**: 订单状态 Reporting → Testing

**关键业务规则**:
- 只有Submit或Completed状态才触发订单状态同步
- 分包测试线同时更新分包状态
- 变更原因必须记录

### 场景3: 测试线取消流程

**业务描述**: 由于业务需要取消某个测试线

**状态变更序列**:
1. **当前状态**: 任意有效状态（非NC、非Cancelled）
2. **执行取消**: CancelTestLine操作
3. **最终状态**: Cancelled (706)
4. **关联处理**:
   - 取消相关Matrix
   - 删除分包关联
   - 更新Job状态

**业务约束**:
- 已NC或已取消的测试线不能再次取消
- 需要有取消权限
- 报告状态必须允许取消

## 异常处理机制

### 1. 状态检查失败
- **异常场景**: 当前状态不允许执行请求的操作
- **处理方式**: 返回错误信息，操作被拒绝
- **错误示例**: "当前状态为Completed，不允许执行NC操作"

### 2. 权限验证失败
- **异常场景**: 用户没有执行特定操作的权限
- **处理方式**: 权限检查失败，操作被阻止
- **错误示例**: "用户无验证权限，不能执行Validate操作"

### 3. 数据完整性检查失败
- **异常场景**: 状态变更时数据不完整或不一致
- **处理方式**: 事务回滚，保持原状态
- **错误示例**: "测试数据不完整，不能提交"

### 4. 外部系统同步失败
- **异常场景**: 订单状态或分包状态同步失败
- **处理方式**: 整个事务回滚，确保数据一致性
- **错误示例**: "订单状态同步失败，操作已回滚"

## 监控和日志

### 1. 状态变更日志
- **记录内容**: 操作类型、原状态、新状态、操作人、操作时间
- **日志级别**: INFO级别记录正常状态变更
- **格式示例**: `"LogID:{} TestLine状态变更: {} -> {}, 操作人: {}, 操作类型: {}"`

### 2. 异常监控
- **监控指标**: 状态变更失败率、权限验证失败次数
- **告警机制**: 异常率超过阈值时触发告警
- **处理建议**: 及时排查异常原因，优化业务流程

### 3. 性能监控
- **监控指标**: 状态变更响应时间、数据库事务执行时间
- **优化建议**: 批量操作优化、数据库索引优化

## 最佳实践建议

### 1. 开发实践
- **状态检查**: 在业务操作前必须进行状态检查
- **事务管理**: 相关操作必须在同一事务中执行
- **错误处理**: 提供清晰的错误信息和处理建议

### 2. 运维实践
- **状态监控**: 定期检查异常状态的测试线
- **数据清理**: 定期清理长期处于中间状态的数据
- **性能优化**: 监控状态变更操作的性能表现

### 3. 业务实践
- **权限管理**: 合理分配不同角色的操作权限
- **流程规范**: 建立标准的测试流程和变更流程
- **培训指导**: 为用户提供状态管理的培训和指导

---

**文档生成时间**: 2025-09-28
**基于代码版本**: 当前项目代码
**维护责任**: 测试线管理模块开发团队
