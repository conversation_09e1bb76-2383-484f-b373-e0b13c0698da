/**
 * GetTemplatePreviewImagesResponse.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class GetTemplatePreviewImagesResponse  implements java.io.Serializable {
    private String[] getTemplatePreviewImagesResult;

    public GetTemplatePreviewImagesResponse() {
    }

    public GetTemplatePreviewImagesResponse(
           String[] getTemplatePreviewImagesResult) {
           this.getTemplatePreviewImagesResult = getTemplatePreviewImagesResult;
    }


    /**
     * Gets the getTemplatePreviewImagesResult value for this GetTemplatePreviewImagesResponse.
     * 
     * @return getTemplatePreviewImagesResult
     */
    public String[] getGetTemplatePreviewImagesResult() {
        return getTemplatePreviewImagesResult;
    }


    /**
     * Sets the getTemplatePreviewImagesResult value for this GetTemplatePreviewImagesResponse.
     * 
     * @param getTemplatePreviewImagesResult
     */
    public void setGetTemplatePreviewImagesResult(String[] getTemplatePreviewImagesResult) {
        this.getTemplatePreviewImagesResult = getTemplatePreviewImagesResult;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof GetTemplatePreviewImagesResponse)) {
            return false;
        }
        GetTemplatePreviewImagesResponse other = (GetTemplatePreviewImagesResponse) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.getTemplatePreviewImagesResult==null && other.getGetTemplatePreviewImagesResult()==null) || 
             (this.getTemplatePreviewImagesResult!=null &&
              java.util.Arrays.equals(this.getTemplatePreviewImagesResult, other.getGetTemplatePreviewImagesResult())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getGetTemplatePreviewImagesResult() != null) {
            for (int i=0;
                 i<java.lang.reflect.Array.getLength(getGetTemplatePreviewImagesResult());
                 i++) {
                Object obj = java.lang.reflect.Array.get(getGetTemplatePreviewImagesResult(), i);
                if (obj != null &&
                    !obj.getClass().isArray()) {
                    _hashCode += obj.hashCode();
                }
            }
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(GetTemplatePreviewImagesResponse.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", ">GetTemplatePreviewImagesResponse"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("getTemplatePreviewImagesResult");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "GetTemplatePreviewImagesResult"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        elemField.setItemQName(new javax.xml.namespace.QName("http://ots.sgs.com/", "string"));
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
