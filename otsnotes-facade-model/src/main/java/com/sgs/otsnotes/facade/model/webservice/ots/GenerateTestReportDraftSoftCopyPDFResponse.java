/**
 * GenerateTestReportDraftSoftCopyPDFResponse.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class GenerateTestReportDraftSoftCopyPDFResponse  implements java.io.Serializable {
    private boolean generateTestReportDraftSoftCopyPDFResult;

    private String[] errMsgs;

    public GenerateTestReportDraftSoftCopyPDFResponse() {
    }

    public GenerateTestReportDraftSoftCopyPDFResponse(
           boolean generateTestReportDraftSoftCopyPDFResult,
           String[] errMsgs) {
           this.generateTestReportDraftSoftCopyPDFResult = generateTestReportDraftSoftCopyPDFResult;
           this.errMsgs = errMsgs;
    }


    /**
     * Gets the generateTestReportDraftSoftCopyPDFResult value for this GenerateTestReportDraftSoftCopyPDFResponse.
     * 
     * @return generateTestReportDraftSoftCopyPDFResult
     */
    public boolean isGenerateTestReportDraftSoftCopyPDFResult() {
        return generateTestReportDraftSoftCopyPDFResult;
    }


    /**
     * Sets the generateTestReportDraftSoftCopyPDFResult value for this GenerateTestReportDraftSoftCopyPDFResponse.
     * 
     * @param generateTestReportDraftSoftCopyPDFResult
     */
    public void setGenerateTestReportDraftSoftCopyPDFResult(boolean generateTestReportDraftSoftCopyPDFResult) {
        this.generateTestReportDraftSoftCopyPDFResult = generateTestReportDraftSoftCopyPDFResult;
    }


    /**
     * Gets the errMsgs value for this GenerateTestReportDraftSoftCopyPDFResponse.
     * 
     * @return errMsgs
     */
    public String[] getErrMsgs() {
        return errMsgs;
    }


    /**
     * Sets the errMsgs value for this GenerateTestReportDraftSoftCopyPDFResponse.
     * 
     * @param errMsgs
     */
    public void setErrMsgs(String[] errMsgs) {
        this.errMsgs = errMsgs;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof GenerateTestReportDraftSoftCopyPDFResponse)) {
            return false;
        }
        GenerateTestReportDraftSoftCopyPDFResponse other = (GenerateTestReportDraftSoftCopyPDFResponse) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            this.generateTestReportDraftSoftCopyPDFResult == other.isGenerateTestReportDraftSoftCopyPDFResult() &&
            ((this.errMsgs==null && other.getErrMsgs()==null) || 
             (this.errMsgs!=null &&
              java.util.Arrays.equals(this.errMsgs, other.getErrMsgs())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        _hashCode += (isGenerateTestReportDraftSoftCopyPDFResult() ? Boolean.TRUE : Boolean.FALSE).hashCode();
        if (getErrMsgs() != null) {
            for (int i=0;
                 i<java.lang.reflect.Array.getLength(getErrMsgs());
                 i++) {
                Object obj = java.lang.reflect.Array.get(getErrMsgs(), i);
                if (obj != null &&
                    !obj.getClass().isArray()) {
                    _hashCode += obj.hashCode();
                }
            }
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(GenerateTestReportDraftSoftCopyPDFResponse.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", ">GenerateTestReportDraftSoftCopyPDFResponse"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("generateTestReportDraftSoftCopyPDFResult");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "GenerateTestReportDraftSoftCopyPDFResult"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "boolean"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("errMsgs");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "errMsgs"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        elemField.setItemQName(new javax.xml.namespace.QName("http://ots.sgs.com/", "string"));
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
