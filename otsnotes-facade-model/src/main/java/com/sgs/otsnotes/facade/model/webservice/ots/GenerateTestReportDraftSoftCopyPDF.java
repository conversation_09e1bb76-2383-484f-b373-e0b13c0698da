/**
 * GenerateTestReportDraftSoftCopyPDF.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class GenerateTestReportDraftSoftCopyPDF  implements java.io.Serializable {
    private TemplateInstanceInfo reportInfo;

    private String outputPdfFilePath;

    private String waterMarkText;

    public GenerateTestReportDraftSoftCopyPDF() {
    }

    public GenerateTestReportDraftSoftCopyPDF(
           TemplateInstanceInfo reportInfo,
           String outputPdfFilePath,
           String waterMarkText) {
           this.reportInfo = reportInfo;
           this.outputPdfFilePath = outputPdfFilePath;
           this.waterMarkText = waterMarkText;
    }


    /**
     * Gets the reportInfo value for this GenerateTestReportDraftSoftCopyPDF.
     * 
     * @return reportInfo
     */
    public TemplateInstanceInfo getReportInfo() {
        return reportInfo;
    }


    /**
     * Sets the reportInfo value for this GenerateTestReportDraftSoftCopyPDF.
     * 
     * @param reportInfo
     */
    public void setReportInfo(TemplateInstanceInfo reportInfo) {
        this.reportInfo = reportInfo;
    }


    /**
     * Gets the outputPdfFilePath value for this GenerateTestReportDraftSoftCopyPDF.
     * 
     * @return outputPdfFilePath
     */
    public String getOutputPdfFilePath() {
        return outputPdfFilePath;
    }


    /**
     * Sets the outputPdfFilePath value for this GenerateTestReportDraftSoftCopyPDF.
     * 
     * @param outputPdfFilePath
     */
    public void setOutputPdfFilePath(String outputPdfFilePath) {
        this.outputPdfFilePath = outputPdfFilePath;
    }


    /**
     * Gets the waterMarkText value for this GenerateTestReportDraftSoftCopyPDF.
     * 
     * @return waterMarkText
     */
    public String getWaterMarkText() {
        return waterMarkText;
    }


    /**
     * Sets the waterMarkText value for this GenerateTestReportDraftSoftCopyPDF.
     * 
     * @param waterMarkText
     */
    public void setWaterMarkText(String waterMarkText) {
        this.waterMarkText = waterMarkText;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof GenerateTestReportDraftSoftCopyPDF)) {
            return false;
        }
        GenerateTestReportDraftSoftCopyPDF other = (GenerateTestReportDraftSoftCopyPDF) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.reportInfo==null && other.getReportInfo()==null) || 
             (this.reportInfo!=null &&
              this.reportInfo.equals(other.getReportInfo()))) &&
            ((this.outputPdfFilePath==null && other.getOutputPdfFilePath()==null) || 
             (this.outputPdfFilePath!=null &&
              this.outputPdfFilePath.equals(other.getOutputPdfFilePath()))) &&
            ((this.waterMarkText==null && other.getWaterMarkText()==null) || 
             (this.waterMarkText!=null &&
              this.waterMarkText.equals(other.getWaterMarkText())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getReportInfo() != null) {
            _hashCode += getReportInfo().hashCode();
        }
        if (getOutputPdfFilePath() != null) {
            _hashCode += getOutputPdfFilePath().hashCode();
        }
        if (getWaterMarkText() != null) {
            _hashCode += getWaterMarkText().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(GenerateTestReportDraftSoftCopyPDF.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", ">GenerateTestReportDraftSoftCopyPDF"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("reportInfo");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "reportInfo"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "TemplateInstanceInfo"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("outputPdfFilePath");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "outputPdfFilePath"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("waterMarkText");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "waterMarkText"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
