<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.otsnotes.dbstorages.mybatis.mapper.TestPositionMultipleLanguageInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.otsnotes.dbstorages.mybatis.model.TestPositionMultipleLanguageInfoPO" >
    <id column="ID" property="ID" jdbcType="VARCHAR" />
    <result column="TestPositionId" property="testPositionId" jdbcType="VARCHAR" />
    <result column="TestPositionName" property="testPositionName" jdbcType="VARCHAR" />
    <result column="LanguageId" property="languageId" jdbcType="INTEGER" />
    <result column="ActiveIndicator" property="activeIndicator" jdbcType="BIT" />
    <result column="CreatedDate" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="CreatedBy" property="createdBy" jdbcType="VARCHAR" />
    <result column="ModifiedDate" property="modifiedDate" jdbcType="TIMESTAMP" />
    <result column="ModifiedBy" property="modifiedBy" jdbcType="VARCHAR" />
    <result column="LastModifiedTimestamp" property="lastModifiedTimestamp" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    ID, TestPositionId, TestPositionName, LanguageId, ActiveIndicator, CreatedDate, CreatedBy, 
    ModifiedDate, ModifiedBy, LastModifiedTimestamp
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestPositionMultipleLanguageInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_test_position_multiplelanguage
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from tb_test_position_multiplelanguage
    where ID = #{ID,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from tb_test_position_multiplelanguage
    where ID = #{ID,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestPositionMultipleLanguageInfoExample" >
    delete from tb_test_position_multiplelanguage
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestPositionMultipleLanguageInfoPO" >
    insert into tb_test_position_multiplelanguage (ID, TestPositionId, TestPositionName, 
      LanguageId, ActiveIndicator, CreatedDate, 
      CreatedBy, ModifiedDate, ModifiedBy, 
      LastModifiedTimestamp)
    values (#{ID,jdbcType=VARCHAR}, #{testPositionId,jdbcType=VARCHAR}, #{testPositionName,jdbcType=VARCHAR}, 
      #{languageId,jdbcType=INTEGER}, #{activeIndicator,jdbcType=BIT}, #{createdDate,jdbcType=TIMESTAMP}, 
      #{createdBy,jdbcType=VARCHAR}, #{modifiedDate,jdbcType=TIMESTAMP}, #{modifiedBy,jdbcType=VARCHAR}, 
      #{lastModifiedTimestamp,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestPositionMultipleLanguageInfoPO" >
    insert into tb_test_position_multiplelanguage
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="ID != null" >
        ID,
      </if>
      <if test="testPositionId != null" >
        TestPositionId,
      </if>
      <if test="testPositionName != null" >
        TestPositionName,
      </if>
      <if test="languageId != null" >
        LanguageId,
      </if>
      <if test="activeIndicator != null" >
        ActiveIndicator,
      </if>
      <if test="createdDate != null" >
        CreatedDate,
      </if>
      <if test="createdBy != null" >
        CreatedBy,
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate,
      </if>
      <if test="modifiedBy != null" >
        ModifiedBy,
      </if>
      <if test="lastModifiedTimestamp != null" >
        LastModifiedTimestamp,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="ID != null" >
        #{ID,jdbcType=VARCHAR},
      </if>
      <if test="testPositionId != null" >
        #{testPositionId,jdbcType=VARCHAR},
      </if>
      <if test="testPositionName != null" >
        #{testPositionName,jdbcType=VARCHAR},
      </if>
      <if test="languageId != null" >
        #{languageId,jdbcType=INTEGER},
      </if>
      <if test="activeIndicator != null" >
        #{activeIndicator,jdbcType=BIT},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTimestamp != null" >
        #{lastModifiedTimestamp,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestPositionMultipleLanguageInfoExample" resultType="java.lang.Integer" >
    select count(*) from tb_test_position_multiplelanguage
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_test_position_multiplelanguage
    <set >
      <if test="record.ID != null" >
        ID = #{record.ID,jdbcType=VARCHAR},
      </if>
      <if test="record.testPositionId != null" >
        TestPositionId = #{record.testPositionId,jdbcType=VARCHAR},
      </if>
      <if test="record.testPositionName != null" >
        TestPositionName = #{record.testPositionName,jdbcType=VARCHAR},
      </if>
      <if test="record.languageId != null" >
        LanguageId = #{record.languageId,jdbcType=INTEGER},
      </if>
      <if test="record.activeIndicator != null" >
        ActiveIndicator = #{record.activeIndicator,jdbcType=BIT},
      </if>
      <if test="record.createdDate != null" >
        CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdBy != null" >
        CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.modifiedDate != null" >
        ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedBy != null" >
        ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.lastModifiedTimestamp != null" >
        LastModifiedTimestamp = #{record.lastModifiedTimestamp,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_test_position_multiplelanguage
    set ID = #{record.ID,jdbcType=VARCHAR},
      TestPositionId = #{record.testPositionId,jdbcType=VARCHAR},
      TestPositionName = #{record.testPositionName,jdbcType=VARCHAR},
      LanguageId = #{record.languageId,jdbcType=INTEGER},
      ActiveIndicator = #{record.activeIndicator,jdbcType=BIT},
      CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      LastModifiedTimestamp = #{record.lastModifiedTimestamp,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestPositionMultipleLanguageInfoPO" >
    update tb_test_position_multiplelanguage
    <set >
      <if test="testPositionId != null" >
        TestPositionId = #{testPositionId,jdbcType=VARCHAR},
      </if>
      <if test="testPositionName != null" >
        TestPositionName = #{testPositionName,jdbcType=VARCHAR},
      </if>
      <if test="languageId != null" >
        LanguageId = #{languageId,jdbcType=INTEGER},
      </if>
      <if test="activeIndicator != null" >
        ActiveIndicator = #{activeIndicator,jdbcType=BIT},
      </if>
      <if test="createdDate != null" >
        CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        CreatedBy = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        ModifiedBy = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTimestamp != null" >
        LastModifiedTimestamp = #{lastModifiedTimestamp,jdbcType=TIMESTAMP},
      </if>
    </set>
    where ID = #{ID,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestPositionMultipleLanguageInfoPO" >
    update tb_test_position_multiplelanguage
    set TestPositionId = #{testPositionId,jdbcType=VARCHAR},
      TestPositionName = #{testPositionName,jdbcType=VARCHAR},
      LanguageId = #{languageId,jdbcType=INTEGER},
      ActiveIndicator = #{activeIndicator,jdbcType=BIT},
      CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      CreatedBy = #{createdBy,jdbcType=VARCHAR},
      ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{modifiedBy,jdbcType=VARCHAR},
      LastModifiedTimestamp = #{lastModifiedTimestamp,jdbcType=TIMESTAMP}
    where ID = #{ID,jdbcType=VARCHAR}
  </update>
</mapper>