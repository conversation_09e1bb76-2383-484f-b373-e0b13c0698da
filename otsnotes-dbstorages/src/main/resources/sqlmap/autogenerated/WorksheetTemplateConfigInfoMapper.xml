<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.otsnotes.dbstorages.mybatis.mapper.WorksheetTemplateConfigInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.otsnotes.dbstorages.mybatis.model.WorksheetTemplateConfigInfoPO" >
    <id column="ID" property="ID" jdbcType="VARCHAR" />
    <result column="TestLineId" property="testLineId" jdbcType="INTEGER" />
    <result column="TestLineName" property="testLineName" jdbcType="VARCHAR" />
    <result column="CustomerGroupCode" property="customerGroupCode" jdbcType="VARCHAR" />
    <result column="TemplateId" property="templateId" jdbcType="INTEGER" />
    <result column="TemplateName" property="templateName" jdbcType="VARCHAR" />
    <result column="TemplatePath" property="templatePath" jdbcType="VARCHAR" />
    <result column="QrCode" property="qrCode" jdbcType="BIT" />
    <result column="ActiveIndicator" property="activeIndicator" jdbcType="BIT" />
    <result column="CreatedBy" property="createdBy" jdbcType="VARCHAR" />
    <result column="CreatedDate" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="ModifiedBy" property="modifiedBy" jdbcType="VARCHAR" />
    <result column="ModifiedDate" property="modifiedDate" jdbcType="TIMESTAMP" />
    <result column="TemplateType" property="templateType" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    ID, TestLineId, TestLineName, CustomerGroupCode, TemplateId, TemplateName, TemplatePath, 
    QrCode, ActiveIndicator, CreatedBy, CreatedDate, ModifiedBy, ModifiedDate, TemplateType
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.WorksheetTemplateConfigInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_worksheet_template_config
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from tb_worksheet_template_config
    where ID = #{ID,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from tb_worksheet_template_config
    where ID = #{ID,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.WorksheetTemplateConfigInfoExample" >
    delete from tb_worksheet_template_config
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.WorksheetTemplateConfigInfoPO" >
    insert into tb_worksheet_template_config (ID, TestLineId, TestLineName, 
      CustomerGroupCode, TemplateId, TemplateName, 
      TemplatePath, QrCode, ActiveIndicator, 
      CreatedBy, CreatedDate, ModifiedBy, 
      ModifiedDate, TemplateType)
    values (#{ID,jdbcType=VARCHAR}, #{testLineId,jdbcType=INTEGER}, #{testLineName,jdbcType=VARCHAR}, 
      #{customerGroupCode,jdbcType=VARCHAR}, #{templateId,jdbcType=INTEGER}, #{templateName,jdbcType=VARCHAR}, 
      #{templatePath,jdbcType=VARCHAR}, #{qrCode,jdbcType=BIT}, #{activeIndicator,jdbcType=BIT}, 
      #{createdBy,jdbcType=VARCHAR}, #{createdDate,jdbcType=TIMESTAMP}, #{modifiedBy,jdbcType=VARCHAR}, 
      #{modifiedDate,jdbcType=TIMESTAMP}, #{templateType,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.WorksheetTemplateConfigInfoPO" >
    insert into tb_worksheet_template_config
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="ID != null" >
        ID,
      </if>
      <if test="testLineId != null" >
        TestLineId,
      </if>
      <if test="testLineName != null" >
        TestLineName,
      </if>
      <if test="customerGroupCode != null" >
        CustomerGroupCode,
      </if>
      <if test="templateId != null" >
        TemplateId,
      </if>
      <if test="templateName != null" >
        TemplateName,
      </if>
      <if test="templatePath != null" >
        TemplatePath,
      </if>
      <if test="qrCode != null" >
        QrCode,
      </if>
      <if test="activeIndicator != null" >
        ActiveIndicator,
      </if>
      <if test="createdBy != null" >
        CreatedBy,
      </if>
      <if test="createdDate != null" >
        CreatedDate,
      </if>
      <if test="modifiedBy != null" >
        ModifiedBy,
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate,
      </if>
      <if test="templateType != null" >
        TemplateType,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="ID != null" >
        #{ID,jdbcType=VARCHAR},
      </if>
      <if test="testLineId != null" >
        #{testLineId,jdbcType=INTEGER},
      </if>
      <if test="testLineName != null" >
        #{testLineName,jdbcType=VARCHAR},
      </if>
      <if test="customerGroupCode != null" >
        #{customerGroupCode,jdbcType=VARCHAR},
      </if>
      <if test="templateId != null" >
        #{templateId,jdbcType=INTEGER},
      </if>
      <if test="templateName != null" >
        #{templateName,jdbcType=VARCHAR},
      </if>
      <if test="templatePath != null" >
        #{templatePath,jdbcType=VARCHAR},
      </if>
      <if test="qrCode != null" >
        #{qrCode,jdbcType=BIT},
      </if>
      <if test="activeIndicator != null" >
        #{activeIndicator,jdbcType=BIT},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="templateType != null" >
        #{templateType,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.WorksheetTemplateConfigInfoExample" resultType="java.lang.Integer" >
    select count(*) from tb_worksheet_template_config
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_worksheet_template_config
    <set >
      <if test="record.ID != null" >
        ID = #{record.ID,jdbcType=VARCHAR},
      </if>
      <if test="record.testLineId != null" >
        TestLineId = #{record.testLineId,jdbcType=INTEGER},
      </if>
      <if test="record.testLineName != null" >
        TestLineName = #{record.testLineName,jdbcType=VARCHAR},
      </if>
      <if test="record.customerGroupCode != null" >
        CustomerGroupCode = #{record.customerGroupCode,jdbcType=VARCHAR},
      </if>
      <if test="record.templateId != null" >
        TemplateId = #{record.templateId,jdbcType=INTEGER},
      </if>
      <if test="record.templateName != null" >
        TemplateName = #{record.templateName,jdbcType=VARCHAR},
      </if>
      <if test="record.templatePath != null" >
        TemplatePath = #{record.templatePath,jdbcType=VARCHAR},
      </if>
      <if test="record.qrCode != null" >
        QrCode = #{record.qrCode,jdbcType=BIT},
      </if>
      <if test="record.activeIndicator != null" >
        ActiveIndicator = #{record.activeIndicator,jdbcType=BIT},
      </if>
      <if test="record.createdBy != null" >
        CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdDate != null" >
        CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedBy != null" >
        ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.modifiedDate != null" >
        ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.templateType != null" >
        TemplateType = #{record.templateType,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_worksheet_template_config
    set ID = #{record.ID,jdbcType=VARCHAR},
      TestLineId = #{record.testLineId,jdbcType=INTEGER},
      TestLineName = #{record.testLineName,jdbcType=VARCHAR},
      CustomerGroupCode = #{record.customerGroupCode,jdbcType=VARCHAR},
      TemplateId = #{record.templateId,jdbcType=INTEGER},
      TemplateName = #{record.templateName,jdbcType=VARCHAR},
      TemplatePath = #{record.templatePath,jdbcType=VARCHAR},
      QrCode = #{record.qrCode,jdbcType=BIT},
      ActiveIndicator = #{record.activeIndicator,jdbcType=BIT},
      CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      TemplateType = #{record.templateType,jdbcType=INTEGER}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.WorksheetTemplateConfigInfoPO" >
    update tb_worksheet_template_config
    <set >
      <if test="testLineId != null" >
        TestLineId = #{testLineId,jdbcType=INTEGER},
      </if>
      <if test="testLineName != null" >
        TestLineName = #{testLineName,jdbcType=VARCHAR},
      </if>
      <if test="customerGroupCode != null" >
        CustomerGroupCode = #{customerGroupCode,jdbcType=VARCHAR},
      </if>
      <if test="templateId != null" >
        TemplateId = #{templateId,jdbcType=INTEGER},
      </if>
      <if test="templateName != null" >
        TemplateName = #{templateName,jdbcType=VARCHAR},
      </if>
      <if test="templatePath != null" >
        TemplatePath = #{templatePath,jdbcType=VARCHAR},
      </if>
      <if test="qrCode != null" >
        QrCode = #{qrCode,jdbcType=BIT},
      </if>
      <if test="activeIndicator != null" >
        ActiveIndicator = #{activeIndicator,jdbcType=BIT},
      </if>
      <if test="createdBy != null" >
        CreatedBy = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        ModifiedBy = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="templateType != null" >
        TemplateType = #{templateType,jdbcType=INTEGER},
      </if>
    </set>
    where ID = #{ID,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.WorksheetTemplateConfigInfoPO" >
    update tb_worksheet_template_config
    set TestLineId = #{testLineId,jdbcType=INTEGER},
      TestLineName = #{testLineName,jdbcType=VARCHAR},
      CustomerGroupCode = #{customerGroupCode,jdbcType=VARCHAR},
      TemplateId = #{templateId,jdbcType=INTEGER},
      TemplateName = #{templateName,jdbcType=VARCHAR},
      TemplatePath = #{templatePath,jdbcType=VARCHAR},
      QrCode = #{qrCode,jdbcType=BIT},
      ActiveIndicator = #{activeIndicator,jdbcType=BIT},
      CreatedBy = #{createdBy,jdbcType=VARCHAR},
      CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{modifiedBy,jdbcType=VARCHAR},
      ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      TemplateType = #{templateType,jdbcType=INTEGER}
    where ID = #{ID,jdbcType=VARCHAR}
  </update>
</mapper>