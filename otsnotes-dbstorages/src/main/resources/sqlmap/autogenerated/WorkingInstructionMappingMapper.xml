<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.otsnotes.dbstorages.mybatis.mapper.WorkingInstructionMappingMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.otsnotes.dbstorages.mybatis.model.WorkingInstructionMappingPO" >
    <id column="id" property="id" jdbcType="VARCHAR" />
    <result column="testline_id" property="testlineId" jdbcType="VARCHAR" />
    <result column="testline_name" property="testlineName" jdbcType="VARCHAR" />
    <result column="customer_group_name" property="customerGroupName" jdbcType="VARCHAR" />
    <result column="template_id" property="templateId" jdbcType="VARCHAR" />
    <result column="WIType" property="witype" jdbcType="INTEGER" />
    <result column="CitationId" property="citationid" jdbcType="INTEGER" />
    <result column="createdDate" property="createddate" jdbcType="TIMESTAMP" />
    <result column="createdBy" property="createdby" jdbcType="VARCHAR" />
    <result column="activeIndicator" property="activeindicator" jdbcType="BIT" />
    <result column="modifiedDate" property="modifieddate" jdbcType="TIMESTAMP" />
    <result column="modifiedBy" property="modifiedby" jdbcType="VARCHAR" />
    <result column="groupCode" property="groupcode" jdbcType="VARCHAR" />
    <result column="labCode" property="labcode" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, testline_id, testline_name, customer_group_name, template_id, WIType, CitationId, 
    createdDate, createdBy, activeIndicator, modifiedDate, modifiedBy, groupCode, labCode
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.WorkingInstructionMappingExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_working_instruction_mapping
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from tb_working_instruction_mapping
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from tb_working_instruction_mapping
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.WorkingInstructionMappingExample" >
    delete from tb_working_instruction_mapping
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.WorkingInstructionMappingPO" >
    insert into tb_working_instruction_mapping (id, testline_id, testline_name, 
      customer_group_name, template_id, WIType, 
      CitationId, createdDate, createdBy, 
      activeIndicator, modifiedDate, modifiedBy, 
      groupCode, labCode)
    values (#{id,jdbcType=VARCHAR}, #{testlineId,jdbcType=VARCHAR}, #{testlineName,jdbcType=VARCHAR}, 
      #{customerGroupName,jdbcType=VARCHAR}, #{templateId,jdbcType=VARCHAR}, #{witype,jdbcType=INTEGER}, 
      #{citationid,jdbcType=INTEGER}, #{createddate,jdbcType=TIMESTAMP}, #{createdby,jdbcType=VARCHAR}, 
      #{activeindicator,jdbcType=BIT}, #{modifieddate,jdbcType=TIMESTAMP}, #{modifiedby,jdbcType=VARCHAR}, 
      #{groupcode,jdbcType=VARCHAR}, #{labcode,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.WorkingInstructionMappingPO" >
    insert into tb_working_instruction_mapping
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="testlineId != null" >
        testline_id,
      </if>
      <if test="testlineName != null" >
        testline_name,
      </if>
      <if test="customerGroupName != null" >
        customer_group_name,
      </if>
      <if test="templateId != null" >
        template_id,
      </if>
      <if test="witype != null" >
        WIType,
      </if>
      <if test="citationid != null" >
        CitationId,
      </if>
      <if test="createddate != null" >
        createdDate,
      </if>
      <if test="createdby != null" >
        createdBy,
      </if>
      <if test="activeindicator != null" >
        activeIndicator,
      </if>
      <if test="modifieddate != null" >
        modifiedDate,
      </if>
      <if test="modifiedby != null" >
        modifiedBy,
      </if>
      <if test="groupcode != null" >
        groupCode,
      </if>
      <if test="labcode != null" >
        labCode,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="testlineId != null" >
        #{testlineId,jdbcType=VARCHAR},
      </if>
      <if test="testlineName != null" >
        #{testlineName,jdbcType=VARCHAR},
      </if>
      <if test="customerGroupName != null" >
        #{customerGroupName,jdbcType=VARCHAR},
      </if>
      <if test="templateId != null" >
        #{templateId,jdbcType=VARCHAR},
      </if>
      <if test="witype != null" >
        #{witype,jdbcType=INTEGER},
      </if>
      <if test="citationid != null" >
        #{citationid,jdbcType=INTEGER},
      </if>
      <if test="createddate != null" >
        #{createddate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdby != null" >
        #{createdby,jdbcType=VARCHAR},
      </if>
      <if test="activeindicator != null" >
        #{activeindicator,jdbcType=BIT},
      </if>
      <if test="modifieddate != null" >
        #{modifieddate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedby != null" >
        #{modifiedby,jdbcType=VARCHAR},
      </if>
      <if test="groupcode != null" >
        #{groupcode,jdbcType=VARCHAR},
      </if>
      <if test="labcode != null" >
        #{labcode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.WorkingInstructionMappingExample" resultType="java.lang.Integer" >
    select count(*) from tb_working_instruction_mapping
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_working_instruction_mapping
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.testlineId != null" >
        testline_id = #{record.testlineId,jdbcType=VARCHAR},
      </if>
      <if test="record.testlineName != null" >
        testline_name = #{record.testlineName,jdbcType=VARCHAR},
      </if>
      <if test="record.customerGroupName != null" >
        customer_group_name = #{record.customerGroupName,jdbcType=VARCHAR},
      </if>
      <if test="record.templateId != null" >
        template_id = #{record.templateId,jdbcType=VARCHAR},
      </if>
      <if test="record.witype != null" >
        WIType = #{record.witype,jdbcType=INTEGER},
      </if>
      <if test="record.citationid != null" >
        CitationId = #{record.citationid,jdbcType=INTEGER},
      </if>
      <if test="record.createddate != null" >
        createdDate = #{record.createddate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdby != null" >
        createdBy = #{record.createdby,jdbcType=VARCHAR},
      </if>
      <if test="record.activeindicator != null" >
        activeIndicator = #{record.activeindicator,jdbcType=BIT},
      </if>
      <if test="record.modifieddate != null" >
        modifiedDate = #{record.modifieddate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedby != null" >
        modifiedBy = #{record.modifiedby,jdbcType=VARCHAR},
      </if>
      <if test="record.groupcode != null" >
        groupCode = #{record.groupcode,jdbcType=VARCHAR},
      </if>
      <if test="record.labcode != null" >
        labCode = #{record.labcode,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_working_instruction_mapping
    set id = #{record.id,jdbcType=VARCHAR},
      testline_id = #{record.testlineId,jdbcType=VARCHAR},
      testline_name = #{record.testlineName,jdbcType=VARCHAR},
      customer_group_name = #{record.customerGroupName,jdbcType=VARCHAR},
      template_id = #{record.templateId,jdbcType=VARCHAR},
      WIType = #{record.witype,jdbcType=INTEGER},
      CitationId = #{record.citationid,jdbcType=INTEGER},
      createdDate = #{record.createddate,jdbcType=TIMESTAMP},
      createdBy = #{record.createdby,jdbcType=VARCHAR},
      activeIndicator = #{record.activeindicator,jdbcType=BIT},
      modifiedDate = #{record.modifieddate,jdbcType=TIMESTAMP},
      modifiedBy = #{record.modifiedby,jdbcType=VARCHAR},
      groupCode = #{record.groupcode,jdbcType=VARCHAR},
      labCode = #{record.labcode,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.WorkingInstructionMappingPO" >
    update tb_working_instruction_mapping
    <set >
      <if test="testlineId != null" >
        testline_id = #{testlineId,jdbcType=VARCHAR},
      </if>
      <if test="testlineName != null" >
        testline_name = #{testlineName,jdbcType=VARCHAR},
      </if>
      <if test="customerGroupName != null" >
        customer_group_name = #{customerGroupName,jdbcType=VARCHAR},
      </if>
      <if test="templateId != null" >
        template_id = #{templateId,jdbcType=VARCHAR},
      </if>
      <if test="witype != null" >
        WIType = #{witype,jdbcType=INTEGER},
      </if>
      <if test="citationid != null" >
        CitationId = #{citationid,jdbcType=INTEGER},
      </if>
      <if test="createddate != null" >
        createdDate = #{createddate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdby != null" >
        createdBy = #{createdby,jdbcType=VARCHAR},
      </if>
      <if test="activeindicator != null" >
        activeIndicator = #{activeindicator,jdbcType=BIT},
      </if>
      <if test="modifieddate != null" >
        modifiedDate = #{modifieddate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedby != null" >
        modifiedBy = #{modifiedby,jdbcType=VARCHAR},
      </if>
      <if test="groupcode != null" >
        groupCode = #{groupcode,jdbcType=VARCHAR},
      </if>
      <if test="labcode != null" >
        labCode = #{labcode,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.WorkingInstructionMappingPO" >
    update tb_working_instruction_mapping
    set testline_id = #{testlineId,jdbcType=VARCHAR},
      testline_name = #{testlineName,jdbcType=VARCHAR},
      customer_group_name = #{customerGroupName,jdbcType=VARCHAR},
      template_id = #{templateId,jdbcType=VARCHAR},
      WIType = #{witype,jdbcType=INTEGER},
      CitationId = #{citationid,jdbcType=INTEGER},
      createdDate = #{createddate,jdbcType=TIMESTAMP},
      createdBy = #{createdby,jdbcType=VARCHAR},
      activeIndicator = #{activeindicator,jdbcType=BIT},
      modifiedDate = #{modifieddate,jdbcType=TIMESTAMP},
      modifiedBy = #{modifiedby,jdbcType=VARCHAR},
      groupCode = #{groupcode,jdbcType=VARCHAR},
      labCode = #{labcode,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>