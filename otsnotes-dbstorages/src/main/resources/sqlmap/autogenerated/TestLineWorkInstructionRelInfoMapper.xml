<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.otsnotes.dbstorages.mybatis.mapper.TestLineWorkInstructionRelInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineWorkInstructionRelInfoPO" >
    <id column="Id" property="id" jdbcType="BIGINT" />
    <result column="TestLineVersionId" property="testLineVersionId" jdbcType="INTEGER" />
    <result column="ProductLineId" property="productLineId" jdbcType="INTEGER" />
    <result column="CategoryId" property="categoryId" jdbcType="INTEGER" />
    <result column="CategoryName" property="categoryName" jdbcType="VARCHAR" />
    <result column="CustomerAccountId" property="customerAccountId" jdbcType="INTEGER" />
    <result column="WorkInstructionId" property="workInstructionId" jdbcType="INTEGER" />
    <result column="WorkInstructionName" property="workInstructionName" jdbcType="VARCHAR" />
    <result column="WorkInstructionText" property="workInstructionText" jdbcType="VARCHAR" />
    <result column="BizVersionId" property="bizVersionId" jdbcType="CHAR" />
    <result column="RelStatus" property="relStatus" jdbcType="INTEGER" />
    <result column="CreatedDate" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="ModifiedDate" property="modifiedDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    Id, TestLineVersionId, ProductLineId, CategoryId, CategoryName, CustomerAccountId, 
    WorkInstructionId, WorkInstructionName, WorkInstructionText, BizVersionId, RelStatus, 
    CreatedDate, ModifiedDate
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineWorkInstructionRelInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tre_trims_testline_work_instruction_relationship
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from tre_trims_testline_work_instruction_relationship
    where Id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from tre_trims_testline_work_instruction_relationship
    where Id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineWorkInstructionRelInfoExample" >
    delete from tre_trims_testline_work_instruction_relationship
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineWorkInstructionRelInfoPO" >
    insert into tre_trims_testline_work_instruction_relationship (Id, TestLineVersionId, ProductLineId,
      CategoryId, CategoryName, CustomerAccountId, 
      WorkInstructionId, WorkInstructionName, 
      WorkInstructionText, BizVersionId, RelStatus, 
      CreatedDate, ModifiedDate)
    values (#{id,jdbcType=BIGINT}, #{testLineVersionId,jdbcType=INTEGER}, #{productLineId,jdbcType=INTEGER}, 
      #{categoryId,jdbcType=INTEGER}, #{categoryName,jdbcType=VARCHAR}, #{customerAccountId,jdbcType=INTEGER}, 
      #{workInstructionId,jdbcType=INTEGER}, #{workInstructionName,jdbcType=VARCHAR}, 
      #{workInstructionText,jdbcType=VARCHAR}, #{bizVersionId,jdbcType=CHAR}, #{relStatus,jdbcType=INTEGER}, 
      #{createdDate,jdbcType=TIMESTAMP}, #{modifiedDate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineWorkInstructionRelInfoPO" >
    insert into tre_trims_testline_work_instruction_relationship
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        Id,
      </if>
      <if test="testLineVersionId != null" >
        TestLineVersionId,
      </if>
      <if test="productLineId != null" >
        ProductLineId,
      </if>
      <if test="categoryId != null" >
        CategoryId,
      </if>
      <if test="categoryName != null" >
        CategoryName,
      </if>
      <if test="customerAccountId != null" >
        CustomerAccountId,
      </if>
      <if test="workInstructionId != null" >
        WorkInstructionId,
      </if>
      <if test="workInstructionName != null" >
        WorkInstructionName,
      </if>
      <if test="workInstructionText != null" >
        WorkInstructionText,
      </if>
      <if test="bizVersionId != null" >
        BizVersionId,
      </if>
      <if test="relStatus != null" >
        RelStatus,
      </if>
      <if test="createdDate != null" >
        CreatedDate,
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="testLineVersionId != null" >
        #{testLineVersionId,jdbcType=INTEGER},
      </if>
      <if test="productLineId != null" >
        #{productLineId,jdbcType=INTEGER},
      </if>
      <if test="categoryId != null" >
        #{categoryId,jdbcType=INTEGER},
      </if>
      <if test="categoryName != null" >
        #{categoryName,jdbcType=VARCHAR},
      </if>
      <if test="customerAccountId != null" >
        #{customerAccountId,jdbcType=INTEGER},
      </if>
      <if test="workInstructionId != null" >
        #{workInstructionId,jdbcType=INTEGER},
      </if>
      <if test="workInstructionName != null" >
        #{workInstructionName,jdbcType=VARCHAR},
      </if>
      <if test="workInstructionText != null" >
        #{workInstructionText,jdbcType=VARCHAR},
      </if>
      <if test="bizVersionId != null" >
        #{bizVersionId,jdbcType=CHAR},
      </if>
      <if test="relStatus != null" >
        #{relStatus,jdbcType=INTEGER},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineWorkInstructionRelInfoExample" resultType="java.lang.Integer" >
    select count(*) from tre_trims_testline_work_instruction_relationship
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tre_trims_testline_work_instruction_relationship
    <set >
      <if test="record.id != null" >
        Id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.testLineVersionId != null" >
        TestLineVersionId = #{record.testLineVersionId,jdbcType=INTEGER},
      </if>
      <if test="record.productLineId != null" >
        ProductLineId = #{record.productLineId,jdbcType=INTEGER},
      </if>
      <if test="record.categoryId != null" >
        CategoryId = #{record.categoryId,jdbcType=INTEGER},
      </if>
      <if test="record.categoryName != null" >
        CategoryName = #{record.categoryName,jdbcType=VARCHAR},
      </if>
      <if test="record.customerAccountId != null" >
        CustomerAccountId = #{record.customerAccountId,jdbcType=INTEGER},
      </if>
      <if test="record.workInstructionId != null" >
        WorkInstructionId = #{record.workInstructionId,jdbcType=INTEGER},
      </if>
      <if test="record.workInstructionName != null" >
        WorkInstructionName = #{record.workInstructionName,jdbcType=VARCHAR},
      </if>
      <if test="record.workInstructionText != null" >
        WorkInstructionText = #{record.workInstructionText,jdbcType=VARCHAR},
      </if>
      <if test="record.bizVersionId != null" >
        BizVersionId = #{record.bizVersionId,jdbcType=CHAR},
      </if>
      <if test="record.relStatus != null" >
        RelStatus = #{record.relStatus,jdbcType=INTEGER},
      </if>
      <if test="record.createdDate != null" >
        CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedDate != null" >
        ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tre_trims_testline_work_instruction_relationship
    set Id = #{record.id,jdbcType=BIGINT},
      TestLineVersionId = #{record.testLineVersionId,jdbcType=INTEGER},
      ProductLineId = #{record.productLineId,jdbcType=INTEGER},
      CategoryId = #{record.categoryId,jdbcType=INTEGER},
      CategoryName = #{record.categoryName,jdbcType=VARCHAR},
      CustomerAccountId = #{record.customerAccountId,jdbcType=INTEGER},
      WorkInstructionId = #{record.workInstructionId,jdbcType=INTEGER},
      WorkInstructionName = #{record.workInstructionName,jdbcType=VARCHAR},
      WorkInstructionText = #{record.workInstructionText,jdbcType=VARCHAR},
      BizVersionId = #{record.bizVersionId,jdbcType=CHAR},
      RelStatus = #{record.relStatus,jdbcType=INTEGER},
      CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineWorkInstructionRelInfoPO" >
    update tre_trims_testline_work_instruction_relationship
    <set >
      <if test="testLineVersionId != null" >
        TestLineVersionId = #{testLineVersionId,jdbcType=INTEGER},
      </if>
      <if test="productLineId != null" >
        ProductLineId = #{productLineId,jdbcType=INTEGER},
      </if>
      <if test="categoryId != null" >
        CategoryId = #{categoryId,jdbcType=INTEGER},
      </if>
      <if test="categoryName != null" >
        CategoryName = #{categoryName,jdbcType=VARCHAR},
      </if>
      <if test="customerAccountId != null" >
        CustomerAccountId = #{customerAccountId,jdbcType=INTEGER},
      </if>
      <if test="workInstructionId != null" >
        WorkInstructionId = #{workInstructionId,jdbcType=INTEGER},
      </if>
      <if test="workInstructionName != null" >
        WorkInstructionName = #{workInstructionName,jdbcType=VARCHAR},
      </if>
      <if test="workInstructionText != null" >
        WorkInstructionText = #{workInstructionText,jdbcType=VARCHAR},
      </if>
      <if test="bizVersionId != null" >
        BizVersionId = #{bizVersionId,jdbcType=CHAR},
      </if>
      <if test="relStatus != null" >
        RelStatus = #{relStatus,jdbcType=INTEGER},
      </if>
      <if test="createdDate != null" >
        CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where Id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineWorkInstructionRelInfoPO" >
    update tre_trims_testline_work_instruction_relationship
    set TestLineVersionId = #{testLineVersionId,jdbcType=INTEGER},
      ProductLineId = #{productLineId,jdbcType=INTEGER},
      CategoryId = #{categoryId,jdbcType=INTEGER},
      CategoryName = #{categoryName,jdbcType=VARCHAR},
      CustomerAccountId = #{customerAccountId,jdbcType=INTEGER},
      WorkInstructionId = #{workInstructionId,jdbcType=INTEGER},
      WorkInstructionName = #{workInstructionName,jdbcType=VARCHAR},
      WorkInstructionText = #{workInstructionText,jdbcType=VARCHAR},
      BizVersionId = #{bizVersionId,jdbcType=CHAR},
      RelStatus = #{relStatus,jdbcType=INTEGER},
      CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP}
    where Id = #{id,jdbcType=BIGINT}
  </update>
</mapper>