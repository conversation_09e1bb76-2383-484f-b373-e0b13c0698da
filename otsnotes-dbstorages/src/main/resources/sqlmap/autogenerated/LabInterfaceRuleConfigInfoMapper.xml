<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.otsnotes.dbstorages.mybatis.mapper.LabInterfaceRuleConfigInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.otsnotes.dbstorages.mybatis.model.LabInterfaceRuleConfigInfoPO" >
    <id column="Id" property="id" jdbcType="INTEGER" />
    <result column="LabId" property="labId" jdbcType="INTEGER" />
    <result column="ClassName" property="className" jdbcType="VARCHAR" />
    <result column="MethodName" property="methodName" jdbcType="VARCHAR" />
    <result column="RouteId" property="routeId" jdbcType="INTEGER" />
    <result column="OldRouteId" property="oldRouteId" jdbcType="INTEGER" />
    <result column="MethodRouteId" property="methodRouteId" jdbcType="INTEGER" />
    <result column="OldMethodRouteId" property="oldMethodRouteId" jdbcType="INTEGER" />
    <result column="DualWrite" property="dualWrite" jdbcType="BIT" />
    <result column="Status" property="status" jdbcType="INTEGER" />
    <result column="Remarks" property="remarks" jdbcType="VARCHAR" />
    <result column="DevComplete" property="devComplete" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    Id, LabId, ClassName, MethodName, RouteId, OldRouteId, MethodRouteId, OldMethodRouteId, 
    DualWrite, `Status`, Remarks, DevComplete
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.LabInterfaceRuleConfigInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_trims_lab_interface_rule_config
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from tb_trims_lab_interface_rule_config
    where Id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from tb_trims_lab_interface_rule_config
    where Id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.LabInterfaceRuleConfigInfoExample" >
    delete from tb_trims_lab_interface_rule_config
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.LabInterfaceRuleConfigInfoPO" >
    insert into tb_trims_lab_interface_rule_config (Id, LabId, ClassName,
      MethodName, RouteId, OldRouteId, 
      MethodRouteId, OldMethodRouteId, DualWrite, 
      `Status`, Remarks, DevComplete
      )
    values (#{id,jdbcType=INTEGER}, #{labId,jdbcType=INTEGER}, #{className,jdbcType=VARCHAR}, 
      #{methodName,jdbcType=VARCHAR}, #{routeId,jdbcType=INTEGER}, #{oldRouteId,jdbcType=INTEGER}, 
      #{methodRouteId,jdbcType=INTEGER}, #{oldMethodRouteId,jdbcType=INTEGER}, #{dualWrite,jdbcType=BIT}, 
      #{status,jdbcType=INTEGER}, #{remarks,jdbcType=VARCHAR}, #{devComplete,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.LabInterfaceRuleConfigInfoPO" >
    insert into tb_trims_lab_interface_rule_config
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        Id,
      </if>
      <if test="labId != null" >
        LabId,
      </if>
      <if test="className != null" >
        ClassName,
      </if>
      <if test="methodName != null" >
        MethodName,
      </if>
      <if test="routeId != null" >
        RouteId,
      </if>
      <if test="oldRouteId != null" >
        OldRouteId,
      </if>
      <if test="methodRouteId != null" >
        MethodRouteId,
      </if>
      <if test="oldMethodRouteId != null" >
        OldMethodRouteId,
      </if>
      <if test="dualWrite != null" >
        DualWrite,
      </if>
      <if test="status != null" >
        `Status`,
      </if>
      <if test="remarks != null" >
        Remarks,
      </if>
      <if test="devComplete != null" >
        DevComplete,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="labId != null" >
        #{labId,jdbcType=INTEGER},
      </if>
      <if test="className != null" >
        #{className,jdbcType=VARCHAR},
      </if>
      <if test="methodName != null" >
        #{methodName,jdbcType=VARCHAR},
      </if>
      <if test="routeId != null" >
        #{routeId,jdbcType=INTEGER},
      </if>
      <if test="oldRouteId != null" >
        #{oldRouteId,jdbcType=INTEGER},
      </if>
      <if test="methodRouteId != null" >
        #{methodRouteId,jdbcType=INTEGER},
      </if>
      <if test="oldMethodRouteId != null" >
        #{oldMethodRouteId,jdbcType=INTEGER},
      </if>
      <if test="dualWrite != null" >
        #{dualWrite,jdbcType=BIT},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="remarks != null" >
        #{remarks,jdbcType=VARCHAR},
      </if>
      <if test="devComplete != null" >
        #{devComplete,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.LabInterfaceRuleConfigInfoExample" resultType="java.lang.Integer" >
    select count(*) from tb_trims_lab_interface_rule_config
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_trims_lab_interface_rule_config
    <set >
      <if test="record.id != null" >
        Id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.labId != null" >
        LabId = #{record.labId,jdbcType=INTEGER},
      </if>
      <if test="record.className != null" >
        ClassName = #{record.className,jdbcType=VARCHAR},
      </if>
      <if test="record.methodName != null" >
        MethodName = #{record.methodName,jdbcType=VARCHAR},
      </if>
      <if test="record.routeId != null" >
        RouteId = #{record.routeId,jdbcType=INTEGER},
      </if>
      <if test="record.oldRouteId != null" >
        OldRouteId = #{record.oldRouteId,jdbcType=INTEGER},
      </if>
      <if test="record.methodRouteId != null" >
        MethodRouteId = #{record.methodRouteId,jdbcType=INTEGER},
      </if>
      <if test="record.oldMethodRouteId != null" >
        OldMethodRouteId = #{record.oldMethodRouteId,jdbcType=INTEGER},
      </if>
      <if test="record.dualWrite != null" >
        DualWrite = #{record.dualWrite,jdbcType=BIT},
      </if>
      <if test="record.status != null" >
        `Status` = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.remarks != null" >
        Remarks = #{record.remarks,jdbcType=VARCHAR},
      </if>
      <if test="record.devComplete != null" >
        DevComplete = #{record.devComplete,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_trims_lab_interface_rule_config
    set Id = #{record.id,jdbcType=INTEGER},
      LabId = #{record.labId,jdbcType=INTEGER},
      ClassName = #{record.className,jdbcType=VARCHAR},
      MethodName = #{record.methodName,jdbcType=VARCHAR},
      RouteId = #{record.routeId,jdbcType=INTEGER},
      OldRouteId = #{record.oldRouteId,jdbcType=INTEGER},
      MethodRouteId = #{record.methodRouteId,jdbcType=INTEGER},
      OldMethodRouteId = #{record.oldMethodRouteId,jdbcType=INTEGER},
      DualWrite = #{record.dualWrite,jdbcType=BIT},
      `Status` = #{record.status,jdbcType=INTEGER},
      Remarks = #{record.remarks,jdbcType=VARCHAR},
      DevComplete = #{record.devComplete,jdbcType=INTEGER}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.LabInterfaceRuleConfigInfoPO" >
    update tb_trims_lab_interface_rule_config
    <set >
      <if test="labId != null" >
        LabId = #{labId,jdbcType=INTEGER},
      </if>
      <if test="className != null" >
        ClassName = #{className,jdbcType=VARCHAR},
      </if>
      <if test="methodName != null" >
        MethodName = #{methodName,jdbcType=VARCHAR},
      </if>
      <if test="routeId != null" >
        RouteId = #{routeId,jdbcType=INTEGER},
      </if>
      <if test="oldRouteId != null" >
        OldRouteId = #{oldRouteId,jdbcType=INTEGER},
      </if>
      <if test="methodRouteId != null" >
        MethodRouteId = #{methodRouteId,jdbcType=INTEGER},
      </if>
      <if test="oldMethodRouteId != null" >
        OldMethodRouteId = #{oldMethodRouteId,jdbcType=INTEGER},
      </if>
      <if test="dualWrite != null" >
        DualWrite = #{dualWrite,jdbcType=BIT},
      </if>
      <if test="status != null" >
        `Status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="remarks != null" >
        Remarks = #{remarks,jdbcType=VARCHAR},
      </if>
      <if test="devComplete != null" >
        DevComplete = #{devComplete,jdbcType=INTEGER},
      </if>
    </set>
    where Id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.LabInterfaceRuleConfigInfoPO" >
    update tb_trims_lab_interface_rule_config
    set LabId = #{labId,jdbcType=INTEGER},
      ClassName = #{className,jdbcType=VARCHAR},
      MethodName = #{methodName,jdbcType=VARCHAR},
      RouteId = #{routeId,jdbcType=INTEGER},
      OldRouteId = #{oldRouteId,jdbcType=INTEGER},
      MethodRouteId = #{methodRouteId,jdbcType=INTEGER},
      OldMethodRouteId = #{oldMethodRouteId,jdbcType=INTEGER},
      DualWrite = #{dualWrite,jdbcType=BIT},
      `Status` = #{status,jdbcType=INTEGER},
      Remarks = #{remarks,jdbcType=VARCHAR},
      DevComplete = #{devComplete,jdbcType=INTEGER}
    where Id = #{id,jdbcType=INTEGER}
  </update>
</mapper>