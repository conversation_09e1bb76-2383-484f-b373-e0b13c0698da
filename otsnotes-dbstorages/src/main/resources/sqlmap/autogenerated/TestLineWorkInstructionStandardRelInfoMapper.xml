<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.otsnotes.dbstorages.mybatis.mapper.TestLineWorkInstructionStandardRelInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineWorkInstructionStandardRelInfoPO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="WorkInstructionId" property="workInstructionId" jdbcType="INTEGER" />
    <result column="TestStandardVersionId" property="testStandardVersionId" jdbcType="INTEGER" />
    <result column="RelStatus" property="relStatus" jdbcType="INTEGER" />
    <result column="BizVersionId" property="bizVersionId" jdbcType="CHAR" />
    <result column="CreatedDate" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="ModifiedDate" property="modifiedDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, WorkInstructionId, TestStandardVersionId, RelStatus, BizVersionId, CreatedDate, 
    ModifiedDate
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineWorkInstructionStandardRelInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_trims_work_instruction_standard_relationship
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from tb_trims_work_instruction_standard_relationship
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from tb_trims_work_instruction_standard_relationship
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineWorkInstructionStandardRelInfoExample" >
    delete from tb_trims_work_instruction_standard_relationship
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineWorkInstructionStandardRelInfoPO" >
    insert into tb_trims_work_instruction_standard_relationship (id, WorkInstructionId, TestStandardVersionId,
      RelStatus, BizVersionId, CreatedDate, 
      ModifiedDate)
    values (#{id,jdbcType=BIGINT}, #{workInstructionId,jdbcType=INTEGER}, #{testStandardVersionId,jdbcType=INTEGER}, 
      #{relStatus,jdbcType=INTEGER}, #{bizVersionId,jdbcType=CHAR}, #{createdDate,jdbcType=TIMESTAMP}, 
      #{modifiedDate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineWorkInstructionStandardRelInfoPO" >
    insert into tb_trims_work_instruction_standard_relationship
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="workInstructionId != null" >
        WorkInstructionId,
      </if>
      <if test="testStandardVersionId != null" >
        TestStandardVersionId,
      </if>
      <if test="relStatus != null" >
        RelStatus,
      </if>
      <if test="bizVersionId != null" >
        BizVersionId,
      </if>
      <if test="createdDate != null" >
        CreatedDate,
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="workInstructionId != null" >
        #{workInstructionId,jdbcType=INTEGER},
      </if>
      <if test="testStandardVersionId != null" >
        #{testStandardVersionId,jdbcType=INTEGER},
      </if>
      <if test="relStatus != null" >
        #{relStatus,jdbcType=INTEGER},
      </if>
      <if test="bizVersionId != null" >
        #{bizVersionId,jdbcType=CHAR},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineWorkInstructionStandardRelInfoExample" resultType="java.lang.Integer" >
    select count(*) from tb_trims_work_instruction_standard_relationship
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_trims_work_instruction_standard_relationship
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.workInstructionId != null" >
        WorkInstructionId = #{record.workInstructionId,jdbcType=INTEGER},
      </if>
      <if test="record.testStandardVersionId != null" >
        TestStandardVersionId = #{record.testStandardVersionId,jdbcType=INTEGER},
      </if>
      <if test="record.relStatus != null" >
        RelStatus = #{record.relStatus,jdbcType=INTEGER},
      </if>
      <if test="record.bizVersionId != null" >
        BizVersionId = #{record.bizVersionId,jdbcType=CHAR},
      </if>
      <if test="record.createdDate != null" >
        CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedDate != null" >
        ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_trims_work_instruction_standard_relationship
    set id = #{record.id,jdbcType=BIGINT},
      WorkInstructionId = #{record.workInstructionId,jdbcType=INTEGER},
      TestStandardVersionId = #{record.testStandardVersionId,jdbcType=INTEGER},
      RelStatus = #{record.relStatus,jdbcType=INTEGER},
      BizVersionId = #{record.bizVersionId,jdbcType=CHAR},
      CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineWorkInstructionStandardRelInfoPO" >
    update tb_trims_work_instruction_standard_relationship
    <set >
      <if test="workInstructionId != null" >
        WorkInstructionId = #{workInstructionId,jdbcType=INTEGER},
      </if>
      <if test="testStandardVersionId != null" >
        TestStandardVersionId = #{testStandardVersionId,jdbcType=INTEGER},
      </if>
      <if test="relStatus != null" >
        RelStatus = #{relStatus,jdbcType=INTEGER},
      </if>
      <if test="bizVersionId != null" >
        BizVersionId = #{bizVersionId,jdbcType=CHAR},
      </if>
      <if test="createdDate != null" >
        CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineWorkInstructionStandardRelInfoPO" >
    update tb_trims_work_instruction_standard_relationship
    set WorkInstructionId = #{workInstructionId,jdbcType=INTEGER},
      TestStandardVersionId = #{testStandardVersionId,jdbcType=INTEGER},
      RelStatus = #{relStatus,jdbcType=INTEGER},
      BizVersionId = #{bizVersionId,jdbcType=CHAR},
      CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>