<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.otsnotes.dbstorages.mybatis.mapper.ReportMatrixRelationShipInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.otsnotes.dbstorages.mybatis.model.ReportMatrixRelationShipInfoPO" >
    <id column="ID" property="ID" jdbcType="VARCHAR" />
    <result column="ReportID" property="reportID" jdbcType="VARCHAR" />
    <result column="TestMatrixID" property="testMatrixID" jdbcType="VARCHAR" />
    <result column="CreatedBy" property="createdBy" jdbcType="VARCHAR" />
    <result column="CreatedDate" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="ModifiedBy" property="modifiedBy" jdbcType="VARCHAR" />
    <result column="ModifiedDate" property="modifiedDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    ID, ReportID, TestMatrixID, CreatedBy, CreatedDate, ModifiedBy, ModifiedDate
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.ReportMatrixRelationShipInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tre_report_matrix_relationship
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from tre_report_matrix_relationship
    where ID = #{ID,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from tre_report_matrix_relationship
    where ID = #{ID,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.ReportMatrixRelationShipInfoExample" >
    delete from tre_report_matrix_relationship
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.ReportMatrixRelationShipInfoPO" >
    insert into tre_report_matrix_relationship (ID, ReportID, TestMatrixID, 
      CreatedBy, CreatedDate, ModifiedBy, 
      ModifiedDate)
    values (#{ID,jdbcType=VARCHAR}, #{reportID,jdbcType=VARCHAR}, #{testMatrixID,jdbcType=VARCHAR}, 
      #{createdBy,jdbcType=VARCHAR}, #{createdDate,jdbcType=TIMESTAMP}, #{modifiedBy,jdbcType=VARCHAR}, 
      #{modifiedDate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.ReportMatrixRelationShipInfoPO" >
    insert into tre_report_matrix_relationship
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="ID != null" >
        ID,
      </if>
      <if test="reportID != null" >
        ReportID,
      </if>
      <if test="testMatrixID != null" >
        TestMatrixID,
      </if>
      <if test="createdBy != null" >
        CreatedBy,
      </if>
      <if test="createdDate != null" >
        CreatedDate,
      </if>
      <if test="modifiedBy != null" >
        ModifiedBy,
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="ID != null" >
        #{ID,jdbcType=VARCHAR},
      </if>
      <if test="reportID != null" >
        #{reportID,jdbcType=VARCHAR},
      </if>
      <if test="testMatrixID != null" >
        #{testMatrixID,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.ReportMatrixRelationShipInfoExample" resultType="java.lang.Integer" >
    select count(*) from tre_report_matrix_relationship
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tre_report_matrix_relationship
    <set >
      <if test="record.ID != null" >
        ID = #{record.ID,jdbcType=VARCHAR},
      </if>
      <if test="record.reportID != null" >
        ReportID = #{record.reportID,jdbcType=VARCHAR},
      </if>
      <if test="record.testMatrixID != null" >
        TestMatrixID = #{record.testMatrixID,jdbcType=VARCHAR},
      </if>
      <if test="record.createdBy != null" >
        CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdDate != null" >
        CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedBy != null" >
        ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.modifiedDate != null" >
        ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tre_report_matrix_relationship
    set ID = #{record.ID,jdbcType=VARCHAR},
      ReportID = #{record.reportID,jdbcType=VARCHAR},
      TestMatrixID = #{record.testMatrixID,jdbcType=VARCHAR},
      CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.ReportMatrixRelationShipInfoPO" >
    update tre_report_matrix_relationship
    <set >
      <if test="reportID != null" >
        ReportID = #{reportID,jdbcType=VARCHAR},
      </if>
      <if test="testMatrixID != null" >
        TestMatrixID = #{testMatrixID,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        CreatedBy = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        ModifiedBy = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where ID = #{ID,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.ReportMatrixRelationShipInfoPO" >
    update tre_report_matrix_relationship
    set ReportID = #{reportID,jdbcType=VARCHAR},
      TestMatrixID = #{testMatrixID,jdbcType=VARCHAR},
      CreatedBy = #{createdBy,jdbcType=VARCHAR},
      CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{modifiedBy,jdbcType=VARCHAR},
      ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP}
    where ID = #{ID,jdbcType=VARCHAR}
  </update>
</mapper>