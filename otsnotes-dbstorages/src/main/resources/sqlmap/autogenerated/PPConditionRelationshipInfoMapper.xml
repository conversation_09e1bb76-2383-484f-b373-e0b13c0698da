<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.otsnotes.dbstorages.mybatis.mapper.PPConditionRelationshipInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.otsnotes.dbstorages.mybatis.model.PPConditionRelationshipInfoPO" >
    <id column="ID" property="ID" jdbcType="VARCHAR" />
    <result column="PPTestLineRelID" property="PPTestLineRelID" jdbcType="VARCHAR" />
    <result column="TestConditionTypeID" property="testConditionTypeID" jdbcType="INTEGER" />
    <result column="TestConditionID" property="testConditionID" jdbcType="INTEGER" />
    <result column="TestConditionName" property="testConditionName" jdbcType="VARCHAR" />
    <result column="TestConditionDesc" property="testConditionDesc" jdbcType="VARCHAR" />
    <result column="TestConditionSeq" property="testConditionSeq" jdbcType="INTEGER" />
    <result column="IsConditionTypeBlock" property="isConditionTypeBlock" jdbcType="BIT" />
    <result column="TestConditionTypeName" property="testConditionTypeName" jdbcType="VARCHAR" />
    <result column="ConditionTypeBlockLevel" property="conditionTypeBlockLevel" jdbcType="INTEGER" />
    <result column="IsProcedureCondition" property="isProcedureCondition" jdbcType="BIT" />
    <result column="ActiveIndicator" property="activeIndicator" jdbcType="BIT" />
    <result column="CreatedDate" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="CreatedBy" property="createdBy" jdbcType="VARCHAR" />
    <result column="ModifiedDate" property="modifiedDate" jdbcType="TIMESTAMP" />
    <result column="ModifiedBy" property="modifiedBy" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    ID, PPTestLineRelID, TestConditionTypeID, TestConditionID, TestConditionName, TestConditionDesc, 
    TestConditionSeq, IsConditionTypeBlock, TestConditionTypeName, ConditionTypeBlockLevel, 
    IsProcedureCondition, ActiveIndicator, CreatedDate, CreatedBy, ModifiedDate, ModifiedBy
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.PPConditionRelationshipInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tre_pp_condition_relationship
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from tre_pp_condition_relationship
    where ID = #{ID,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from tre_pp_condition_relationship
    where ID = #{ID,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.PPConditionRelationshipInfoExample" >
    delete from tre_pp_condition_relationship
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.PPConditionRelationshipInfoPO" >
    insert into tre_pp_condition_relationship (ID, PPTestLineRelID, TestConditionTypeID, 
      TestConditionID, TestConditionName, TestConditionDesc, 
      TestConditionSeq, IsConditionTypeBlock, TestConditionTypeName, 
      ConditionTypeBlockLevel, IsProcedureCondition, 
      ActiveIndicator, CreatedDate, CreatedBy, 
      ModifiedDate, ModifiedBy)
    values (#{ID,jdbcType=VARCHAR}, #{PPTestLineRelID,jdbcType=VARCHAR}, #{testConditionTypeID,jdbcType=INTEGER}, 
      #{testConditionID,jdbcType=INTEGER}, #{testConditionName,jdbcType=VARCHAR}, #{testConditionDesc,jdbcType=VARCHAR}, 
      #{testConditionSeq,jdbcType=INTEGER}, #{isConditionTypeBlock,jdbcType=BIT}, #{testConditionTypeName,jdbcType=VARCHAR}, 
      #{conditionTypeBlockLevel,jdbcType=INTEGER}, #{isProcedureCondition,jdbcType=BIT}, 
      #{activeIndicator,jdbcType=BIT}, #{createdDate,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=VARCHAR}, 
      #{modifiedDate,jdbcType=TIMESTAMP}, #{modifiedBy,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.PPConditionRelationshipInfoPO" >
    insert into tre_pp_condition_relationship
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="ID != null" >
        ID,
      </if>
      <if test="PPTestLineRelID != null" >
        PPTestLineRelID,
      </if>
      <if test="testConditionTypeID != null" >
        TestConditionTypeID,
      </if>
      <if test="testConditionID != null" >
        TestConditionID,
      </if>
      <if test="testConditionName != null" >
        TestConditionName,
      </if>
      <if test="testConditionDesc != null" >
        TestConditionDesc,
      </if>
      <if test="testConditionSeq != null" >
        TestConditionSeq,
      </if>
      <if test="isConditionTypeBlock != null" >
        IsConditionTypeBlock,
      </if>
      <if test="testConditionTypeName != null" >
        TestConditionTypeName,
      </if>
      <if test="conditionTypeBlockLevel != null" >
        ConditionTypeBlockLevel,
      </if>
      <if test="isProcedureCondition != null" >
        IsProcedureCondition,
      </if>
      <if test="activeIndicator != null" >
        ActiveIndicator,
      </if>
      <if test="createdDate != null" >
        CreatedDate,
      </if>
      <if test="createdBy != null" >
        CreatedBy,
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate,
      </if>
      <if test="modifiedBy != null" >
        ModifiedBy,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="ID != null" >
        #{ID,jdbcType=VARCHAR},
      </if>
      <if test="PPTestLineRelID != null" >
        #{PPTestLineRelID,jdbcType=VARCHAR},
      </if>
      <if test="testConditionTypeID != null" >
        #{testConditionTypeID,jdbcType=INTEGER},
      </if>
      <if test="testConditionID != null" >
        #{testConditionID,jdbcType=INTEGER},
      </if>
      <if test="testConditionName != null" >
        #{testConditionName,jdbcType=VARCHAR},
      </if>
      <if test="testConditionDesc != null" >
        #{testConditionDesc,jdbcType=VARCHAR},
      </if>
      <if test="testConditionSeq != null" >
        #{testConditionSeq,jdbcType=INTEGER},
      </if>
      <if test="isConditionTypeBlock != null" >
        #{isConditionTypeBlock,jdbcType=BIT},
      </if>
      <if test="testConditionTypeName != null" >
        #{testConditionTypeName,jdbcType=VARCHAR},
      </if>
      <if test="conditionTypeBlockLevel != null" >
        #{conditionTypeBlockLevel,jdbcType=INTEGER},
      </if>
      <if test="isProcedureCondition != null" >
        #{isProcedureCondition,jdbcType=BIT},
      </if>
      <if test="activeIndicator != null" >
        #{activeIndicator,jdbcType=BIT},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.PPConditionRelationshipInfoExample" resultType="java.lang.Integer" >
    select count(*) from tre_pp_condition_relationship
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tre_pp_condition_relationship
    <set >
      <if test="record.ID != null" >
        ID = #{record.ID,jdbcType=VARCHAR},
      </if>
      <if test="record.PPTestLineRelID != null" >
        PPTestLineRelID = #{record.PPTestLineRelID,jdbcType=VARCHAR},
      </if>
      <if test="record.testConditionTypeID != null" >
        TestConditionTypeID = #{record.testConditionTypeID,jdbcType=INTEGER},
      </if>
      <if test="record.testConditionID != null" >
        TestConditionID = #{record.testConditionID,jdbcType=INTEGER},
      </if>
      <if test="record.testConditionName != null" >
        TestConditionName = #{record.testConditionName,jdbcType=VARCHAR},
      </if>
      <if test="record.testConditionDesc != null" >
        TestConditionDesc = #{record.testConditionDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.testConditionSeq != null" >
        TestConditionSeq = #{record.testConditionSeq,jdbcType=INTEGER},
      </if>
      <if test="record.isConditionTypeBlock != null" >
        IsConditionTypeBlock = #{record.isConditionTypeBlock,jdbcType=BIT},
      </if>
      <if test="record.testConditionTypeName != null" >
        TestConditionTypeName = #{record.testConditionTypeName,jdbcType=VARCHAR},
      </if>
      <if test="record.conditionTypeBlockLevel != null" >
        ConditionTypeBlockLevel = #{record.conditionTypeBlockLevel,jdbcType=INTEGER},
      </if>
      <if test="record.isProcedureCondition != null" >
        IsProcedureCondition = #{record.isProcedureCondition,jdbcType=BIT},
      </if>
      <if test="record.activeIndicator != null" >
        ActiveIndicator = #{record.activeIndicator,jdbcType=BIT},
      </if>
      <if test="record.createdDate != null" >
        CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdBy != null" >
        CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.modifiedDate != null" >
        ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedBy != null" >
        ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tre_pp_condition_relationship
    set ID = #{record.ID,jdbcType=VARCHAR},
      PPTestLineRelID = #{record.PPTestLineRelID,jdbcType=VARCHAR},
      TestConditionTypeID = #{record.testConditionTypeID,jdbcType=INTEGER},
      TestConditionID = #{record.testConditionID,jdbcType=INTEGER},
      TestConditionName = #{record.testConditionName,jdbcType=VARCHAR},
      TestConditionDesc = #{record.testConditionDesc,jdbcType=VARCHAR},
      TestConditionSeq = #{record.testConditionSeq,jdbcType=INTEGER},
      IsConditionTypeBlock = #{record.isConditionTypeBlock,jdbcType=BIT},
      TestConditionTypeName = #{record.testConditionTypeName,jdbcType=VARCHAR},
      ConditionTypeBlockLevel = #{record.conditionTypeBlockLevel,jdbcType=INTEGER},
      IsProcedureCondition = #{record.isProcedureCondition,jdbcType=BIT},
      ActiveIndicator = #{record.activeIndicator,jdbcType=BIT},
      CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.PPConditionRelationshipInfoPO" >
    update tre_pp_condition_relationship
    <set >
      <if test="PPTestLineRelID != null" >
        PPTestLineRelID = #{PPTestLineRelID,jdbcType=VARCHAR},
      </if>
      <if test="testConditionTypeID != null" >
        TestConditionTypeID = #{testConditionTypeID,jdbcType=INTEGER},
      </if>
      <if test="testConditionID != null" >
        TestConditionID = #{testConditionID,jdbcType=INTEGER},
      </if>
      <if test="testConditionName != null" >
        TestConditionName = #{testConditionName,jdbcType=VARCHAR},
      </if>
      <if test="testConditionDesc != null" >
        TestConditionDesc = #{testConditionDesc,jdbcType=VARCHAR},
      </if>
      <if test="testConditionSeq != null" >
        TestConditionSeq = #{testConditionSeq,jdbcType=INTEGER},
      </if>
      <if test="isConditionTypeBlock != null" >
        IsConditionTypeBlock = #{isConditionTypeBlock,jdbcType=BIT},
      </if>
      <if test="testConditionTypeName != null" >
        TestConditionTypeName = #{testConditionTypeName,jdbcType=VARCHAR},
      </if>
      <if test="conditionTypeBlockLevel != null" >
        ConditionTypeBlockLevel = #{conditionTypeBlockLevel,jdbcType=INTEGER},
      </if>
      <if test="isProcedureCondition != null" >
        IsProcedureCondition = #{isProcedureCondition,jdbcType=BIT},
      </if>
      <if test="activeIndicator != null" >
        ActiveIndicator = #{activeIndicator,jdbcType=BIT},
      </if>
      <if test="createdDate != null" >
        CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        CreatedBy = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        ModifiedBy = #{modifiedBy,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{ID,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.PPConditionRelationshipInfoPO" >
    update tre_pp_condition_relationship
    set PPTestLineRelID = #{PPTestLineRelID,jdbcType=VARCHAR},
      TestConditionTypeID = #{testConditionTypeID,jdbcType=INTEGER},
      TestConditionID = #{testConditionID,jdbcType=INTEGER},
      TestConditionName = #{testConditionName,jdbcType=VARCHAR},
      TestConditionDesc = #{testConditionDesc,jdbcType=VARCHAR},
      TestConditionSeq = #{testConditionSeq,jdbcType=INTEGER},
      IsConditionTypeBlock = #{isConditionTypeBlock,jdbcType=BIT},
      TestConditionTypeName = #{testConditionTypeName,jdbcType=VARCHAR},
      ConditionTypeBlockLevel = #{conditionTypeBlockLevel,jdbcType=INTEGER},
      IsProcedureCondition = #{isProcedureCondition,jdbcType=BIT},
      ActiveIndicator = #{activeIndicator,jdbcType=BIT},
      CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      CreatedBy = #{createdBy,jdbcType=VARCHAR},
      ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{modifiedBy,jdbcType=VARCHAR}
    where ID = #{ID,jdbcType=VARCHAR}
  </update>
</mapper>