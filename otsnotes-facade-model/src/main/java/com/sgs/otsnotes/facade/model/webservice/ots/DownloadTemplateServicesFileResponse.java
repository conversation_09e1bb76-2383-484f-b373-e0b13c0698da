/**
 * DownloadTemplateServicesFileResponse.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class DownloadTemplateServicesFileResponse  implements java.io.Serializable {
    private byte[] downloadTemplateServicesFileResult;

    private boolean isExists;

    public DownloadTemplateServicesFileResponse() {
    }

    public DownloadTemplateServicesFileResponse(
           byte[] downloadTemplateServicesFileResult,
           boolean isExists) {
           this.downloadTemplateServicesFileResult = downloadTemplateServicesFileResult;
           this.isExists = isExists;
    }


    /**
     * Gets the downloadTemplateServicesFileResult value for this DownloadTemplateServicesFileResponse.
     * 
     * @return downloadTemplateServicesFileResult
     */
    public byte[] getDownloadTemplateServicesFileResult() {
        return downloadTemplateServicesFileResult;
    }


    /**
     * Sets the downloadTemplateServicesFileResult value for this DownloadTemplateServicesFileResponse.
     * 
     * @param downloadTemplateServicesFileResult
     */
    public void setDownloadTemplateServicesFileResult(byte[] downloadTemplateServicesFileResult) {
        this.downloadTemplateServicesFileResult = downloadTemplateServicesFileResult;
    }


    /**
     * Gets the isExists value for this DownloadTemplateServicesFileResponse.
     * 
     * @return isExists
     */
    public boolean isIsExists() {
        return isExists;
    }


    /**
     * Sets the isExists value for this DownloadTemplateServicesFileResponse.
     * 
     * @param isExists
     */
    public void setIsExists(boolean isExists) {
        this.isExists = isExists;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof DownloadTemplateServicesFileResponse)) {
            return false;
        }
        DownloadTemplateServicesFileResponse other = (DownloadTemplateServicesFileResponse) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.downloadTemplateServicesFileResult==null && other.getDownloadTemplateServicesFileResult()==null) || 
             (this.downloadTemplateServicesFileResult!=null &&
              java.util.Arrays.equals(this.downloadTemplateServicesFileResult, other.getDownloadTemplateServicesFileResult()))) &&
            this.isExists == other.isIsExists();
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getDownloadTemplateServicesFileResult() != null) {
            for (int i=0;
                 i<java.lang.reflect.Array.getLength(getDownloadTemplateServicesFileResult());
                 i++) {
                Object obj = java.lang.reflect.Array.get(getDownloadTemplateServicesFileResult(), i);
                if (obj != null &&
                    !obj.getClass().isArray()) {
                    _hashCode += obj.hashCode();
                }
            }
        }
        _hashCode += (isIsExists() ? Boolean.TRUE : Boolean.FALSE).hashCode();
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(DownloadTemplateServicesFileResponse.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", ">DownloadTemplateServicesFileResponse"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("downloadTemplateServicesFileResult");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "DownloadTemplateServicesFileResult"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "base64Binary"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("isExists");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "isExists"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "boolean"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
