<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.otsnotes.dbstorages.mybatis.mapper.CustomTestCategoryLangInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.otsnotes.dbstorages.mybatis.model.CustomTestCategoryLangInfoPO" >
    <id column="Id" property="id" jdbcType="INTEGER" />
    <result column="CustomTestCategoryBaseId" property="customTestCategoryBaseId" jdbcType="BIGINT" />
    <result column="CustomTestCategoryId" property="customTestCategoryId" jdbcType="INTEGER" />
    <result column="CustomTestCategoryName" property="customTestCategoryName" jdbcType="VARCHAR" />
    <result column="LanguageId" property="languageId" jdbcType="INTEGER" />
    <result column="BizVersionId" property="bizVersionId" jdbcType="CHAR" />
    <result column="Status" property="status" jdbcType="INTEGER" />
    <result column="CreatedDate" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="ModifiedDate" property="modifiedDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    Id, CustomTestCategoryBaseId, CustomTestCategoryId, CustomTestCategoryName, LanguageId, 
    BizVersionId, `Status`, CreatedDate, ModifiedDate
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.CustomTestCategoryLangInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_trims_custom_testcategory_language
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from tb_trims_custom_testcategory_language
    where Id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from tb_trims_custom_testcategory_language
    where Id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.CustomTestCategoryLangInfoExample" >
    delete from tb_trims_custom_testcategory_language
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.CustomTestCategoryLangInfoPO" >
    insert into tb_trims_custom_testcategory_language (Id, CustomTestCategoryBaseId, CustomTestCategoryId,
      CustomTestCategoryName, LanguageId, BizVersionId, 
      `Status`, CreatedDate, ModifiedDate
      )
    values (#{id,jdbcType=INTEGER}, #{customTestCategoryBaseId,jdbcType=BIGINT}, #{customTestCategoryId,jdbcType=INTEGER}, 
      #{customTestCategoryName,jdbcType=VARCHAR}, #{languageId,jdbcType=INTEGER}, #{bizVersionId,jdbcType=CHAR}, 
      #{status,jdbcType=INTEGER}, #{createdDate,jdbcType=TIMESTAMP}, #{modifiedDate,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.CustomTestCategoryLangInfoPO" >
    insert into tb_trims_custom_testcategory_language
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        Id,
      </if>
      <if test="customTestCategoryBaseId != null" >
        CustomTestCategoryBaseId,
      </if>
      <if test="customTestCategoryId != null" >
        CustomTestCategoryId,
      </if>
      <if test="customTestCategoryName != null" >
        CustomTestCategoryName,
      </if>
      <if test="languageId != null" >
        LanguageId,
      </if>
      <if test="bizVersionId != null" >
        BizVersionId,
      </if>
      <if test="status != null" >
        `Status`,
      </if>
      <if test="createdDate != null" >
        CreatedDate,
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="customTestCategoryBaseId != null" >
        #{customTestCategoryBaseId,jdbcType=BIGINT},
      </if>
      <if test="customTestCategoryId != null" >
        #{customTestCategoryId,jdbcType=INTEGER},
      </if>
      <if test="customTestCategoryName != null" >
        #{customTestCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="languageId != null" >
        #{languageId,jdbcType=INTEGER},
      </if>
      <if test="bizVersionId != null" >
        #{bizVersionId,jdbcType=CHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.CustomTestCategoryLangInfoExample" resultType="java.lang.Integer" >
    select count(*) from tb_trims_custom_testcategory_language
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_trims_custom_testcategory_language
    <set >
      <if test="record.id != null" >
        Id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.customTestCategoryBaseId != null" >
        CustomTestCategoryBaseId = #{record.customTestCategoryBaseId,jdbcType=BIGINT},
      </if>
      <if test="record.customTestCategoryId != null" >
        CustomTestCategoryId = #{record.customTestCategoryId,jdbcType=INTEGER},
      </if>
      <if test="record.customTestCategoryName != null" >
        CustomTestCategoryName = #{record.customTestCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="record.languageId != null" >
        LanguageId = #{record.languageId,jdbcType=INTEGER},
      </if>
      <if test="record.bizVersionId != null" >
        BizVersionId = #{record.bizVersionId,jdbcType=CHAR},
      </if>
      <if test="record.status != null" >
        `Status` = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.createdDate != null" >
        CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedDate != null" >
        ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_trims_custom_testcategory_language
    set Id = #{record.id,jdbcType=INTEGER},
      CustomTestCategoryBaseId = #{record.customTestCategoryBaseId,jdbcType=BIGINT},
      CustomTestCategoryId = #{record.customTestCategoryId,jdbcType=INTEGER},
      CustomTestCategoryName = #{record.customTestCategoryName,jdbcType=VARCHAR},
      LanguageId = #{record.languageId,jdbcType=INTEGER},
      BizVersionId = #{record.bizVersionId,jdbcType=CHAR},
      `Status` = #{record.status,jdbcType=INTEGER},
      CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.CustomTestCategoryLangInfoPO" >
    update tb_trims_custom_testcategory_language
    <set >
      <if test="customTestCategoryBaseId != null" >
        CustomTestCategoryBaseId = #{customTestCategoryBaseId,jdbcType=BIGINT},
      </if>
      <if test="customTestCategoryId != null" >
        CustomTestCategoryId = #{customTestCategoryId,jdbcType=INTEGER},
      </if>
      <if test="customTestCategoryName != null" >
        CustomTestCategoryName = #{customTestCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="languageId != null" >
        LanguageId = #{languageId,jdbcType=INTEGER},
      </if>
      <if test="bizVersionId != null" >
        BizVersionId = #{bizVersionId,jdbcType=CHAR},
      </if>
      <if test="status != null" >
        `Status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="createdDate != null" >
        CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where Id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.CustomTestCategoryLangInfoPO" >
    update tb_trims_custom_testcategory_language
    set CustomTestCategoryBaseId = #{customTestCategoryBaseId,jdbcType=BIGINT},
      CustomTestCategoryId = #{customTestCategoryId,jdbcType=INTEGER},
      CustomTestCategoryName = #{customTestCategoryName,jdbcType=VARCHAR},
      LanguageId = #{languageId,jdbcType=INTEGER},
      BizVersionId = #{bizVersionId,jdbcType=CHAR},
      `Status` = #{status,jdbcType=INTEGER},
      CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP}
    where Id = #{id,jdbcType=INTEGER}
  </update>
</mapper>