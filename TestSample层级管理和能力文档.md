# TestSample与TestSampleGroupInfoPO树型结构设计文档 v2.0

## 文档说明
本文档基于树型结构的方式重新设计了TestSample对象的层级管理架构，将TestSampleGroupInfoPO与TestSample整合为统一的树型结构体系，实现层级化的样本和分组管理。通过抽象基类TreeNode提供统一的树型结构能力，支持灵活的节点类型和层级关系管理。

## 业务概述
TestSample和TestSampleGroupInfoPO共同构成一个完整的树型体系，其中TestSampleGroupInfoPO作为分组节点负责组织和管理，TestSample作为样本节点承载具体的测试内容。通过统一的树型结构实现垂直层级管理，支持动态调整节点关系和类型。

## 核心特性
- **统一树型结构**：TestSampleGroupInfoPO和TestSample共同构成完整树型体系
- **多层级管理**：支持5层级的深度管理（Level 0-4）
- **节点类型区分**：GROUP节点（分组）和SAMPLE节点（样本）
- **灵活扩展**：支持运行时动态调整树型结构和节点关系
- **性能优化**：通过关系表和索引表优化查询性能

---

## 树型结构视图

### 完整树型结构图

```mermaid
graph TD
    %% 根节点 - 分组类型
    Root[测试项目根分组<br/>TestSampleGroupInfoPO<br/>Level 0 - GROUP]

    %% 第一层 - 功能模块分组
    ModuleA[用户管理模块<br/>TestSampleGroupInfoPO<br/>Level 1 - GROUP<br/>Type: FUNCTIONAL]
    ModuleB[订单管理模块<br/>TestSampleGroupInfoPO<br/>Level 1 - GROUP<br/>Type: FUNCTIONAL]
    ModuleC[支付管理模块<br/>TestSampleGroupInfoPO<br/>Level 1 - GROUP<br/>Type: FUNCTIONAL]

    %% 第二层 - 测试分类分组
    CategoryA1[登录测试分组<br/>TestSampleGroupInfoPO<br/>Level 2 - GROUP<br/>Type: CATEGORY]
    CategoryA2[权限测试分组<br/>TestSampleGroupInfoPO<br/>Level 2 - GROUP<br/>Type: CATEGORY]

    CategoryB1[订单创建分组<br/>TestSampleGroupInfoPO<br/>Level 2 - GROUP<br/>Type: CATEGORY]
    CategoryB2[订单查询分组<br/>TestSampleGroupInfoPO<br/>Level 2 - GROUP<br/>Type: CATEGORY]

    %% 第三层 - 具体测试样本
    SampleA1[用户名密码登录<br/>TestSample<br/>Level 3 - SAMPLE<br/>Type: TEST_CASE]
    SampleA2[手机号登录<br/>TestSample<br/>Level 3 - SAMPLE<br/>Type: TEST_CASE]
    SampleA3[第三方登录<br/>TestSample<br/>Level 3 - SAMPLE<br/>Type: TEST_CASE]

    SampleA4[管理员权限测试<br/>TestSample<br/>Level 3 - SAMPLE<br/>Type: TEST_CASE]
    SampleA5[普通用户权限测试<br/>TestSample<br/>Level 3 - SAMPLE<br/>Type: TEST_CASE]

    SampleB1[创建普通订单<br/>TestSample<br/>Level 3 - SAMPLE<br/>Type: TEST_CASE]
    SampleB2[创建批量订单<br/>TestSample<br/>Level 3 - SAMPLE<br/>Type: TEST_CASE]

    SampleB3[按订单号查询<br/>TestSample<br/>Level 3 - SAMPLE<br/>Type: TEST_CASE]
    SampleB4[按时间范围查询<br/>TestSample<br/>Level 3 - SAMPLE<br/>Type: TEST_CASE]

    %% 第四层 - 测试数据样本
    DataA1[登录测试数据集1<br/>TestSample<br/>Level 4 - SAMPLE<br/>Type: TEST_DATA]
    DataA2[登录测试数据集2<br/>TestSample<br/>Level 4 - SAMPLE<br/>Type: TEST_DATA]

    DataB1[订单创建数据集<br/>TestSample<br/>Level 4 - SAMPLE<br/>Type: TEST_DATA]

    %% 树型结构连接关系
    Root --> ModuleA
    Root --> ModuleB
    Root --> ModuleC

    ModuleA --> CategoryA1
    ModuleA --> CategoryA2

    ModuleB --> CategoryB1
    ModuleB --> CategoryB2

    CategoryA1 --> SampleA1
    CategoryA1 --> SampleA2
    CategoryA1 --> SampleA3

    CategoryA2 --> SampleA4
    CategoryA2 --> SampleA5

    CategoryB1 --> SampleB1
    CategoryB1 --> SampleB2

    CategoryB2 --> SampleB3
    CategoryB2 --> SampleB4

    SampleA1 --> DataA1
    SampleA1 --> DataA2

    SampleB1 --> DataB1

    %% 样式定义
    classDef groupRoot fill:#e1f5fe,stroke:#01579b,stroke-width:3px,color:#000
    classDef groupLevel1 fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000
    classDef groupLevel2 fill:#fff3e0,stroke:#e65100,stroke-width:2px,color:#000
    classDef sampleLevel3 fill:#c8e6c9,stroke:#1b5e20,stroke-width:2px,color:#000
    classDef sampleLevel4 fill:#ffecb3,stroke:#f57f17,stroke-width:2px,color:#000

    %% 应用样式
    class Root groupRoot
    class ModuleA,ModuleB,ModuleC groupLevel1
    class CategoryA1,CategoryA2,CategoryB1,CategoryB2 groupLevel2
    class SampleA1,SampleA2,SampleA3,SampleA4,SampleA5,SampleB1,SampleB2,SampleB3,SampleB4 sampleLevel3
    class DataA1,DataA2,DataB1 sampleLevel4
```

### 层级结构示意图

```mermaid
graph TD
    subgraph "Level 0 - 项目根节点"
        Root["🏢 测试项目<br/>TestSampleGroupInfoPO<br/>Type: FUNCTIONAL<br/>管理整个测试项目"]
    end

    subgraph "Level 1 - 功能模块分组"
        Module1["📁 用户管理模块<br/>TestSampleGroupInfoPO<br/>Type: FUNCTIONAL<br/>按业务功能分组"]
        Module2["📁 订单管理模块<br/>TestSampleGroupInfoPO<br/>Type: FUNCTIONAL<br/>按业务功能分组"]
        Module3["📁 支付管理模块<br/>TestSampleGroupInfoPO<br/>Type: FUNCTIONAL<br/>按业务功能分组"]
    end

    subgraph "Level 2 - 测试分类分组"
        Category1["📂 登录测试分组<br/>TestSampleGroupInfoPO<br/>Type: CATEGORY<br/>按测试类型分组"]
        Category2["📂 权限测试分组<br/>TestSampleGroupInfoPO<br/>Type: CATEGORY<br/>按测试类型分组"]
        Category3["📂 订单创建分组<br/>TestSampleGroupInfoPO<br/>Type: CATEGORY<br/>按测试类型分组"]
    end

    subgraph "Level 3 - 测试用例样本"
        Sample1["🧪 用户名密码登录<br/>TestSample<br/>Type: TEST_CASE<br/>具体测试用例"]
        Sample2["🧪 手机号登录<br/>TestSample<br/>Type: TEST_CASE<br/>具体测试用例"]
        Sample3["🧪 管理员权限测试<br/>TestSample<br/>Type: TEST_CASE<br/>具体测试用例"]
        Sample4["🧪 创建普通订单<br/>TestSample<br/>Type: TEST_CASE<br/>具体测试用例"]
    end

    subgraph "Level 4 - 测试数据样本"
        Data1["📊 登录测试数据集1<br/>TestSample<br/>Type: TEST_DATA<br/>测试数据"]
        Data2["📊 登录测试数据集2<br/>TestSample<br/>Type: TEST_DATA<br/>测试数据"]
        Data3["📊 权限测试数据集<br/>TestSample<br/>Type: TEST_DATA<br/>测试数据"]
    end

    %% 连接关系
    Root --> Module1
    Root --> Module2
    Root --> Module3

    Module1 --> Category1
    Module1 --> Category2
    Module2 --> Category3

    Category1 --> Sample1
    Category1 --> Sample2
    Category2 --> Sample3
    Category3 --> Sample4

    Sample1 --> Data1
    Sample1 --> Data2
    Sample3 --> Data3

    %% 样式定义
    classDef level0 fill:#e3f2fd,stroke:#1976d2,stroke-width:3px,color:#000
    classDef level1 fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,color:#000
    classDef level2 fill:#fff3e0,stroke:#f57c00,stroke-width:2px,color:#000
    classDef level3 fill:#e8f5e8,stroke:#388e3c,stroke-width:2px,color:#000
    classDef level4 fill:#fff8e1,stroke:#fbc02d,stroke-width:2px,color:#000

    %% 应用样式
    class Root level0
    class Module1,Module2,Module3 level1
    class Category1,Category2,Category3 level2
    class Sample1,Sample2,Sample3,Sample4 level3
    class Data1,Data2,Data3 level4
```

---

## 对象结构重新设计

### 1. 抽象基类：TreeNode

```java
/**
 * 树型节点抽象基类
 * 为TestSample和TestSampleGroupInfoPO提供统一的树型结构能力
 */
@MappedSuperclass
public abstract class TreeNode {

    /** 节点ID */
    @Id
    private String id;

    /** 节点名称 */
    @Column(name = "name", nullable = false, length = 100)
    private String name;

    /** 节点编码 */
    @Column(name = "code", unique = true, nullable = false, length = 50)
    private String code;

    /** 父节点ID */
    @Column(name = "parent_id")
    private String parentId;

    /** 节点层级深度 */
    @Column(name = "level", nullable = false)
    private Integer level;

    /** 节点类型 */
    @Enumerated(EnumType.STRING)
    @Column(name = "node_type", nullable = false)
    private NodeType nodeType;

    /** 排序序号 */
    @Column(name = "sort_order")
    private Integer sortOrder;

    /** 节点状态 */
    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private NodeStatus status;

    /** 扩展属性 */
    @Column(name = "properties", columnDefinition = "JSON")
    private String properties;

    /** 创建信息 */
    @Column(name = "created_by", nullable = false)
    private String createdBy;

    @Column(name = "created_date", nullable = false)
    private Date createdDate;

    @Column(name = "modified_by")
    private String modifiedBy;

    @Column(name = "modified_date")
    private Date modifiedDate;

    // 抽象方法，由子类实现
    public abstract NodeType getNodeType();
    public abstract List<TreeNode> getChildren();
    public abstract void addChild(TreeNode child);
    public abstract void removeChild(TreeNode child);
}
```

### 2. 重新设计的TestSampleGroupInfoPO

```java
/**
 * 测试样本分组信息PO - 树型结构中的分组节点
 * 继承TreeNode，具备完整的树型结构能力
 */
@Entity
@Table(name = "test_sample_group_info")
public class TestSampleGroupInfoPO extends TreeNode {

    /** 分组类型 */
    @Enumerated(EnumType.STRING)
    @Column(name = "group_type", nullable = false)
    private GroupType groupType;

    /** 分组描述 */
    @Column(name = "description", length = 500)
    private String description;

    /** 分组配置 */
    @Column(name = "group_config", columnDefinition = "JSON")
    private String groupConfig;

    /** 是否允许包含子分组 */
    @Column(name = "allow_sub_groups")
    private Boolean allowSubGroups = true;

    /** 是否允许包含样本 */
    @Column(name = "allow_samples")
    private Boolean allowSamples = true;

    /** 最大子节点数量限制 */
    @Column(name = "max_children_count")
    private Integer maxChildrenCount;

    /** 子节点集合（延迟加载） */
    @Transient
    private List<TreeNode> children = new ArrayList<>();

    @Override
    public NodeType getNodeType() {
        return NodeType.GROUP;
    }

    @Override
    public List<TreeNode> getChildren() {
        if (children.isEmpty()) {
            // 延迟加载子节点
            loadChildren();
        }
        return children;
    }

    @Override
    public void addChild(TreeNode child) {
        if (child != null) {
            child.setParentId(this.getId());
            child.setLevel(this.getLevel() + 1);
            children.add(child);
        }
    }

    @Override
    public void removeChild(TreeNode child) {
        if (child != null) {
            children.remove(child);
            child.setParentId(null);
        }
    }

    /**
     * 延迟加载子节点
     */
    private void loadChildren() {
        // 通过Repository加载子节点
        // children = treeNodeRepository.findByParentId(this.getId());
    }
}
```

### 3. 重新设计的TestSample

```java
/**
 * 测试样本PO - 树型结构中的样本节点
 * 继承TreeNode，具备完整的树型结构能力
 */
@Entity
@Table(name = "test_sample")
public class TestSample extends TreeNode {

    /** 样本类型 */
    @Enumerated(EnumType.STRING)
    @Column(name = "sample_type", nullable = false)
    private SampleType sampleType;

    /** 测试数据 */
    @Column(name = "test_data", columnDefinition = "JSON")
    private String testData;

    /** 测试配置 */
    @Column(name = "test_config", columnDefinition = "JSON")
    private String testConfig;

    /** 执行状态 */
    @Enumerated(EnumType.STRING)
    @Column(name = "execution_status")
    private ExecutionStatus executionStatus;

    /** 测试结果 */
    @Column(name = "test_result", columnDefinition = "JSON")
    private String testResult;

    /** 是否为叶子节点 */
    @Column(name = "is_leaf")
    private Boolean isLeaf = false;

    /** 子节点集合（延迟加载） */
    @Transient
    private List<TreeNode> children = new ArrayList<>();

    @Override
    public NodeType getNodeType() {
        return NodeType.SAMPLE;
    }

    @Override
    public List<TreeNode> getChildren() {
        if (!isLeaf && children.isEmpty()) {
            // 延迟加载子节点
            loadChildren();
        }
        return children;
    }

    @Override
    public void addChild(TreeNode child) {
        if (!isLeaf && child != null) {
            child.setParentId(this.getId());
            child.setLevel(this.getLevel() + 1);
            children.add(child);
        }
    }

    @Override
    public void removeChild(TreeNode child) {
        if (child != null) {
            children.remove(child);
            child.setParentId(null);
        }
    }

    /**
     * 延迟加载子节点
     */
    private void loadChildren() {
        // 通过Repository加载子节点
        // children = treeNodeRepository.findByParentId(this.getId());
    }
}
```

### Group对象属性

| 字段名 | 类型 | 说明 | 约束条件 |
|--------|------|------|----------|
| id | String | 唯一标识符 | 主键，非空 |
| name | String | 分组名称 | 非空，长度1-50 |
| type | GroupType | 分组类型 | SHARE或MIX |
| description | String | 分组描述 | 可空，长度0-200 |
| samples | List<TestSample> | 关联的样本集合 | 多对多关系 |
| priority | Integer | 优先级 | 1-100，默认50 |
| isActive | Boolean | 是否激活 | 默认true |
| createdBy | String | 创建人 | 非空 |
| createdDate | Date | 创建时间 | 非空 |

### 枚举定义

#### SampleType（样本类型）
```java
public enum SampleType {
    ROOT("根节点样本"),
    BRANCH("分支节点样本"), 
    LEAF("叶子节点样本"),
    TEMPLATE("模板样本");
}
```

#### SampleStatus（样本状态）
```java
public enum SampleStatus {
    DRAFT("草稿"),
    ACTIVE("激活"),
    INACTIVE("停用"),
    ARCHIVED("归档");
}
```

#### GroupType（分组类型）
```java
public enum GroupType {
    SHARE("共享型分组 - 样本间共享资源和配置"),
    MIX("混合型分组 - 样本间建立复杂业务关系");
}
```

---

## 层级管理架构

### 三层级结构设计

```
Level 0 (根节点)
├── Level 1 (中间节点)
│   ├── Level 2 (叶子节点)
│   ├── Level 2 (叶子节点)
│   └── Level 2 (叶子节点)
├── Level 1 (中间节点)
│   ├── Level 2 (叶子节点)
│   └── Level 2 (叶子节点)
└── Level 1 (中间节点)
    └── Level 2 (叶子节点)
```

### 层级约束规则

| 层级 | 名称 | 约束条件 | 业务含义 |
|------|------|----------|----------|
| Level 0 | 根节点 | parentId为null，可有多个子节点 | 测试项目或测试套件 |
| Level 1 | 中间节点 | 必须有父节点，可有多个子节点 | 测试模块或测试分类 |
| Level 2 | 叶子节点 | 必须有父节点，不能有子节点 | 具体测试用例或测试数据 |

### 层级操作能力

#### 1. 节点创建
```java
/**
 * 创建子节点
 * 
 * @param parentId 父节点ID
 * @param sample 待创建的样本对象
 * @return 创建结果
 * @throws BusinessException 当层级深度超限时抛出异常
 */
public TestSample createChild(String parentId, TestSample sample) {
    // 1. 验证父节点存在性
    TestSample parent = findById(parentId);
    if (parent == null) {
        throw new BusinessException("父节点不存在");
    }
    
    // 2. 验证层级深度限制
    if (parent.getLevel() >= 2) {
        throw new BusinessException("已达到最大层级深度，无法创建子节点");
    }
    
    // 3. 设置层级关系
    sample.setParentId(parentId);
    sample.setLevel(parent.getLevel() + 1);
    
    // 4. 保存并返回
    return save(sample);
}
```

#### 2. 节点移动
```java
/**
 * 移动节点到新的父节点下
 * 
 * @param nodeId 待移动节点ID
 * @param newParentId 新父节点ID
 * @return 移动结果
 */
public boolean moveNode(String nodeId, String newParentId) {
    // 1. 获取待移动节点及其所有子节点
    TestSample node = findById(nodeId);
    List<TestSample> descendants = findAllDescendants(nodeId);
    
    // 2. 验证移动后的层级深度
    TestSample newParent = findById(newParentId);
    int maxDescendantDepth = calculateMaxDepth(descendants);
    if (newParent.getLevel() + 1 + maxDescendantDepth > 2) {
        throw new BusinessException("移动后将超过最大层级深度限制");
    }
    
    // 3. 更新层级关系
    updateNodeHierarchy(node, newParent);
    
    return true;
}
```

#### 3. 节点删除
```java
/**
 * 删除节点（级联删除所有子节点）
 * 
 * @param nodeId 节点ID
 * @return 删除结果
 */
public boolean deleteNode(String nodeId) {
    // 1. 获取所有子孙节点
    List<TestSample> descendants = findAllDescendants(nodeId);
    
    // 2. 检查Group关联关系
    checkGroupRelations(nodeId, descendants);
    
    // 3. 级联删除
    deleteNodesWithRelations(nodeId, descendants);
    
    return true;
}
```

---

## Group关系管理

### Group类型详解

#### SHARE类型分组
- **用途**：用于建立TestSample间的资源共享关系
- **特点**：
  - 分组内的样本共享配置、数据、执行环境等资源
  - 支持配置继承和覆盖机制
  - 适用于相似测试场景的样本管理
- **应用场景**：
  - 同一测试环境下的多个测试用例
  - 共享测试数据的样本集合
  - 需要统一配置管理的样本组

#### MIX类型分组  
- **用途**：用于建立TestSample间的复杂业务关系
- **特点**：
  - 支持多种关系类型（依赖、互斥、组合等）
  - 可定义复杂的执行顺序和条件
  - 支持动态关系调整
- **应用场景**：
  - 有执行依赖关系的测试序列
  - 互斥执行的测试分支
  - 需要组合执行的测试套件

### Group关系操作

#### 1. 创建分组关系
```java
/**
 * 将TestSample加入到指定Group中
 * 
 * @param sampleId 样本ID
 * @param groupId 分组ID
 * @param relationProperties 关系属性
 * @return 操作结果
 */
public boolean addSampleToGroup(String sampleId, String groupId, 
                               Map<String, Object> relationProperties) {
    // 1. 验证样本和分组存在性
    TestSample sample = findSampleById(sampleId);
    Group group = findGroupById(groupId);
    
    // 2. 根据Group类型执行不同的关联逻辑
    if (group.getType() == GroupType.SHARE) {
        return addToShareGroup(sample, group, relationProperties);
    } else if (group.getType() == GroupType.MIX) {
        return addToMixGroup(sample, group, relationProperties);
    }
    
    return false;
}
```

#### 2. 查询关联关系
```java
/**
 * 获取TestSample的所有Group关系
 * 
 * @param sampleId 样本ID
 * @return Group关系列表
 */
public List<GroupRelation> getSampleGroupRelations(String sampleId) {
    return groupRelationRepository.findBySampleId(sampleId);
}

/**
 * 获取指定类型的关联样本
 * 
 * @param sampleId 样本ID
 * @param groupType 分组类型
 * @return 关联样本列表
 */
public List<TestSample> getRelatedSamples(String sampleId, GroupType groupType) {
    List<Group> groups = findGroupsBySampleAndType(sampleId, groupType);
    return groups.stream()
                 .flatMap(group -> group.getSamples().stream())
                 .filter(sample -> !sample.getId().equals(sampleId))
                 .distinct()
                 .collect(Collectors.toList());
}
```

---

## 核心业务能力

### 1. 层级遍历能力
```java
/**
 * 深度优先遍历
 */
public void traverseDepthFirst(String rootId, Consumer<TestSample> visitor) {
    TestSample root = findById(rootId);
    if (root != null) {
        visitor.accept(root);
        root.getChildren().forEach(child -> 
            traverseDepthFirst(child.getId(), visitor));
    }
}

/**
 * 广度优先遍历  
 */
public void traverseBreadthFirst(String rootId, Consumer<TestSample> visitor) {
    Queue<TestSample> queue = new LinkedList<>();
    TestSample root = findById(rootId);
    if (root != null) {
        queue.offer(root);
        while (!queue.isEmpty()) {
            TestSample current = queue.poll();
            visitor.accept(current);
            queue.addAll(current.getChildren());
        }
    }
}
```

### 2. 路径查找能力
```java
/**
 * 查找从根节点到指定节点的路径
 */
public List<TestSample> findPathToRoot(String nodeId) {
    List<TestSample> path = new ArrayList<>();
    TestSample current = findById(nodeId);
    
    while (current != null) {
        path.add(0, current);
        current = current.getParentId() != null ? 
                  findById(current.getParentId()) : null;
    }
    
    return path;
}
```

### 3. 批量操作能力
```java
/**
 * 批量更新节点状态
 */
public int batchUpdateStatus(List<String> nodeIds, SampleStatus status) {
    return nodeIds.stream()
                  .mapToInt(id -> updateNodeStatus(id, status) ? 1 : 0)
                  .sum();
}
```

---

## 关系图示

### TestSample层级关系图
```mermaid
graph TD
    A[Root Sample A] --> B[Branch Sample B]
    A --> C[Branch Sample C]
    A --> D[Branch Sample D]
    
    B --> E[Leaf Sample E]
    B --> F[Leaf Sample F]
    B --> G[Leaf Sample G]
    
    C --> H[Leaf Sample H]
    C --> I[Leaf Sample I]
    
    D --> J[Leaf Sample J]
    
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#f3e5f5
    style D fill:#f3e5f5
    style E fill:#c8e6c9
    style F fill:#c8e6c9
    style G fill:#c8e6c9
    style H fill:#c8e6c9
    style I fill:#c8e6c9
    style J fill:#c8e6c9
```

### Group关系图
```mermaid
graph LR
    subgraph "SHARE Group"
        S1[Sample 1] -.共享配置.-> S2[Sample 2]
        S2 -.共享配置.-> S3[Sample 3]
        S3 -.共享配置.-> S1
    end
    
    subgraph "MIX Group"
        M1[Sample A] -->|依赖| M2[Sample B]
        M2 -->|依赖| M3[Sample C]
        M1 -.互斥.-> M4[Sample D]
    end
    
    style S1 fill:#e3f2fd
    style S2 fill:#e3f2fd
    style S3 fill:#e3f2fd
    style M1 fill:#fce4ec
    style M2 fill:#fce4ec
    style M3 fill:#fce4ec
    style M4 fill:#fce4ec
```

---

## 使用示例

### 创建层级结构示例
```java
// 1. 创建根节点
TestSample rootSample = TestSample.builder()
    .name("API测试套件")
    .code("API_TEST_SUITE_001")
    .type(SampleType.ROOT)
    .level(0)
    .build();
String rootId = testSampleService.create(rootSample);

// 2. 创建中间节点
TestSample branchSample = TestSample.builder()
    .name("用户管理模块")
    .code("USER_MODULE_001")
    .type(SampleType.BRANCH)
    .build();
String branchId = testSampleService.createChild(rootId, branchSample);

// 3. 创建叶子节点
TestSample leafSample = TestSample.builder()
    .name("用户登录测试")
    .code("USER_LOGIN_001")
    .type(SampleType.LEAF)
    .build();
testSampleService.createChild(branchId, leafSample);
```

### Group关系管理示例
```java
// 1. 创建SHARE类型分组
Group shareGroup = Group.builder()
    .name("环境配置共享组")
    .type(GroupType.SHARE)
    .description("共享测试环境配置的样本组")
    .build();
String shareGroupId = groupService.create(shareGroup);

// 2. 将样本加入SHARE分组
Map<String, Object> shareProperties = new HashMap<>();
shareProperties.put("configLevel", "high");
shareProperties.put("inheritMode", "override");
testSampleService.addSampleToGroup(leafSample.getId(), shareGroupId, shareProperties);

// 3. 创建MIX类型分组
Group mixGroup = Group.builder()
    .name("执行依赖组")
    .type(GroupType.MIX)
    .description("有执行顺序依赖的样本组")
    .build();
String mixGroupId = groupService.create(mixGroup);

// 4. 建立依赖关系
Map<String, Object> mixProperties = new HashMap<>();
mixProperties.put("relationType", "DEPENDENCY");
mixProperties.put("executionOrder", 1);
testSampleService.addSampleToGroup(leafSample.getId(), mixGroupId, mixProperties);
```

---

## 最佳实践

### 层级设计原则
1. **单一职责**：每个层级应有明确的业务含义
2. **深度控制**：严格控制在3层以内，避免过深的层级结构
3. **命名规范**：采用统一的命名规范，便于理解和维护
4. **状态管理**：合理设计状态流转，确保数据一致性

### Group使用建议
1. **类型选择**：
   - 资源共享场景优先使用SHARE类型
   - 复杂业务关系场景使用MIX类型
2. **关系维护**：
   - 定期清理无效的Group关系
   - 避免创建过于复杂的关系网络
3. **性能优化**：
   - 合理使用延迟加载
   - 对频繁查询的关系建立索引

### 【风险】注意事项
1. **循环依赖**：在MIX类型分组中要避免创建循环依赖关系
2. **层级深度**：严格控制层级深度，超过限制会影响系统性能
3. **并发安全**：在多线程环境下操作层级关系时要注意并发安全
4. **数据一致性**：删除节点时要确保相关Group关系的一致性

---

## 性能优化策略

### 查询优化
1. **索引设计**：
   - 在parentId字段上建立索引，优化层级查询
   - 在level字段上建立索引，支持按层级快速过滤
   - 在Group关联表上建立复合索引

2. **缓存策略**：
   - 对频繁访问的根节点进行缓存
   - 缓存Group关系映射，减少数据库查询
   - 使用分布式缓存存储完整的树形结构

3. **分页加载**：
   - 对大量子节点采用分页加载策略
   - 实现懒加载机制，按需加载子树

### 数据库ER图设计

```mermaid
erDiagram
    %% 统一树型节点基表
    TREE_NODE {
        varchar id PK "节点ID"
        varchar name "节点名称"
        varchar code UK "节点编码"
        varchar parent_id FK "父节点ID"
        int level "层级深度"
        varchar node_type "节点类型(GROUP/SAMPLE)"
        int sort_order "排序序号"
        varchar status "节点状态"
        json properties "扩展属性"
        varchar created_by "创建人"
        timestamp created_date "创建时间"
        varchar modified_by "修改人"
        timestamp modified_date "修改时间"
    }

    %% 测试样本分组信息表
    TEST_SAMPLE_GROUP_INFO {
        varchar id PK,FK "分组ID"
        varchar group_type "分组类型"
        varchar description "分组描述"
        json group_config "分组配置"
        boolean allow_sub_groups "允许子分组"
        boolean allow_samples "允许样本"
        int max_children_count "最大子节点数"
    }

    %% 测试样本表
    TEST_SAMPLE {
        varchar id PK,FK "样本ID"
        varchar sample_type "样本类型"
        json test_data "测试数据"
        json test_config "测试配置"
        varchar execution_status "执行状态"
        json test_result "测试结果"
        boolean is_leaf "是否叶子节点"
    }

    %% 节点关系表（用于优化查询）
    TREE_NODE_RELATION {
        varchar id PK "关系ID"
        varchar ancestor_id FK "祖先节点ID"
        varchar descendant_id FK "后代节点ID"
        int depth "深度差"
        varchar path "路径"
        timestamp created_date "创建时间"
    }

    %% 节点索引表（用于快速查询）
    TREE_NODE_INDEX {
        varchar id PK "索引ID"
        varchar node_id FK "节点ID"
        varchar root_id FK "根节点ID"
        varchar path "完整路径"
        int left_value "左值"
        int right_value "右值"
        timestamp updated_date "更新时间"
    }

    %% 关系定义
    TREE_NODE ||--o{ TREE_NODE : "parent_id"
    TREE_NODE ||--|| TEST_SAMPLE_GROUP_INFO : "id"
    TREE_NODE ||--|| TEST_SAMPLE : "id"
    TREE_NODE ||--o{ TREE_NODE_RELATION : "ancestor_id"
    TREE_NODE ||--o{ TREE_NODE_RELATION : "descendant_id"
    TREE_NODE ||--|| TREE_NODE_INDEX : "node_id"
```

### 数据库表结构SQL

```sql
-- 统一的树型节点表
CREATE TABLE tree_node (
    id VARCHAR(32) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(50) UNIQUE NOT NULL,
    parent_id VARCHAR(32),
    level INT NOT NULL DEFAULT 0,
    node_type VARCHAR(20) NOT NULL,
    sort_order INT DEFAULT 0,
    status VARCHAR(20) DEFAULT 'ACTIVE',
    properties JSON,
    created_by VARCHAR(50) NOT NULL,
    created_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    modified_by VARCHAR(50),
    modified_date TIMESTAMP,

    INDEX idx_parent_id (parent_id),
    INDEX idx_level (level),
    INDEX idx_node_type (node_type),
    INDEX idx_status (status),
    INDEX idx_code (code),
    FOREIGN KEY (parent_id) REFERENCES tree_node(id) ON DELETE CASCADE
);

-- 测试样本分组信息表
CREATE TABLE test_sample_group_info (
    id VARCHAR(32) PRIMARY KEY,
    group_type VARCHAR(20) NOT NULL,
    description VARCHAR(500),
    group_config JSON,
    allow_sub_groups BOOLEAN DEFAULT TRUE,
    allow_samples BOOLEAN DEFAULT TRUE,
    max_children_count INT,

    FOREIGN KEY (id) REFERENCES tree_node(id) ON DELETE CASCADE,
    INDEX idx_group_type (group_type)
);

-- 测试样本表
CREATE TABLE test_sample (
    id VARCHAR(32) PRIMARY KEY,
    sample_type VARCHAR(20) NOT NULL,
    test_data JSON,
    test_config JSON,
    execution_status VARCHAR(20),
    test_result JSON,
    is_leaf BOOLEAN DEFAULT FALSE,

    FOREIGN KEY (id) REFERENCES tree_node(id) ON DELETE CASCADE,
    INDEX idx_sample_type (sample_type),
    INDEX idx_execution_status (execution_status),
    INDEX idx_is_leaf (is_leaf)
);

-- 节点关系表（用于优化查询）
CREATE TABLE tree_node_relation (
    id VARCHAR(32) PRIMARY KEY,
    ancestor_id VARCHAR(32) NOT NULL,
    descendant_id VARCHAR(32) NOT NULL,
    depth INT NOT NULL,
    path VARCHAR(1000),
    created_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

    UNIQUE KEY uk_ancestor_descendant (ancestor_id, descendant_id),
    INDEX idx_ancestor_id (ancestor_id),
    INDEX idx_descendant_id (descendant_id),
    INDEX idx_depth (depth),
    FOREIGN KEY (ancestor_id) REFERENCES tree_node(id) ON DELETE CASCADE,
    FOREIGN KEY (descendant_id) REFERENCES tree_node(id) ON DELETE CASCADE
);

-- 节点索引表（用于快速查询）
CREATE TABLE tree_node_index (
    id VARCHAR(32) PRIMARY KEY,
    node_id VARCHAR(32) NOT NULL,
    root_id VARCHAR(32) NOT NULL,
    path VARCHAR(1000),
    left_value INT NOT NULL,
    right_value INT NOT NULL,
    updated_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    UNIQUE KEY uk_node_id (node_id),
    INDEX idx_root_id (root_id),
    INDEX idx_left_right (left_value, right_value),
    FOREIGN KEY (node_id) REFERENCES tree_node(id) ON DELETE CASCADE,
    FOREIGN KEY (root_id) REFERENCES tree_node(id) ON DELETE CASCADE
);
```

---

## 监控和运维

### 关键指标监控
| 监控项 | 指标说明 | 正常范围 | 告警阈值 |
|--------|----------|----------|----------|
| 层级深度 | 最大层级深度 | ≤ 2 | > 2 |
| 节点数量 | 单个父节点下的子节点数 | < 100 | > 200 |
| Group关系数 | 单个样本的Group关联数 | < 10 | > 20 |
| 查询响应时间 | 层级查询平均响应时间 | < 100ms | > 500ms |
| 内存使用 | 树形结构缓存内存占用 | < 500MB | > 1GB |

### 数据一致性检查
```java
/**
 * 数据一致性检查服务
 */
@Service
public class DataConsistencyChecker {

    /**
     * 检查层级深度一致性
     */
    public List<String> checkLevelConsistency() {
        List<String> inconsistentNodes = new ArrayList<>();

        // 检查所有节点的level字段是否与实际层级一致
        List<TestSample> allSamples = testSampleRepository.findAll();
        for (TestSample sample : allSamples) {
            int actualLevel = calculateActualLevel(sample);
            if (sample.getLevel() != actualLevel) {
                inconsistentNodes.add(sample.getId());
            }
        }

        return inconsistentNodes;
    }

    /**
     * 检查孤儿节点
     */
    public List<String> checkOrphanNodes() {
        return testSampleRepository.findOrphanNodes();
    }

    /**
     * 检查循环依赖
     */
    public List<String> checkCircularDependency() {
        // 实现循环依赖检测算法
        return detectCircularDependency();
    }
}
```

### 运维工具
```java
/**
 * TestSample运维工具类
 */
@Component
public class TestSampleMaintenanceTool {

    /**
     * 批量修复层级深度
     */
    public int fixLevelInconsistency() {
        List<String> inconsistentNodes = dataConsistencyChecker.checkLevelConsistency();
        int fixedCount = 0;

        for (String nodeId : inconsistentNodes) {
            TestSample sample = testSampleRepository.findById(nodeId);
            int correctLevel = calculateActualLevel(sample);
            sample.setLevel(correctLevel);
            testSampleRepository.save(sample);
            fixedCount++;
        }

        return fixedCount;
    }

    /**
     * 清理无效Group关系
     */
    public int cleanupInvalidGroupRelations() {
        return groupRelationRepository.deleteInvalidRelations();
    }

    /**
     * 生成层级结构报告
     */
    public HierarchyReport generateHierarchyReport() {
        return HierarchyReport.builder()
            .totalNodes(testSampleRepository.count())
            .rootNodes(testSampleRepository.countByLevel(0))
            .branchNodes(testSampleRepository.countByLevel(1))
            .leafNodes(testSampleRepository.countByLevel(2))
            .totalGroups(groupRepository.count())
            .shareGroups(groupRepository.countByType(GroupType.SHARE))
            .mixGroups(groupRepository.countByType(GroupType.MIX))
            .build();
    }
}
```

---

## 扩展性设计

### 插件化架构
```java
/**
 * TestSample操作插件接口
 */
public interface TestSamplePlugin {

    /**
     * 插件名称
     */
    String getName();

    /**
     * 支持的操作类型
     */
    Set<OperationType> getSupportedOperations();

    /**
     * 执行前置处理
     */
    void beforeOperation(TestSample sample, OperationType operation);

    /**
     * 执行后置处理
     */
    void afterOperation(TestSample sample, OperationType operation);
}

/**
 * 插件管理器
 */
@Service
public class PluginManager {

    private final List<TestSamplePlugin> plugins = new ArrayList<>();

    public void registerPlugin(TestSamplePlugin plugin) {
        plugins.add(plugin);
    }

    public void executePlugins(TestSample sample, OperationType operation, PluginPhase phase) {
        plugins.stream()
               .filter(plugin -> plugin.getSupportedOperations().contains(operation))
               .forEach(plugin -> {
                   if (phase == PluginPhase.BEFORE) {
                       plugin.beforeOperation(sample, operation);
                   } else {
                       plugin.afterOperation(sample, operation);
                   }
               });
    }
}
```

### 自定义扩展点
1. **自定义属性验证器**：支持业务特定的属性验证逻辑
2. **自定义关系处理器**：支持特殊的Group关系处理逻辑
3. **自定义序列化器**：支持不同格式的数据导入导出
4. **自定义缓存策略**：支持业务特定的缓存优化策略

---

## 版本变更记录

| 版本号 | 变更日期 | 变更内容 | 影响范围 | 变更人 |
|--------|----------|----------|----------|--------|
| v1.0 | 2025-09-29 | 初始版本，完整的层级管理和Group关系设计 | 全部功能 | 系统架构师 |

---

## 附录

### A. 常见问题解答

**Q1: 为什么限制层级深度为3层？**
A1: 基于性能和复杂度考虑，3层结构能满足大部分业务场景，同时保持良好的查询性能和维护性。

**Q2: SHARE和MIX类型的Group有什么本质区别？**
A2: SHARE类型主要用于资源共享，关系相对简单；MIX类型用于复杂业务关系，支持依赖、互斥等多种关系类型。

**Q3: 如何处理大量节点的性能问题？**
A3: 采用分页加载、缓存策略、索引优化等多种手段，具体可参考性能优化策略章节。

### B. 相关技术文档
- [TestSample API接口文档](./TestSample-API文档.md)
- [Group管理服务文档](./Group管理服务文档.md)
- [数据库设计文档](./数据库设计文档.md)

---

*文档版本：v1.0*
*最后更新：2025年9月29日*
*维护团队：测试管理开发组*
