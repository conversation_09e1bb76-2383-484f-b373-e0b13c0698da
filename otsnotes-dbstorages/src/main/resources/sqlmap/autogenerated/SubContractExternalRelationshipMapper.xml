<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.otsnotes.dbstorages.mybatis.mapper.SubContractExternalRelationshipMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.otsnotes.dbstorages.mybatis.model.SubContractExternalRelationshipPO" >
    <id column="ID" property="ID" jdbcType="VARCHAR" />
    <result column="SubContractNo" property="subContractNo" jdbcType="VARCHAR" />
    <result column="ExternalNo" property="externalNo" jdbcType="VARCHAR" />
    <result column="Status" property="status" jdbcType="BIT" />
    <result column="CreatedBy" property="createdBy" jdbcType="VARCHAR" />
    <result column="CreatedDate" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="ModifiedBy" property="modifiedBy" jdbcType="VARCHAR" />
    <result column="ModifiedDate" property="modifiedDate" jdbcType="TIMESTAMP" />
    <result column="SubContractType" property="subContractType" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    ID, SubContractNo, ExternalNo, `Status`, CreatedBy, CreatedDate, ModifiedBy, ModifiedDate, 
    SubContractType
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.SubContractExternalRelationshipExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_subcontract_external_relationship
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from tb_subcontract_external_relationship
    where ID = #{ID,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from tb_subcontract_external_relationship
    where ID = #{ID,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.SubContractExternalRelationshipExample" >
    delete from tb_subcontract_external_relationship
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.SubContractExternalRelationshipPO" >
    insert into tb_subcontract_external_relationship (ID, SubContractNo, ExternalNo, 
      `Status`, CreatedBy, CreatedDate, 
      ModifiedBy, ModifiedDate, SubContractType
      )
    values (#{ID,jdbcType=VARCHAR}, #{subContractNo,jdbcType=VARCHAR}, #{externalNo,jdbcType=VARCHAR}, 
      #{status,jdbcType=BIT}, #{createdBy,jdbcType=VARCHAR}, #{createdDate,jdbcType=TIMESTAMP}, 
      #{modifiedBy,jdbcType=VARCHAR}, #{modifiedDate,jdbcType=TIMESTAMP}, #{subContractType,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.SubContractExternalRelationshipPO" >
    insert into tb_subcontract_external_relationship
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="ID != null" >
        ID,
      </if>
      <if test="subContractNo != null" >
        SubContractNo,
      </if>
      <if test="externalNo != null" >
        ExternalNo,
      </if>
      <if test="status != null" >
        `Status`,
      </if>
      <if test="createdBy != null" >
        CreatedBy,
      </if>
      <if test="createdDate != null" >
        CreatedDate,
      </if>
      <if test="modifiedBy != null" >
        ModifiedBy,
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate,
      </if>
      <if test="subContractType != null" >
        SubContractType,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="ID != null" >
        #{ID,jdbcType=VARCHAR},
      </if>
      <if test="subContractNo != null" >
        #{subContractNo,jdbcType=VARCHAR},
      </if>
      <if test="externalNo != null" >
        #{externalNo,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=BIT},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="subContractType != null" >
        #{subContractType,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.SubContractExternalRelationshipExample" resultType="java.lang.Integer" >
    select count(*) from tb_subcontract_external_relationship
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_subcontract_external_relationship
    <set >
      <if test="record.ID != null" >
        ID = #{record.ID,jdbcType=VARCHAR},
      </if>
      <if test="record.subContractNo != null" >
        SubContractNo = #{record.subContractNo,jdbcType=VARCHAR},
      </if>
      <if test="record.externalNo != null" >
        ExternalNo = #{record.externalNo,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null" >
        `Status` = #{record.status,jdbcType=BIT},
      </if>
      <if test="record.createdBy != null" >
        CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdDate != null" >
        CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedBy != null" >
        ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.modifiedDate != null" >
        ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.subContractType != null" >
        SubContractType = #{record.subContractType,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_subcontract_external_relationship
    set ID = #{record.ID,jdbcType=VARCHAR},
      SubContractNo = #{record.subContractNo,jdbcType=VARCHAR},
      ExternalNo = #{record.externalNo,jdbcType=VARCHAR},
      `Status` = #{record.status,jdbcType=BIT},
      CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      SubContractType = #{record.subContractType,jdbcType=INTEGER}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.SubContractExternalRelationshipPO" >
    update tb_subcontract_external_relationship
    <set >
      <if test="subContractNo != null" >
        SubContractNo = #{subContractNo,jdbcType=VARCHAR},
      </if>
      <if test="externalNo != null" >
        ExternalNo = #{externalNo,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        `Status` = #{status,jdbcType=BIT},
      </if>
      <if test="createdBy != null" >
        CreatedBy = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        ModifiedBy = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="subContractType != null" >
        SubContractType = #{subContractType,jdbcType=INTEGER},
      </if>
    </set>
    where ID = #{ID,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.SubContractExternalRelationshipPO" >
    update tb_subcontract_external_relationship
    set SubContractNo = #{subContractNo,jdbcType=VARCHAR},
      ExternalNo = #{externalNo,jdbcType=VARCHAR},
      `Status` = #{status,jdbcType=BIT},
      CreatedBy = #{createdBy,jdbcType=VARCHAR},
      CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{modifiedBy,jdbcType=VARCHAR},
      ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      SubContractType = #{subContractType,jdbcType=INTEGER}
    where ID = #{ID,jdbcType=VARCHAR}
  </update>
</mapper>