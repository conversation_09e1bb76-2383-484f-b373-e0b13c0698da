# TestLine对象处理方法详细文档

## 文档概述

本文档基于实际代码分析，详细梳理了TestLine对象的所有处理方法，包括每个方法的对象、状态、触发动作、触发规则，严格基于代码事实。

**【事实】** 基于以下核心文件的实际代码分析：
- `TestLineService.java` - TestLine核心业务服务
- `TestLineStatusService.java` - TestLine状态管理服务
- `TestLineQueryService.java` - TestLine查询服务
- `TestLineValidateService.java` - TestLine验证服务
- `TestLineRemarkService.java` - TestLine备注服务

## TestLine处理方法分类总览

### 方法统计表

| 分类 | 方法数量 | 主要功能 | 权限要求 |
|------|----------|----------|----------|
| 查询相关 | 5 | 获取测试线、PP列表等查询操作 | 基础查询权限 |
| 创建保存 | 1 | 创建和保存测试线 | 报告状态控制 |
| 状态管理 | 3 | 测试线状态转换和更新 | 状态转换权限 |
| 测试线操作 | 4 | 删除、取消、NC、重测等操作 | 高级操作权限 |
| 标准条件管理 | 2 | 测试标准和条件的管理 | 标准更新权限 |
| 备注管理 | 2 | 测试线备注的增删改查 | 基础编辑权限 |
| 分包相关 | 2 | 分包测试线的管理和查询 | 分包操作权限 |
| 验证相关 | 2 | 测试线数据验证和状态检查 | 验证权限 |
| 辅助工具 | 3 | 退回、修改标记、引用信息等 | 对应功能权限 |
| 数据导出 | 3 | 模板数据、Gemo数据导出 | 导出权限 |
| 业务统计 | 3 | 统计查询、业务单元管理 | 统计查询权限 |
| 实用工具 | 4 | 样品检查、排序、实验室验证等 | 基础工具权限 |

### 1. 查询相关方法 (5个)

| 方法名 | 对象 | 触发动作 | 主要触发规则 | 关联对象影响 | 代码位置 |
|--------|------|----------|-------------|-------------|----------|
| **getPPList** | TestLine(PP) | 从TRIMS获取可用PP列表 | 订单存在、查询参数有效、有查询权限 | **PPClient**: TRIMS PP数据查询<br/>**Order**: 订单关联验证<br/>**PPTestLineRel**: PP关系过滤 | TestLineService.java:480 |
| **getPPTestLineList** | TestLine(PP) | 根据PP ID获取测试线 | ppId/ppNo>0、订单存在 | **PPTestLineRel**: PP测试线关系查询<br/>**TestLine**: 测试线详细信息<br/>**TestMatrix**: Matrix关联查询<br/>**PPSampleRel**: PP样品关系 | TestLineService.java:569 |
| **getTestLineList** | TestLine | 获取订单下测试线列表 | 订单ID非空、有查询权限 | **TestLine**: 测试线基础信息<br/>**TestMatrix**: Matrix关联信息<br/>**Order**: 订单关联验证<br/>**TestSample**: 样品分配状态 | TestLineService.java:695 |
| **queryTestLineList** | TestLine | 查询测试线(带过滤) | 订单存在、查询条件有效 | **TestLine**: 测试线过滤查询<br/>**TestMatrix**: Matrix状态过滤<br/>**Order**: 订单范围限制<br/>**TestCondition**: 条件过滤 | TestLineService.java:981 |
| **getTestLineBreakDownInfoList** | TestLine | 获取测试线分解信息 | 订单号非空、报告信息存在 | **TestLine**: 测试线分解信息<br/>**Report**: 报告关联信息<br/>**TestMatrix**: Matrix分解详情<br/>**TestSample**: 样品分解关系 | TestLineService.java:4575 |

### 2. 创建保存相关方法 (1个)

| 方法名 | 对象 | 权限控制 | 触发动作 | 主要触发规则 | 关联对象影响 | 代码位置 |
|--------|------|----------|----------|-------------|-------------|----------|
| **saveTestLine** | TestLine | @AccessRule(reportStatus={Approved,Cancelled}) | 创建TestLine记录、设置基础信息、建立PP关联 | 报告状态为Approved/Cancelled、数据完整有效、有保存权限 | **TestLine**: 新增测试线记录<br/>**PPTestLineRel**: PP关系建立<br/>**TestMatrix**: 初始Matrix创建<br/>**Order**: 订单测试线统计更新<br/>**Report**: 报告关联建立 | TestLineService.java:2854 |

### 3. 状态管理相关方法 (3个)

| 方法名 | 对象 | 触发动作 | 主要触发规则 | 关联对象影响 | 代码位置 |
|--------|------|----------|-------------|-------------|----------|
| **updateTestLineStatus** | TestLine | 更新单个测试线状态 | moduleType非空、testLine非空、状态转换规则有效 | **TestLine**: 状态更新<br/>**TestMatrix**: Matrix状态同步<br/>**Order**: 订单状态可能更新<br/>**SubContract**: 分包状态同步<br/>**Job**: Job状态可能更新 | TestLineStatusService.java:66 |
| **batchUpdateTestLineStatus** | TestLine | 批量更新测试线状态 | moduleType非空、testLines列表非空、所有状态转换规则有效 | **TestLine**: 批量状态更新<br/>**TestMatrix**: 批量Matrix状态同步<br/>**Order**: 订单完成度重新计算<br/>**SubContract**: 批量分包状态同步 | TestLineStatusService.java:95 |
| **updateMatrixStatus** | TestLine Matrix | 更新测试线矩阵状态 | testLineInstanceId非空、testMatrixIds非空、状态转换规则有效 | **TestMatrix**: Matrix状态更新<br/>**TestLine**: 测试线状态可能同步<br/>**TestData**: 数据状态联动<br/>**ReportMatrix**: 报告关联状态更新 | TestLineStatusService.java:135 |

### 4. 测试线操作相关方法 (4个)

| 方法名 | 对象 | 权限控制 | 触发动作 | 主要触发规则 | 关联对象影响 | 代码位置 |
|--------|------|----------|----------|-------------|-------------|----------|
| **delTestLine** | TestLine | @AccessRule(reportStatus={Approved,Cancelled,Replaced}, subContractType=DelTestLine) | 删除TestLine记录、清理Matrix数据、更新关联关系 | 报告状态允许删除、测试线未分包或分包状态允许、有删除权限 | **TestLine**: 记录物理删除<br/>**TestMatrix**: Matrix数据清理<br/>**PPTestLineRel**: PP关系删除<br/>**TestSample**: 样品分配清理<br/>**Order**: 订单统计更新<br/>**SubContract**: 分包关系清理 | TestLineService.java:1134 |
| **cancelTestLine** | TestLine | @AccessRule(reportStatus={Approved,Cancelled,Replaced}, subContractType=CancelTestLine, testLinePendingType=TestLineInstanceId) | 设置状态为Cancelled、处理分包关联、更新Job状态 | 非NC/非Cancelled状态、报告状态允许取消、有取消权限 | **TestLine**: 状态设为Cancelled<br/>**TestMatrix**: Matrix状态同步<br/>**Job**: Job状态更新<br/>**Order**: 订单完成度重新计算<br/>**SubContract**: 分包状态更新<br/>**Report**: 报告内容调整 | TestLineService.java:1533 |
| **ncTestLine** | TestLine | @AccessRule(reportStatus={Approved,Cancelled,Replaced}, subContractType=NCTestLine, testLinePendingType=TestLineInstanceId) | 设置状态为NC、记录NC原因、检查样品分配 | 测试线未分配样品、必须提供NC备注、有NC权限 | **TestLine**: 状态设为NC<br/>**TestMatrix**: Matrix状态同步<br/>**TestRemark**: NC原因记录<br/>**TestSample**: 样品分配检查<br/>**Order**: 订单完成度重新计算<br/>**Report**: 报告内容调整 | TestLineService.java:2673 |
| **reTest** | TestLine | @AccessRule(reportStatus={Approved,Cancelled}) | 删除测试数据、重置状态为Entered、清空Matrix数据 | 报告状态允许重测、测试线存在有效、有重测权限 | **TestLine**: 状态重置为Entered<br/>**TestMatrix**: Matrix状态重置<br/>**TestData**: 测试数据清空<br/>**Conclusion**: 结论重置<br/>**TestRemark**: 重测原因记录<br/>**Report**: 报告状态可能回退 | TestLineService.java:4681 |

### 5. 标准条件管理方法 (2个)

| 方法名 | 对象 | 权限控制 | 触发动作 | 主要触发规则 | 代码位置 |
|--------|------|----------|----------|-------------|----------|
| **updateStandard** | TestLine Standard | @AccessRule(reportStatus={Approved,Cancelled}, subContractType=UpdateStandard, testLinePendingType=SaveStandTestLineId) | 更新测试标准、触发状态变更为Entered、可能触发订单状态同步 | 标准数据非空、报告状态允许更新、有标准更新权限 | TestLineService.java:1935 |
| **getTestStandard** | TestLine Standard | 无 | 获取测试线的测试标准信息 | testLineInstanceId非空、测试线存在 | TestLineService.java:1785 |

### 6. 备注管理方法 (2个)

| 方法名 | 对象 | 权限控制 | 触发动作 | 主要触发规则 | 代码位置 |
|--------|------|----------|----------|-------------|----------|
| **updateRemark** | TestLine | @AccessRule(testLinePendingType=TestLineInstanceId) | 更新测试线备注、NC状态时检查样品分配 | 测试线存在、NC状态需检查样品分配、有更新权限 | TestLineService.java:1759 |
| **getTestLineRemark** | TestLine | 无 | 获取测试线备注信息 | testLineInstanceId非空 | TestLineService.java:1745 |

### 7. 分包相关方法 (2个)

| 方法名 | 对象 | 权限控制 | 触发动作 | 主要触发规则 | 代码位置 |
|--------|------|----------|----------|-------------|----------|
| **getSubContractTestLine** | TestLine(分包) | 无 | 获取分包测试线信息 | 订单号非空、分包ID有效(可选) | TestLineService.java:4788 |
| **checkTestLineSubContractStatus** | TestLine(分包) | @AccessRule(testLinePendingType=TestLineInstanceId) | 检查测试线分包状态 | testLineInstanceId非空、有查询权限 | TestLineService.java:5202 |

### 8. 验证相关方法 (2个)

| 方法名 | 对象 | 服务类 | 触发动作 | 主要触发规则 | 代码位置 |
|--------|------|---------|----------|-------------|----------|
| **testLineValidate** | TestLine | TestLineValidateService | 验证测试线数据完整性和准确性 | 测试线状态为Submit、验证数据完整、有验证权限 | TestLineValidateService |
| **checkAllTestLineStatusUpdateOrderStatus** | TestLine | TestLineValidateService | 检查所有测试线状态并更新订单状态 | 订单ID非空、测试线ID列表非空 | TestLineValidateService |

### 9. 辅助工具方法 (3个)

| 方法名 | 对象 | 触发动作 | 主要触发规则 | 代码位置 |
|--------|------|----------|-------------|----------|
| **returnTestLine** | TestLine | 退回测试线到上一个状态 | 测试线存在且状态允许退回、有退回权限 | TestLineService.java:4629 |
| **updateModified** | TestLine | 更新测试线修改标记 | testLineInstanceId非空、modified标记非空 | TestLineService.java:1723 |
| **getCitationInfoList** | TestLine Citation | 获取引用信息列表 | citationName非空 | TestLineService.java:1082 |

## 状态转换操作汇总

**【事实】** 基于TestLineStatusService.java中的实际状态转换逻辑：

| 操作类型 | 目标状态 | 代码位置 | 备注 |
|----------|----------|----------|------|
| **DataEntrySave** | Entered(705) | 第158行 | 数据录入保存 |
| **DataEntrySumbit** | Submit(702) | 第163行 | 数据录入提交 |
| **Validate** | Completed(703) | 第166行 | 验证完成 |
| **Change** | Entered(705) | 第154行 | 一般变更 |
| **ChangeUpdateCondition** | Entered(705) | 第154行 | 更新测试条件 |
| **ChangeUpdateStandard** | Entered(705) | 第154行 | 更新测试标准 |
| **ChangeAddMatrix** | Entered(705) | 第154行 | 添加测试矩阵 |
| **ReTest** | Entered(705) | 第160行 | 重新测试 |
| **PPSummaryDataEntry** | Entered(705) | 第160行 | PP汇总数据录入 |
| **ChangeCancelMatrix** | 动态决定 | 第147行 | 取消/恢复矩阵 |

## 权限控制注解说明

**【事实】** 基于代码中的@AccessRule注解：

### 报告状态控制
- **ReportStatus.Approved**: 报告已批准
- **ReportStatus.Cancelled**: 报告已取消
- **ReportStatus.Replaced**: 报告已替换
- **ReportStatus.New**: 新报告
- **ReportStatus.Draft**: 草稿报告
- **ReportStatus.Combined**: 合并报告

### 分包操作类型控制
- **SubContractOperationTypeEnums.DelTestLine**: 删除测试线
- **SubContractOperationTypeEnums.CancelTestLine**: 取消测试线
- **SubContractOperationTypeEnums.NCTestLine**: NC测试线
- **SubContractOperationTypeEnums.UpdateStandard**: 更新标准
- **SubContractOperationTypeEnums.updateTLStatus**: 更新测试线状态

### 测试线挂起类型控制
- **TestLinePendingTypeEnums.TestLineInstanceId**: 基于测试线实例ID
- **TestLinePendingTypeEnums.SaveStandTestLineId**: 基于保存标准测试线ID

### 10. 数据导出相关方法 (3个)

| 方法名 | 对象 | 权限控制 | 触发动作 | 主要触发规则 | 代码位置 |
|--------|------|----------|----------|-------------|----------|
| **getTlTemplateData** | TestLine | @AccessRule(reportStatus={Approved,Cancelled}) | 获取测试线模板数据用于导出 | generalOrderInstanceID非空、报告状态允许导出、有导出权限 | TestLineService.java:5015 |
| **getConfirmMatrixGemoData** | TestLine(Gemo) | 无 | 获取确认矩阵的Gemo数据 | GemoReq参数有效、测试线存在Gemo相关数据 | TestLineService.java:5072 |
| **getSoftcopyGemoData** | TestLine(Gemo) | 无 | 获取软拷贝Gemo数据 | 报告号列表非空、报告状态有效 | TestLineService.java:5110 |

### 11. 业务统计方法 (3个)

| 方法名 | 对象 | 触发动作 | 主要触发规则 | 代码位置 |
|--------|------|----------|-------------|----------|
| **queryTestLineByOrderNo** | TestLine | 根据订单号查询测试线列表 | 订单号非空、订单存在 | TestLineService.java:5382 |
| **getTestLineCount** | TestLine | 获取指定时间范围内的测试线数量统计 | 开始/结束日期非空、SGS Token有效、有统计查询权限 | TestLineService.java:5624 |
| **getBuCodeList** | ProductLine | 获取业务单元代码列表 | 无特殊限制 | TestLineService.java:5307 |

### 12. 实用工具方法 (4个)

| 方法名 | 对象 | 触发动作 | 主要触发规则 | 代码位置 |
|--------|------|----------|-------------|----------|
| **checkAssignedSampleByTestLine** | TestLine | 检查测试线是否已分配样品 | testLineID非空、测试线存在 | TestLineService.java:2609 |
| **checkNCTestline** | TestLine | 检查测试线是否可以执行NC操作 | 测试线ID列表非空、测试线未分配样品、状态为Typing | TestLineService.java:2623 |
| **sortTestLine** | TestLine | 对测试线进行排序处理 | 订单号非空、报告语言ID有效 | TestLineService.java:5393 |
| **checkAndGetLabId** | TestLine | 检查并获取实验室ID信息 | 订单号和实验室代码非空、有相应实验室权限 | TestLineService.java:5600 |

## 状态检查和权限控制机制

**【事实】** 基于ActionTestLineStatusMatrixEnum和TestLineStatusManager的实际实现：

### ActionTestLineStatusMatrixEnum权限矩阵

| 操作类型 | 操作代码 | 允许的源状态 | 代码定义 |
|----------|----------|-------------|----------|
| **ASSIGN_SAMPLE** | 1 | Typing, DR | 分配样品 |
| **UPDATE_STANDARD** | 2 | Typing, DR | 更新标准 |
| **UPDATE_CONDITION** | 3 | Typing, DR | 更新条件 |
| **CONFIRM_CONDITION** | 4 | Typing, DR | 确认条件 |
| **DELETE_TL** | 5 | Typing, NC, DR | 删除测试线 |
| **CANCEL_TL** | 6 | Typing, NC, DR | 取消测试线 |
| **NC_TL** | 7 | Typing | NC测试线 |
| **COPY_TEST** | 8 | Typing | 复制测试 |
| **DEL_TEST** | 9 | Typing | 删除测试 |
| **CHANGE** | 10 | Entered, Submit, Completed | 变更操作 |

### 状态检查实现机制

**【事实】** TestLineStatusManager.checkTestLineStatus方法的实际逻辑：

```java
public static CustomResult checkTestLineStatus(ActionTestLineStatusMatrixEnum actionTestLineStatusMatrixEnum, List<TestLineInstancePO> testLineInstancePOS) {
    // 检查测试线当前状态是否在允许的状态列表中
    // 如果不在允许范围内，返回失败结果和错误信息
}
```

### 业务层权限控制

**【事实】** 业务服务层的额外权限检查：

1. **报告状态检查**: 通过@AccessRule注解控制
2. **分包操作检查**: 通过subContractType参数控制
3. **测试线挂起检查**: 通过testLinePendingType参数控制
4. **业务逻辑检查**: 在具体方法中实现特殊业务规则

## 事务处理和数据一致性

**【事实】** 基于代码中的事务处理机制：

### 事务模板使用
- **TransactionTemplate**: 用于确保数据库操作的原子性
- **事务回滚**: 任何步骤失败时整个事务回滚
- **外部系统调用**: 在事务中调用外部系统接口

### 典型事务流程
1. **状态更新**: 更新TestLine和Matrix状态
2. **外部同步**: 调用订单状态、分包状态同步接口
3. **关联数据**: 更新相关的Job、Sample等数据
4. **日志记录**: 记录操作历史和审计信息

## 错误处理和异常机制

**【事实】** 基于代码中的错误处理模式：

### 常见错误类型
1. **参数验证失败**: "参数不能为空"、"参数格式错误"
2. **状态检查失败**: "TestLine状态为XX才能执行XX操作"
3. **权限验证失败**: "用户无XX权限"
4. **业务规则违反**: "测试线已分配样品，不能NC"
5. **外部系统调用失败**: "订单状态同步失败"

### 错误处理策略
- **立即返回**: 参数验证失败时立即返回错误
- **事务回滚**: 业务操作失败时回滚所有变更
- **错误传播**: 将底层错误信息传播到上层调用者
- **日志记录**: 记录错误详情用于问题排查

---

**文档生成时间**: 2025-09-28
**基于代码版本**: 当前项目代码
**维护责任**: TestLine管理模块开发团队
