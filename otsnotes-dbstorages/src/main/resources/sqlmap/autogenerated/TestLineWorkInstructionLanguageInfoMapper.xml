<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.otsnotes.dbstorages.mybatis.mapper.TestLineWorkInstructionLanguageInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineWorkInstructionLanguageInfoPO" >
    <id column="LangId" property="langId" jdbcType="BIGINT" />
    <result column="WorkInstructionBaseId" property="workInstructionBaseId" jdbcType="BIGINT" />
    <result column="TestLineVersionId" property="testLineVersionId" jdbcType="INTEGER" />
    <result column="CategoryName" property="categoryName" jdbcType="VARCHAR" />
    <result column="WorkInstructionName" property="workInstructionName" jdbcType="VARCHAR" />
    <result column="WorkInstructionText" property="workInstructionText" jdbcType="VARCHAR" />
    <result column="LanguageId" property="languageId" jdbcType="INTEGER" />
    <result column="BizVersionId" property="bizVersionId" jdbcType="CHAR" />
    <result column="LangStatus" property="langStatus" jdbcType="INTEGER" />
    <result column="CreatedDate" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="ModifiedDate" property="modifiedDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    LangId, WorkInstructionBaseId, TestLineVersionId, CategoryName, WorkInstructionName, 
    WorkInstructionText, LanguageId, BizVersionId, LangStatus, CreatedDate, ModifiedDate
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineWorkInstructionLanguageInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tre_trims_testline_work_instruction_language
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from tre_trims_testline_work_instruction_language
    where LangId = #{langId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from tre_trims_testline_work_instruction_language
    where LangId = #{langId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineWorkInstructionLanguageInfoExample" >
    delete from tre_trims_testline_work_instruction_language
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineWorkInstructionLanguageInfoPO" >
    insert into tre_trims_testline_work_instruction_language (LangId, WorkInstructionBaseId, TestLineVersionId,
      CategoryName, WorkInstructionName, WorkInstructionText, 
      LanguageId, BizVersionId, LangStatus, 
      CreatedDate, ModifiedDate)
    values (#{langId,jdbcType=BIGINT}, #{workInstructionBaseId,jdbcType=BIGINT}, #{testLineVersionId,jdbcType=INTEGER}, 
      #{categoryName,jdbcType=VARCHAR}, #{workInstructionName,jdbcType=VARCHAR}, #{workInstructionText,jdbcType=VARCHAR}, 
      #{languageId,jdbcType=INTEGER}, #{bizVersionId,jdbcType=CHAR}, #{langStatus,jdbcType=INTEGER}, 
      #{createdDate,jdbcType=TIMESTAMP}, #{modifiedDate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineWorkInstructionLanguageInfoPO" >
    insert into tre_trims_testline_work_instruction_language
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="langId != null" >
        LangId,
      </if>
      <if test="workInstructionBaseId != null" >
        WorkInstructionBaseId,
      </if>
      <if test="testLineVersionId != null" >
        TestLineVersionId,
      </if>
      <if test="categoryName != null" >
        CategoryName,
      </if>
      <if test="workInstructionName != null" >
        WorkInstructionName,
      </if>
      <if test="workInstructionText != null" >
        WorkInstructionText,
      </if>
      <if test="languageId != null" >
        LanguageId,
      </if>
      <if test="bizVersionId != null" >
        BizVersionId,
      </if>
      <if test="langStatus != null" >
        LangStatus,
      </if>
      <if test="createdDate != null" >
        CreatedDate,
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="langId != null" >
        #{langId,jdbcType=BIGINT},
      </if>
      <if test="workInstructionBaseId != null" >
        #{workInstructionBaseId,jdbcType=BIGINT},
      </if>
      <if test="testLineVersionId != null" >
        #{testLineVersionId,jdbcType=INTEGER},
      </if>
      <if test="categoryName != null" >
        #{categoryName,jdbcType=VARCHAR},
      </if>
      <if test="workInstructionName != null" >
        #{workInstructionName,jdbcType=VARCHAR},
      </if>
      <if test="workInstructionText != null" >
        #{workInstructionText,jdbcType=VARCHAR},
      </if>
      <if test="languageId != null" >
        #{languageId,jdbcType=INTEGER},
      </if>
      <if test="bizVersionId != null" >
        #{bizVersionId,jdbcType=CHAR},
      </if>
      <if test="langStatus != null" >
        #{langStatus,jdbcType=INTEGER},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineWorkInstructionLanguageInfoExample" resultType="java.lang.Integer" >
    select count(*) from tre_trims_testline_work_instruction_language
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tre_trims_testline_work_instruction_language
    <set >
      <if test="record.langId != null" >
        LangId = #{record.langId,jdbcType=BIGINT},
      </if>
      <if test="record.workInstructionBaseId != null" >
        WorkInstructionBaseId = #{record.workInstructionBaseId,jdbcType=BIGINT},
      </if>
      <if test="record.testLineVersionId != null" >
        TestLineVersionId = #{record.testLineVersionId,jdbcType=INTEGER},
      </if>
      <if test="record.categoryName != null" >
        CategoryName = #{record.categoryName,jdbcType=VARCHAR},
      </if>
      <if test="record.workInstructionName != null" >
        WorkInstructionName = #{record.workInstructionName,jdbcType=VARCHAR},
      </if>
      <if test="record.workInstructionText != null" >
        WorkInstructionText = #{record.workInstructionText,jdbcType=VARCHAR},
      </if>
      <if test="record.languageId != null" >
        LanguageId = #{record.languageId,jdbcType=INTEGER},
      </if>
      <if test="record.bizVersionId != null" >
        BizVersionId = #{record.bizVersionId,jdbcType=CHAR},
      </if>
      <if test="record.langStatus != null" >
        LangStatus = #{record.langStatus,jdbcType=INTEGER},
      </if>
      <if test="record.createdDate != null" >
        CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedDate != null" >
        ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tre_trims_testline_work_instruction_language
    set LangId = #{record.langId,jdbcType=BIGINT},
      WorkInstructionBaseId = #{record.workInstructionBaseId,jdbcType=BIGINT},
      TestLineVersionId = #{record.testLineVersionId,jdbcType=INTEGER},
      CategoryName = #{record.categoryName,jdbcType=VARCHAR},
      WorkInstructionName = #{record.workInstructionName,jdbcType=VARCHAR},
      WorkInstructionText = #{record.workInstructionText,jdbcType=VARCHAR},
      LanguageId = #{record.languageId,jdbcType=INTEGER},
      BizVersionId = #{record.bizVersionId,jdbcType=CHAR},
      LangStatus = #{record.langStatus,jdbcType=INTEGER},
      CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineWorkInstructionLanguageInfoPO" >
    update tre_trims_testline_work_instruction_language
    <set >
      <if test="workInstructionBaseId != null" >
        WorkInstructionBaseId = #{workInstructionBaseId,jdbcType=BIGINT},
      </if>
      <if test="testLineVersionId != null" >
        TestLineVersionId = #{testLineVersionId,jdbcType=INTEGER},
      </if>
      <if test="categoryName != null" >
        CategoryName = #{categoryName,jdbcType=VARCHAR},
      </if>
      <if test="workInstructionName != null" >
        WorkInstructionName = #{workInstructionName,jdbcType=VARCHAR},
      </if>
      <if test="workInstructionText != null" >
        WorkInstructionText = #{workInstructionText,jdbcType=VARCHAR},
      </if>
      <if test="languageId != null" >
        LanguageId = #{languageId,jdbcType=INTEGER},
      </if>
      <if test="bizVersionId != null" >
        BizVersionId = #{bizVersionId,jdbcType=CHAR},
      </if>
      <if test="langStatus != null" >
        LangStatus = #{langStatus,jdbcType=INTEGER},
      </if>
      <if test="createdDate != null" >
        CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where LangId = #{langId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineWorkInstructionLanguageInfoPO" >
    update tre_trims_testline_work_instruction_language
    set WorkInstructionBaseId = #{workInstructionBaseId,jdbcType=BIGINT},
      TestLineVersionId = #{testLineVersionId,jdbcType=INTEGER},
      CategoryName = #{categoryName,jdbcType=VARCHAR},
      WorkInstructionName = #{workInstructionName,jdbcType=VARCHAR},
      WorkInstructionText = #{workInstructionText,jdbcType=VARCHAR},
      LanguageId = #{languageId,jdbcType=INTEGER},
      BizVersionId = #{bizVersionId,jdbcType=CHAR},
      LangStatus = #{langStatus,jdbcType=INTEGER},
      CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP}
    where LangId = #{langId,jdbcType=BIGINT}
  </update>
</mapper>