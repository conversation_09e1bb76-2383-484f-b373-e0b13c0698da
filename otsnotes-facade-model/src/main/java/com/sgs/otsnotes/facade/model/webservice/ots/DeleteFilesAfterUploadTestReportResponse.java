/**
 * DeleteFilesAfterUploadTestReportResponse.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class DeleteFilesAfterUploadTestReportResponse  implements java.io.Serializable {
    private boolean deleteFilesAfterUploadTestReportResult;

    private String errMessage;

    public DeleteFilesAfterUploadTestReportResponse() {
    }

    public DeleteFilesAfterUploadTestReportResponse(
           boolean deleteFilesAfterUploadTestReportResult,
           String errMessage) {
           this.deleteFilesAfterUploadTestReportResult = deleteFilesAfterUploadTestReportResult;
           this.errMessage = errMessage;
    }


    /**
     * Gets the deleteFilesAfterUploadTestReportResult value for this DeleteFilesAfterUploadTestReportResponse.
     * 
     * @return deleteFilesAfterUploadTestReportResult
     */
    public boolean isDeleteFilesAfterUploadTestReportResult() {
        return deleteFilesAfterUploadTestReportResult;
    }


    /**
     * Sets the deleteFilesAfterUploadTestReportResult value for this DeleteFilesAfterUploadTestReportResponse.
     * 
     * @param deleteFilesAfterUploadTestReportResult
     */
    public void setDeleteFilesAfterUploadTestReportResult(boolean deleteFilesAfterUploadTestReportResult) {
        this.deleteFilesAfterUploadTestReportResult = deleteFilesAfterUploadTestReportResult;
    }


    /**
     * Gets the errMessage value for this DeleteFilesAfterUploadTestReportResponse.
     * 
     * @return errMessage
     */
    public String getErrMessage() {
        return errMessage;
    }


    /**
     * Sets the errMessage value for this DeleteFilesAfterUploadTestReportResponse.
     * 
     * @param errMessage
     */
    public void setErrMessage(String errMessage) {
        this.errMessage = errMessage;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof DeleteFilesAfterUploadTestReportResponse)) {
            return false;
        }
        DeleteFilesAfterUploadTestReportResponse other = (DeleteFilesAfterUploadTestReportResponse) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            this.deleteFilesAfterUploadTestReportResult == other.isDeleteFilesAfterUploadTestReportResult() &&
            ((this.errMessage==null && other.getErrMessage()==null) || 
             (this.errMessage!=null &&
              this.errMessage.equals(other.getErrMessage())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        _hashCode += (isDeleteFilesAfterUploadTestReportResult() ? Boolean.TRUE : Boolean.FALSE).hashCode();
        if (getErrMessage() != null) {
            _hashCode += getErrMessage().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(DeleteFilesAfterUploadTestReportResponse.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", ">DeleteFilesAfterUploadTestReportResponse"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("deleteFilesAfterUploadTestReportResult");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "DeleteFilesAfterUploadTestReportResult"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "boolean"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("errMessage");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "errMessage"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
