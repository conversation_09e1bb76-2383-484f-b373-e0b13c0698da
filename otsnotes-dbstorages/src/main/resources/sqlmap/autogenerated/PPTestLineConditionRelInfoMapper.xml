<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.otsnotes.dbstorages.mybatis.mapper.PPTestLineConditionRelInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.otsnotes.dbstorages.mybatis.model.PPTestLineConditionRelInfoPO" >
    <id column="Id" property="id" jdbcType="BIGINT" />
    <result column="PpVersionId" property="ppVersionId" jdbcType="INTEGER" />
    <result column="ArtifactId" property="artifactId" jdbcType="BIGINT" />
    <result column="TestLineVersionId" property="testLineVersionId" jdbcType="INTEGER" />
    <result column="ConditionTypeBaseId" property="conditionTypeBaseId" jdbcType="BIGINT" />
    <result column="ConditionBaseId" property="conditionBaseId" jdbcType="BIGINT" />
    <result column="BizVersionId" property="bizVersionId" jdbcType="CHAR" />
    <result column="RelStatus" property="relStatus" jdbcType="INTEGER" />
    <result column="CreatedDate" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="ModifiedDate" property="modifiedDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    Id, PpVersionId, ArtifactId, TestLineVersionId, ConditionTypeBaseId, ConditionBaseId, 
    BizVersionId, RelStatus, CreatedDate, ModifiedDate
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.PPTestLineConditionRelInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_trims_pp_testline_condition_relationship
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from tb_trims_pp_testline_condition_relationship
    where Id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from tb_trims_pp_testline_condition_relationship
    where Id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.PPTestLineConditionRelInfoExample" >
    delete from tb_trims_pp_testline_condition_relationship
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.PPTestLineConditionRelInfoPO" >
    insert into tb_trims_pp_testline_condition_relationship (Id, PpVersionId, ArtifactId,
      TestLineVersionId, ConditionTypeBaseId, 
      ConditionBaseId, BizVersionId, RelStatus, 
      CreatedDate, ModifiedDate)
    values (#{id,jdbcType=BIGINT}, #{ppVersionId,jdbcType=INTEGER}, #{artifactId,jdbcType=BIGINT}, 
      #{testLineVersionId,jdbcType=INTEGER}, #{conditionTypeBaseId,jdbcType=BIGINT}, 
      #{conditionBaseId,jdbcType=BIGINT}, #{bizVersionId,jdbcType=CHAR}, #{relStatus,jdbcType=INTEGER}, 
      #{createdDate,jdbcType=TIMESTAMP}, #{modifiedDate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.PPTestLineConditionRelInfoPO" >
    insert into tb_trims_pp_testline_condition_relationship
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        Id,
      </if>
      <if test="ppVersionId != null" >
        PpVersionId,
      </if>
      <if test="artifactId != null" >
        ArtifactId,
      </if>
      <if test="testLineVersionId != null" >
        TestLineVersionId,
      </if>
      <if test="conditionTypeBaseId != null" >
        ConditionTypeBaseId,
      </if>
      <if test="conditionBaseId != null" >
        ConditionBaseId,
      </if>
      <if test="bizVersionId != null" >
        BizVersionId,
      </if>
      <if test="relStatus != null" >
        RelStatus,
      </if>
      <if test="createdDate != null" >
        CreatedDate,
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="ppVersionId != null" >
        #{ppVersionId,jdbcType=INTEGER},
      </if>
      <if test="artifactId != null" >
        #{artifactId,jdbcType=BIGINT},
      </if>
      <if test="testLineVersionId != null" >
        #{testLineVersionId,jdbcType=INTEGER},
      </if>
      <if test="conditionTypeBaseId != null" >
        #{conditionTypeBaseId,jdbcType=BIGINT},
      </if>
      <if test="conditionBaseId != null" >
        #{conditionBaseId,jdbcType=BIGINT},
      </if>
      <if test="bizVersionId != null" >
        #{bizVersionId,jdbcType=CHAR},
      </if>
      <if test="relStatus != null" >
        #{relStatus,jdbcType=INTEGER},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.PPTestLineConditionRelInfoExample" resultType="java.lang.Integer" >
    select count(*) from tb_trims_pp_testline_condition_relationship
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_trims_pp_testline_condition_relationship
    <set >
      <if test="record.id != null" >
        Id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.ppVersionId != null" >
        PpVersionId = #{record.ppVersionId,jdbcType=INTEGER},
      </if>
      <if test="record.artifactId != null" >
        ArtifactId = #{record.artifactId,jdbcType=BIGINT},
      </if>
      <if test="record.testLineVersionId != null" >
        TestLineVersionId = #{record.testLineVersionId,jdbcType=INTEGER},
      </if>
      <if test="record.conditionTypeBaseId != null" >
        ConditionTypeBaseId = #{record.conditionTypeBaseId,jdbcType=BIGINT},
      </if>
      <if test="record.conditionBaseId != null" >
        ConditionBaseId = #{record.conditionBaseId,jdbcType=BIGINT},
      </if>
      <if test="record.bizVersionId != null" >
        BizVersionId = #{record.bizVersionId,jdbcType=CHAR},
      </if>
      <if test="record.relStatus != null" >
        RelStatus = #{record.relStatus,jdbcType=INTEGER},
      </if>
      <if test="record.createdDate != null" >
        CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedDate != null" >
        ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_trims_pp_testline_condition_relationship
    set Id = #{record.id,jdbcType=BIGINT},
      PpVersionId = #{record.ppVersionId,jdbcType=INTEGER},
      ArtifactId = #{record.artifactId,jdbcType=BIGINT},
      TestLineVersionId = #{record.testLineVersionId,jdbcType=INTEGER},
      ConditionTypeBaseId = #{record.conditionTypeBaseId,jdbcType=BIGINT},
      ConditionBaseId = #{record.conditionBaseId,jdbcType=BIGINT},
      BizVersionId = #{record.bizVersionId,jdbcType=CHAR},
      RelStatus = #{record.relStatus,jdbcType=INTEGER},
      CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.PPTestLineConditionRelInfoPO" >
    update tb_trims_pp_testline_condition_relationship
    <set >
      <if test="ppVersionId != null" >
        PpVersionId = #{ppVersionId,jdbcType=INTEGER},
      </if>
      <if test="artifactId != null" >
        ArtifactId = #{artifactId,jdbcType=BIGINT},
      </if>
      <if test="testLineVersionId != null" >
        TestLineVersionId = #{testLineVersionId,jdbcType=INTEGER},
      </if>
      <if test="conditionTypeBaseId != null" >
        ConditionTypeBaseId = #{conditionTypeBaseId,jdbcType=BIGINT},
      </if>
      <if test="conditionBaseId != null" >
        ConditionBaseId = #{conditionBaseId,jdbcType=BIGINT},
      </if>
      <if test="bizVersionId != null" >
        BizVersionId = #{bizVersionId,jdbcType=CHAR},
      </if>
      <if test="relStatus != null" >
        RelStatus = #{relStatus,jdbcType=INTEGER},
      </if>
      <if test="createdDate != null" >
        CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where Id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.PPTestLineConditionRelInfoPO" >
    update tb_trims_pp_testline_condition_relationship
    set PpVersionId = #{ppVersionId,jdbcType=INTEGER},
      ArtifactId = #{artifactId,jdbcType=BIGINT},
      TestLineVersionId = #{testLineVersionId,jdbcType=INTEGER},
      ConditionTypeBaseId = #{conditionTypeBaseId,jdbcType=BIGINT},
      ConditionBaseId = #{conditionBaseId,jdbcType=BIGINT},
      BizVersionId = #{bizVersionId,jdbcType=CHAR},
      RelStatus = #{relStatus,jdbcType=INTEGER},
      CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP}
    where Id = #{id,jdbcType=BIGINT}
  </update>
</mapper>