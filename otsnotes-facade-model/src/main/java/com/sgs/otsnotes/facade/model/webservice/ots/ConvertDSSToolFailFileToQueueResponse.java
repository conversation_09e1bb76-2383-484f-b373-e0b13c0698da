/**
 * ConvertDSSToolFailFileToQueueResponse.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class ConvertDSSToolFailFileToQueueResponse  implements java.io.Serializable {
    private boolean convertDSSToolFailFileToQueueResult;

    public ConvertDSSToolFailFileToQueueResponse() {
    }

    public ConvertDSSToolFailFileToQueueResponse(
           boolean convertDSSToolFailFileToQueueResult) {
           this.convertDSSToolFailFileToQueueResult = convertDSSToolFailFileToQueueResult;
    }


    /**
     * Gets the convertDSSToolFailFileToQueueResult value for this ConvertDSSToolFailFileToQueueResponse.
     * 
     * @return convertDSSToolFailFileToQueueResult
     */
    public boolean isConvertDSSToolFailFileToQueueResult() {
        return convertDSSToolFailFileToQueueResult;
    }


    /**
     * Sets the convertDSSToolFailFileToQueueResult value for this ConvertDSSToolFailFileToQueueResponse.
     * 
     * @param convertDSSToolFailFileToQueueResult
     */
    public void setConvertDSSToolFailFileToQueueResult(boolean convertDSSToolFailFileToQueueResult) {
        this.convertDSSToolFailFileToQueueResult = convertDSSToolFailFileToQueueResult;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof ConvertDSSToolFailFileToQueueResponse)) {
            return false;
        }
        ConvertDSSToolFailFileToQueueResponse other = (ConvertDSSToolFailFileToQueueResponse) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            this.convertDSSToolFailFileToQueueResult == other.isConvertDSSToolFailFileToQueueResult();
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        _hashCode += (isConvertDSSToolFailFileToQueueResult() ? Boolean.TRUE : Boolean.FALSE).hashCode();
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(ConvertDSSToolFailFileToQueueResponse.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", ">ConvertDSSToolFailFileToQueueResponse"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("convertDSSToolFailFileToQueueResult");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "ConvertDSSToolFailFileToQueueResult"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "boolean"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
