/**
 * UpdateWorksheetPhysicalTestClause.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class UpdateWorksheetPhysicalTestClause  implements java.io.Serializable {
    private TemplateInstanceInfo worksheetInfo;

    private ReportDatasource rd;

    private String errMsg;

    public UpdateWorksheetPhysicalTestClause() {
    }

    public UpdateWorksheetPhysicalTestClause(
           TemplateInstanceInfo worksheetInfo,
           ReportDatasource rd,
           String errMsg) {
           this.worksheetInfo = worksheetInfo;
           this.rd = rd;
           this.errMsg = errMsg;
    }


    /**
     * Gets the worksheetInfo value for this UpdateWorksheetPhysicalTestClause.
     * 
     * @return worksheetInfo
     */
    public TemplateInstanceInfo getWorksheetInfo() {
        return worksheetInfo;
    }


    /**
     * Sets the worksheetInfo value for this UpdateWorksheetPhysicalTestClause.
     * 
     * @param worksheetInfo
     */
    public void setWorksheetInfo(TemplateInstanceInfo worksheetInfo) {
        this.worksheetInfo = worksheetInfo;
    }


    /**
     * Gets the rd value for this UpdateWorksheetPhysicalTestClause.
     * 
     * @return rd
     */
    public ReportDatasource getRd() {
        return rd;
    }


    /**
     * Sets the rd value for this UpdateWorksheetPhysicalTestClause.
     * 
     * @param rd
     */
    public void setRd(ReportDatasource rd) {
        this.rd = rd;
    }


    /**
     * Gets the errMsg value for this UpdateWorksheetPhysicalTestClause.
     * 
     * @return errMsg
     */
    public String getErrMsg() {
        return errMsg;
    }


    /**
     * Sets the errMsg value for this UpdateWorksheetPhysicalTestClause.
     * 
     * @param errMsg
     */
    public void setErrMsg(String errMsg) {
        this.errMsg = errMsg;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof UpdateWorksheetPhysicalTestClause)) {
            return false;
        }
        UpdateWorksheetPhysicalTestClause other = (UpdateWorksheetPhysicalTestClause) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.worksheetInfo==null && other.getWorksheetInfo()==null) || 
             (this.worksheetInfo!=null &&
              this.worksheetInfo.equals(other.getWorksheetInfo()))) &&
            ((this.rd==null && other.getRd()==null) || 
             (this.rd!=null &&
              this.rd.equals(other.getRd()))) &&
            ((this.errMsg==null && other.getErrMsg()==null) || 
             (this.errMsg!=null &&
              this.errMsg.equals(other.getErrMsg())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getWorksheetInfo() != null) {
            _hashCode += getWorksheetInfo().hashCode();
        }
        if (getRd() != null) {
            _hashCode += getRd().hashCode();
        }
        if (getErrMsg() != null) {
            _hashCode += getErrMsg().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(UpdateWorksheetPhysicalTestClause.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", ">UpdateWorksheetPhysicalTestClause"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("worksheetInfo");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "worksheetInfo"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "TemplateInstanceInfo"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("rd");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "rd"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "ReportDatasource"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("errMsg");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "errMsg"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
