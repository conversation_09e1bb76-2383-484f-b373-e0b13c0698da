/**
 * GetWorksheetPhysicalTestClauseHtmlResponse.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class GetWorksheetPhysicalTestClauseHtmlResponse  implements java.io.Serializable {
    private String getWorksheetPhysicalTestClauseHtmlResult;

    private String errMsg;

    public GetWorksheetPhysicalTestClauseHtmlResponse() {
    }

    public GetWorksheetPhysicalTestClauseHtmlResponse(
           String getWorksheetPhysicalTestClauseHtmlResult,
           String errMsg) {
           this.getWorksheetPhysicalTestClauseHtmlResult = getWorksheetPhysicalTestClauseHtmlResult;
           this.errMsg = errMsg;
    }


    /**
     * Gets the getWorksheetPhysicalTestClauseHtmlResult value for this GetWorksheetPhysicalTestClauseHtmlResponse.
     * 
     * @return getWorksheetPhysicalTestClauseHtmlResult
     */
    public String getGetWorksheetPhysicalTestClauseHtmlResult() {
        return getWorksheetPhysicalTestClauseHtmlResult;
    }


    /**
     * Sets the getWorksheetPhysicalTestClauseHtmlResult value for this GetWorksheetPhysicalTestClauseHtmlResponse.
     * 
     * @param getWorksheetPhysicalTestClauseHtmlResult
     */
    public void setGetWorksheetPhysicalTestClauseHtmlResult(String getWorksheetPhysicalTestClauseHtmlResult) {
        this.getWorksheetPhysicalTestClauseHtmlResult = getWorksheetPhysicalTestClauseHtmlResult;
    }


    /**
     * Gets the errMsg value for this GetWorksheetPhysicalTestClauseHtmlResponse.
     * 
     * @return errMsg
     */
    public String getErrMsg() {
        return errMsg;
    }


    /**
     * Sets the errMsg value for this GetWorksheetPhysicalTestClauseHtmlResponse.
     * 
     * @param errMsg
     */
    public void setErrMsg(String errMsg) {
        this.errMsg = errMsg;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof GetWorksheetPhysicalTestClauseHtmlResponse)) {
            return false;
        }
        GetWorksheetPhysicalTestClauseHtmlResponse other = (GetWorksheetPhysicalTestClauseHtmlResponse) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.getWorksheetPhysicalTestClauseHtmlResult==null && other.getGetWorksheetPhysicalTestClauseHtmlResult()==null) || 
             (this.getWorksheetPhysicalTestClauseHtmlResult!=null &&
              this.getWorksheetPhysicalTestClauseHtmlResult.equals(other.getGetWorksheetPhysicalTestClauseHtmlResult()))) &&
            ((this.errMsg==null && other.getErrMsg()==null) || 
             (this.errMsg!=null &&
              this.errMsg.equals(other.getErrMsg())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getGetWorksheetPhysicalTestClauseHtmlResult() != null) {
            _hashCode += getGetWorksheetPhysicalTestClauseHtmlResult().hashCode();
        }
        if (getErrMsg() != null) {
            _hashCode += getErrMsg().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(GetWorksheetPhysicalTestClauseHtmlResponse.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", ">GetWorksheetPhysicalTestClauseHtmlResponse"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("getWorksheetPhysicalTestClauseHtmlResult");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "GetWorksheetPhysicalTestClauseHtmlResult"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("errMsg");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "errMsg"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
