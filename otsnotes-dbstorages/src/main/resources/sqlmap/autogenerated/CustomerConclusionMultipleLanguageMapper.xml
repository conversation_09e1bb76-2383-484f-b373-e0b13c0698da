<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.otsnotes.dbstorages.mybatis.mapper.CustomerConclusionMultipleLanguageMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.otsnotes.dbstorages.mybatis.model.CustomerConclusionMultipleLanguagePO" >
    <result column="ID" property="ID" jdbcType="VARCHAR" />
    <result column="CustomerConclusionReferenceID" property="customerConclusionReferenceID" jdbcType="VARCHAR" />
    <result column="LanguageId" property="languageId" jdbcType="INTEGER" />
    <result column="ConclusionDescription" property="conclusionDescription" jdbcType="VARCHAR" />
    <result column="DisplayDescription" property="displayDescription" jdbcType="VARCHAR" />
    <result column="OverallDescription" property="overallDescription" jdbcType="VARCHAR" />
    <result column="ConclusionInterpretation" property="conclusionInterpretation" jdbcType="VARCHAR" />
    <result column="CreatedDate" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="CreatedBy" property="createdBy" jdbcType="VARCHAR" />
    <result column="ModifiedDate" property="modifiedDate" jdbcType="TIMESTAMP" />
    <result column="ModifiedBy" property="modifiedBy" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    ID, CustomerConclusionReferenceID, LanguageId, ConclusionDescription, DisplayDescription, 
    OverallDescription, ConclusionInterpretation, CreatedDate, CreatedBy, ModifiedDate, 
    ModifiedBy
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.CustomerConclusionMultipleLanguageExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_customer_conclusion_reference_multiplelanguage
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.CustomerConclusionMultipleLanguageExample" >
    delete from tb_customer_conclusion_reference_multiplelanguage
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.CustomerConclusionMultipleLanguagePO" >
    insert into tb_customer_conclusion_reference_multiplelanguage (ID, CustomerConclusionReferenceID, 
      LanguageId, ConclusionDescription, DisplayDescription, 
      OverallDescription, ConclusionInterpretation, 
      CreatedDate, CreatedBy, ModifiedDate, 
      ModifiedBy)
    values (#{ID,jdbcType=VARCHAR}, #{customerConclusionReferenceID,jdbcType=VARCHAR}, 
      #{languageId,jdbcType=INTEGER}, #{conclusionDescription,jdbcType=VARCHAR}, #{displayDescription,jdbcType=VARCHAR}, 
      #{overallDescription,jdbcType=VARCHAR}, #{conclusionInterpretation,jdbcType=VARCHAR}, 
      #{createdDate,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=VARCHAR}, #{modifiedDate,jdbcType=TIMESTAMP}, 
      #{modifiedBy,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.CustomerConclusionMultipleLanguagePO" >
    insert into tb_customer_conclusion_reference_multiplelanguage
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="ID != null" >
        ID,
      </if>
      <if test="customerConclusionReferenceID != null" >
        CustomerConclusionReferenceID,
      </if>
      <if test="languageId != null" >
        LanguageId,
      </if>
      <if test="conclusionDescription != null" >
        ConclusionDescription,
      </if>
      <if test="displayDescription != null" >
        DisplayDescription,
      </if>
      <if test="overallDescription != null" >
        OverallDescription,
      </if>
      <if test="conclusionInterpretation != null" >
        ConclusionInterpretation,
      </if>
      <if test="createdDate != null" >
        CreatedDate,
      </if>
      <if test="createdBy != null" >
        CreatedBy,
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate,
      </if>
      <if test="modifiedBy != null" >
        ModifiedBy,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="ID != null" >
        #{ID,jdbcType=VARCHAR},
      </if>
      <if test="customerConclusionReferenceID != null" >
        #{customerConclusionReferenceID,jdbcType=VARCHAR},
      </if>
      <if test="languageId != null" >
        #{languageId,jdbcType=INTEGER},
      </if>
      <if test="conclusionDescription != null" >
        #{conclusionDescription,jdbcType=VARCHAR},
      </if>
      <if test="displayDescription != null" >
        #{displayDescription,jdbcType=VARCHAR},
      </if>
      <if test="overallDescription != null" >
        #{overallDescription,jdbcType=VARCHAR},
      </if>
      <if test="conclusionInterpretation != null" >
        #{conclusionInterpretation,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.CustomerConclusionMultipleLanguageExample" resultType="java.lang.Integer" >
    select count(*) from tb_customer_conclusion_reference_multiplelanguage
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_customer_conclusion_reference_multiplelanguage
    <set >
      <if test="record.ID != null" >
        ID = #{record.ID,jdbcType=VARCHAR},
      </if>
      <if test="record.customerConclusionReferenceID != null" >
        CustomerConclusionReferenceID = #{record.customerConclusionReferenceID,jdbcType=VARCHAR},
      </if>
      <if test="record.languageId != null" >
        LanguageId = #{record.languageId,jdbcType=INTEGER},
      </if>
      <if test="record.conclusionDescription != null" >
        ConclusionDescription = #{record.conclusionDescription,jdbcType=VARCHAR},
      </if>
      <if test="record.displayDescription != null" >
        DisplayDescription = #{record.displayDescription,jdbcType=VARCHAR},
      </if>
      <if test="record.overallDescription != null" >
        OverallDescription = #{record.overallDescription,jdbcType=VARCHAR},
      </if>
      <if test="record.conclusionInterpretation != null" >
        ConclusionInterpretation = #{record.conclusionInterpretation,jdbcType=VARCHAR},
      </if>
      <if test="record.createdDate != null" >
        CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdBy != null" >
        CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.modifiedDate != null" >
        ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedBy != null" >
        ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_customer_conclusion_reference_multiplelanguage
    set ID = #{record.ID,jdbcType=VARCHAR},
      CustomerConclusionReferenceID = #{record.customerConclusionReferenceID,jdbcType=VARCHAR},
      LanguageId = #{record.languageId,jdbcType=INTEGER},
      ConclusionDescription = #{record.conclusionDescription,jdbcType=VARCHAR},
      DisplayDescription = #{record.displayDescription,jdbcType=VARCHAR},
      OverallDescription = #{record.overallDescription,jdbcType=VARCHAR},
      ConclusionInterpretation = #{record.conclusionInterpretation,jdbcType=VARCHAR},
      CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <insert id="batchInsert" parameterType="list" >
    insert into tb_customer_conclusion_reference_multiplelanguage
      (`ID`,`CustomerConclusionReferenceID`,`LanguageId`,
      `ConclusionDescription`,`DisplayDescription`,`OverallDescription`,
      `ConclusionInterpretation`,`CreatedDate`,`CreatedBy`,
      `ModifiedDate`,`ModifiedBy`)
    values 
    <foreach collection="list" item="item" index="index" separator="," > 
      ( #{ item.ID, jdbcType=VARCHAR},#{ item.customerConclusionReferenceID, jdbcType=VARCHAR},#{ item.languageId, jdbcType=INTEGER},
      #{ item.conclusionDescription, jdbcType=VARCHAR},#{ item.displayDescription, jdbcType=VARCHAR},#{ item.overallDescription, jdbcType=VARCHAR},
      #{ item.conclusionInterpretation, jdbcType=VARCHAR},#{ item.createdDate, jdbcType=TIMESTAMP},#{ item.createdBy, jdbcType=VARCHAR},
      #{ item.modifiedDate, jdbcType=TIMESTAMP},#{ item.modifiedBy, jdbcType=VARCHAR}) 
    </foreach>
  </insert>
  <update id="batchUpdate" parameterType="list" >
    <foreach collection="list" item="item" index="index" separator=";" > 
      update tb_customer_conclusion_reference_multiplelanguage 
      <set>
        <if test="item.ID != null"> 
          `ID` = #{item.ID, jdbcType = VARCHAR},
        </if> 
        <if test="item.customerConclusionReferenceID != null"> 
          `CustomerConclusionReferenceID` = #{item.customerConclusionReferenceID, jdbcType = VARCHAR},
        </if> 
        <if test="item.languageId != null"> 
          `LanguageId` = #{item.languageId, jdbcType = INTEGER},
        </if> 
        <if test="item.conclusionDescription != null"> 
          `ConclusionDescription` = #{item.conclusionDescription, jdbcType = VARCHAR},
        </if> 
        <if test="item.displayDescription != null"> 
          `DisplayDescription` = #{item.displayDescription, jdbcType = VARCHAR},
        </if> 
        <if test="item.overallDescription != null"> 
          `OverallDescription` = #{item.overallDescription, jdbcType = VARCHAR},
        </if> 
        <if test="item.conclusionInterpretation != null"> 
          `ConclusionInterpretation` = #{item.conclusionInterpretation, jdbcType = VARCHAR},
        </if> 
        <if test="item.createdDate != null"> 
          `CreatedDate` = #{item.createdDate, jdbcType = TIMESTAMP},
        </if> 
        <if test="item.createdBy != null"> 
          `CreatedBy` = #{item.createdBy, jdbcType = VARCHAR},
        </if> 
        <if test="item.modifiedDate != null"> 
          `ModifiedDate` = #{item.modifiedDate, jdbcType = TIMESTAMP},
        </if> 
        <if test="item.modifiedBy != null"> 
          `ModifiedBy` = #{item.modifiedBy, jdbcType = VARCHAR},
        </if> 
      </set>
      <where>
      </where>
    </foreach>
  </update>
</mapper>