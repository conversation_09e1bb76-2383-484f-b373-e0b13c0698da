# `delTestLine` 条件组删除异常分析

> 日期：2025-09-29  
> 作者：GitHub Copilot（自动生成）

---

## 1. 问题背景

- **模块**：`otsnotes-domain` → `TestLineService.delTestLine`
- **触发功能**：订单侧删除 Test Line，同时清理关联的 Matrix、语言、结论等数据。
- **错误现象**：删除操作抛出 `java.sql.SQLIntegrityConstraintViolationException`，提示 `tb_test_condition_group` 上的外键 `FK_Reference_25` 无法满足，导致事务回滚。
- **外键定义**：`tb_test_condition_group.TestLineInstanceID → tb_test_line_instance.ID`

### 1.1 日志摘要
```
DELETE FROM tb_test_line_instance WHERE ID=?;
...
Cause: java.sql.SQLIntegrityConstraintViolationException: Cannot delete or update a parent row: a foreign key constraint fails (`otsnotes`.`tb_test_condition_group`, CONSTRAINT `FK_Reference_25` FOREIGN KEY (`TestLineInstanceID`) ...)
```

### 1.2 已知前置约束
1. 只有状态为 Typing 且没有 Job/数据的 TL 才允许删除。
2. 删除 TL 前会计算需要清理的 Matrix、语言、结论等依赖。
3. 条件组（`TestConditionGroup`）依赖于 TL，但清理逻辑与 Matrix 是否可删除相关联。

---

## 2. 代码处理流程（文本说明）

1. **获取待删除 TL**：
   - 按用户勾选的 PP-TL 关系聚合得到 `tlIdsWaitForDelete`。
   - 校验 TL 是否满足“可删除”条件。
2. **分析 Matrix 引用**：
   - 通过 `ppSampleRelMapper.listPpSampleRelationWithTestLineListByOrderId` 获取订单内全部 TL-Sample-Matrix 关联。
   - 判断勾选 TL 对应的 Matrix 是否还被其他 TL 引用；未被引用的 Matrix 记录至 `matrixListWaitForDelete`。
3. **推导条件组删除列表**：
   - 仅在 `matrixListWaitForDelete` 非空时执行。
   - 找出和这些 Matrix 关联的 `TestConditionGroupId`，同时排除依然被其他 Matrix 使用的 ID，形成最终 `conditionGroupIds`。
4. **事务删除执行**：
   - 删除 Matrix 及其附属数据（Limit/Condition Instance、Test Data 等）。
   - 删除 `conditionGroupIds`；仅在步骤 3 得到非空列表时触发。
   - 删除 PP-TL 关系、TL 主表、多语言、结论等。

### 2.1 关键代码片段
```java
List<String> conditionGroupIds = Lists.newArrayList();
if (CollectionUtils.isNotEmpty(matrixListWaitForDelete)) {
    List<String> matrixIds = matrixListWaitForDelete.stream()
            .map(TestLineSampleAssociationInfo::getMatrixID)
            .collect(Collectors.toList());

    List<String> conditionGroupIdOfMatrix = ppInstanceSampleRelationshipWithTestLineInfos.stream()
            .filter(p -> matrixIds.contains(p.getMatrixID()) && StringUtils.isNotBlank(p.getTestConditionGroupId()))
            .map(PpInstanceSampleRelationshipWithTestLineInfo::getTestConditionGroupId)
            .collect(Collectors.toList());

    List<String> conditionGroupIdsOfLeft = ppInstanceSampleRelationshipWithTestLineInfos.stream()
            .filter(p -> !matrixIds.contains(p.getMatrixID()) && StringUtils.isNotBlank(p.getTestConditionGroupId()))
            .map(PpInstanceSampleRelationshipWithTestLineInfo::getTestConditionGroupId)
            .collect(Collectors.toList());

    conditionGroupIds = conditionGroupIdOfMatrix.stream()
            .filter(p -> !conditionGroupIdsOfLeft.contains(p))
            .distinct()
            .collect(Collectors.toList());
}
```

---

## 3. 处理逻辑流程图

```mermaid
flowchart TD
    A[用户勾选 PP-TL 关系] --> B{校验是否可删除}
    B -->|未通过| Z[返回错误]
    B -->|通过| C[列出订单内 TL-Sample-Matrix 关联]
    C --> D[筛选 matrixListWaitForDelete]
    D --> E{matrixListWaitForDelete 是否为空?}
    E -->|是| F[跳过 conditionGroupIds 计算]
    E -->|否| G[计算 conditionGroupIds]
    G --> H[删除 Matrix 及附属数据]
    F --> H
    H --> I[删除 conditionGroupIds]
    I --> J[删除 PP-TL 关系]
    J --> K[删除 TL 主表及其他资源]
    K --> L[提交事务]
```

> **注意**：当 `matrixListWaitForDelete` 为空时，流程直接跳过 `conditionGroupIds` 清理。

---

## 4. 根因分析

1. **删除逻辑耦合问题**：条件组清理完全依赖于 Matrix 是否被删除。一旦 Matrix 保留（如多个 TL 共用同一个 Matrix），`matrixListWaitForDelete` 为空，导致条件组不会被清理。
2. **外键约束触发**：未被清理的 `TestConditionGroup` 仍记录待删 TL 的 `TestLineInstanceID`，在删除 TL 主表时触发外键约束。
3. **数据推断依赖缓存**：条件组列表通过 `ppInstanceSampleRelationshipWithTestLineInfos` 推导；若数据缺失或不一致，同样会导致条件组漏删。

---

## 5. 修复建议

1. **解耦条件组清理判定**：
   - 直接根据 `tlIdsWaitForDelete` 查询 `testConditionGroupMapper.getTestConditionGroupListByTestLineIds`。
   - 在事务中无条件删除与这些 TL 关联的条件组及其多语言条目。
2. **保留现有 Matrix 优化逻辑**：
   - 可继续利用 Matrix 级别的判断减少无谓操作，但需在 Matrix 未删除时增加兜底逻辑。
3. **增加冗余校验与日志**：
   - 删除前记录待删条件组 ID；如果最终列表为空但存在关联记录，输出告警日志。
4. **扩充自动化用例**：
   - 覆盖“共享 Matrix” `TL` 删除、“Matrix 保留但 TL 删除”等场景。

---

## 6. 验证方案

1. 构造以下测试场景：
   - 单个 TL 对应独立 Matrix，删除后 Condition Group 一并清理。
   - 多个 TL 共用 Matrix，删除其中一条后 Condition Group 不再残留。
   - 历史数据中 Condition Group 缺失 Matrix 关联的情况。
2. 校验数据库：
   - `tb_test_condition_group` 不再保留已删除 TL 的 `TestLineInstanceID`。
   - 相关多语言、条件实例、限制值表中无孤儿数据。
3. 确认事务提交后订单状态、日志等未出现异常。

---

## 7. 后续行动建议

- **代码实现**：按上述修复方案调整 `TestLineService.delTestLine`。
- **代码评审**：重点关注事务边界、异常处理以及新 SQL 的性能影响。
- **回归测试**：覆盖删除、取消、NC 等路径，并关注多语言、报告生成等相关功能。
- **上线监控**：在上线后监控同类错误日志，确认问题彻底解决。

---

## 8. 附录

### 8.1 主要 Mapper 方法
- `TestMatrixMapper.deleteMatrixAndForeign`
- `TestConditionGroupMapper.deleteConditionGroupWithMultipleLangByIds`
- `TestConditionGroupMapper.getTestConditionGroupListByTestLineIds`

### 8.2 关联表说明
| 表名 | 作用 | 关键字段 |
| --- | --- | --- |
| `tb_test_line_instance` | Test Line 主表 | `ID` |
| `tb_test_condition_group` | 条件组主表 | `TestLineInstanceID` |
| `tb_test_matrix` | Matrix 主表 | `ID`、`TestConditionGroupID` |
| 其他 | Limit、Condition Instance、Test Data 等 | `TestMatrixID` |

---

> 本文档用于指导后续问题排查与修复实现，在提交 MR 前请再次核对数据库结构与实际数据场景。
