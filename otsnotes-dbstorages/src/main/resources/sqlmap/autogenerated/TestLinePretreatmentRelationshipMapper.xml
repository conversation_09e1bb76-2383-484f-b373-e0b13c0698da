<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.otsnotes.dbstorages.mybatis.mapper.TestLinePretreatmentRelationshipMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.otsnotes.dbstorages.mybatis.model.TestLinePretreatmentRelationshipPO" >
    <id column="ID" property="ID" jdbcType="BIGINT" />
    <result column="GeneralOrderInstanceId" property="generalOrderInstanceId" jdbcType="VARCHAR" />
    <result column="TestLineInstanceId" property="testLineInstanceId" jdbcType="VARCHAR" />
    <result column="PretreatmentTestLineInstanceId" property="pretreatmentTestLineInstanceId" jdbcType="VARCHAR" />
    <result column="CreatedDate" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="CreatedBy" property="createdBy" jdbcType="VARCHAR" />
    <result column="ModifiedDate" property="modifiedDate" jdbcType="TIMESTAMP" />
    <result column="ModifiedBy" property="modifiedBy" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    ID, GeneralOrderInstanceId, TestLineInstanceId, PretreatmentTestLineInstanceId, CreatedDate, 
    CreatedBy, ModifiedDate, ModifiedBy
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLinePretreatmentRelationshipExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_testline_pretreatment_relationship
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from tb_testline_pretreatment_relationship
    where ID = #{ID,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from tb_testline_pretreatment_relationship
    where ID = #{ID,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLinePretreatmentRelationshipExample" >
    delete from tb_testline_pretreatment_relationship
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLinePretreatmentRelationshipPO" >
    insert into tb_testline_pretreatment_relationship (ID, GeneralOrderInstanceId, TestLineInstanceId, 
      PretreatmentTestLineInstanceId, CreatedDate, 
      CreatedBy, ModifiedDate, ModifiedBy
      )
    values (#{ID,jdbcType=BIGINT}, #{generalOrderInstanceId,jdbcType=VARCHAR}, #{testLineInstanceId,jdbcType=VARCHAR}, 
      #{pretreatmentTestLineInstanceId,jdbcType=VARCHAR}, #{createdDate,jdbcType=TIMESTAMP}, 
      #{createdBy,jdbcType=VARCHAR}, #{modifiedDate,jdbcType=TIMESTAMP}, #{modifiedBy,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLinePretreatmentRelationshipPO" >
    insert into tb_testline_pretreatment_relationship
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="ID != null" >
        ID,
      </if>
      <if test="generalOrderInstanceId != null" >
        GeneralOrderInstanceId,
      </if>
      <if test="testLineInstanceId != null" >
        TestLineInstanceId,
      </if>
      <if test="pretreatmentTestLineInstanceId != null" >
        PretreatmentTestLineInstanceId,
      </if>
      <if test="createdDate != null" >
        CreatedDate,
      </if>
      <if test="createdBy != null" >
        CreatedBy,
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate,
      </if>
      <if test="modifiedBy != null" >
        ModifiedBy,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="ID != null" >
        #{ID,jdbcType=BIGINT},
      </if>
      <if test="generalOrderInstanceId != null" >
        #{generalOrderInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="testLineInstanceId != null" >
        #{testLineInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="pretreatmentTestLineInstanceId != null" >
        #{pretreatmentTestLineInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLinePretreatmentRelationshipExample" resultType="java.lang.Integer" >
    select count(*) from tb_testline_pretreatment_relationship
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_testline_pretreatment_relationship
    <set >
      <if test="record.ID != null" >
        ID = #{record.ID,jdbcType=BIGINT},
      </if>
      <if test="record.generalOrderInstanceId != null" >
        GeneralOrderInstanceId = #{record.generalOrderInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="record.testLineInstanceId != null" >
        TestLineInstanceId = #{record.testLineInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="record.pretreatmentTestLineInstanceId != null" >
        PretreatmentTestLineInstanceId = #{record.pretreatmentTestLineInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="record.createdDate != null" >
        CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdBy != null" >
        CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.modifiedDate != null" >
        ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedBy != null" >
        ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_testline_pretreatment_relationship
    set ID = #{record.ID,jdbcType=BIGINT},
      GeneralOrderInstanceId = #{record.generalOrderInstanceId,jdbcType=VARCHAR},
      TestLineInstanceId = #{record.testLineInstanceId,jdbcType=VARCHAR},
      PretreatmentTestLineInstanceId = #{record.pretreatmentTestLineInstanceId,jdbcType=VARCHAR},
      CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLinePretreatmentRelationshipPO" >
    update tb_testline_pretreatment_relationship
    <set >
      <if test="generalOrderInstanceId != null" >
        GeneralOrderInstanceId = #{generalOrderInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="testLineInstanceId != null" >
        TestLineInstanceId = #{testLineInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="pretreatmentTestLineInstanceId != null" >
        PretreatmentTestLineInstanceId = #{pretreatmentTestLineInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        CreatedBy = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        ModifiedBy = #{modifiedBy,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{ID,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLinePretreatmentRelationshipPO" >
    update tb_testline_pretreatment_relationship
    set GeneralOrderInstanceId = #{generalOrderInstanceId,jdbcType=VARCHAR},
      TestLineInstanceId = #{testLineInstanceId,jdbcType=VARCHAR},
      PretreatmentTestLineInstanceId = #{pretreatmentTestLineInstanceId,jdbcType=VARCHAR},
      CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      CreatedBy = #{createdBy,jdbcType=VARCHAR},
      ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{modifiedBy,jdbcType=VARCHAR}
    where ID = #{ID,jdbcType=BIGINT}
  </update>
</mapper>