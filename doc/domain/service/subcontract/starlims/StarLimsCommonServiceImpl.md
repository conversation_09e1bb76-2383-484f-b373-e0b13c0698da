# StarLimsCommonService接口实现说明 v1.0

## 文档说明
本文档描述了StarLimsCommonService类的主要业务能力，该服务负责处理与StarLims系统的数据交互、同步和处理。重点说明了toStarLims、receiveReportDoc、updateTimeTrack等关键业务逻辑，并整合了分散的验证规则和业务处理规则。

## 业务场景
- 分包数据同步至StarLims系统
- StarLims报告数据回传处理
- 分包状态轨迹更新
- 化学测试数据处理
- 数据清洗与同步

## 涉及业务实体
- SubContractPO（分包信息）
- GeneralOrderInstanceInfoPO（订单信息）
- ReportInfoPO（报告信息）
- SubReportPO（子报告信息）
- ConclusionListPO（结论信息）
- ReceiveFileInfo（文件信息）
- SubContractExternalRelationshipPO（外部关系信息）

## 业务实体关系图
```
erDiagram
    SubContractPO ||--o{ SubReportPO : contains
    SubContractPO ||--|| GeneralOrderInstanceInfoPO : belongs_to
    SubContractPO ||--o{ SubContractExternalRelationshipPO : has
    ReportInfoPO ||--o{ SubReportPO : generates
    SubReportPO ||--o{ ConclusionListPO : includes
    ReceiveStarLimsReportDocBodyReq ||--o{ ReceiveFileInfo : contains
```

## 核心能力接口列表

### 主要处理接口
| 接口名称 | 接口描述 | 影响业务实体 | 入参对象 | 出参对象 | 详细说明 |
|---------|----------|--------------|----------|----------|----------|
| toStarLims | 分包同步到StarLims | SubContract, Sample, ExternalRel | SubContractStarLimsReq | CustomResult<ExternalRelInfo> | [详细说明](#1-tostarlims) |
| receiveReportDoc | 处理StarLims报告回传 | Report, SubContract, Conclusion, TestLine | ReceiveStarLimsReportDocBodyReq | CustomResult<Void> | [详细说明](#2-receivereportdoc) |
| updateTimeTrack | 更新时间轨迹 | SubContract, ExternalRel | ReceiveStarLimsUpdateTimeBodyReq | CustomResult<Void> | [详细说明](#3-updatetimetrack) |
| cleanStarlimsData | 数据清洗 | SubContract, Report, Sample | SubcontractExternalRelReq | CustomResult<CleanInfo> | [详细说明](#4-cleanstarlimsdata) |
| cancelSubReport | 取消分包报告 | SubContract, Report | StarLimsSubReportCancelReq | CustomResult<Boolean> | [详细说明](#5-cancelsubreport) |

## 业务实体对象说明

### SubContractPO（分包信息）
| 字段名 | 类型 | 说明 | 关联实体 |
|--------|------|------|----------|
| ID | String | 主键ID | - |
| subContractNo | String | 分包编号 | - |
| orderNo | String | 订单编号 | GeneralOrderInstanceInfoPO.orderNo |
| status | Integer | 状态 | - |
| subContractOrder | Integer | 分包订单类型 | - |
| createdBy | String | 创建人 | - |
| createdDate | Date | 创建时间 | - |

### GeneralOrderInstanceInfoPO（订单信息）
| 字段名 | 类型 | 说明 | 关联实体 |
|--------|------|------|----------|
| ID | String | 主键ID | - |
| orderNo | String | 订单编号 | - |
| labCode | String | 实验室代码 | - |
| createdBy | String | 创建人 | - |
| createdDate | Date | 创建时间 | - |

### ReportInfoPO（报告信息）
| 字段名 | 类型 | 说明 | 关联实体 |
|--------|------|------|----------|
| ID | String | 主键ID | - |
| reportNo | String | 报告编号 | - |
| generalOrderInstanceID | String | 订单实例ID | GeneralOrderInstanceInfoPO.ID |
| createdBy | String | 创建人 | - |
| createdDate | Date | 创建时间 | - |

### SubReportPO（子报告信息）
| 字段名 | 类型 | 说明 | 关联实体 |
|--------|------|------|----------|
| ID | String | 主键ID | - |
| subReportNo | String | 子报告编号 | - |
| generalOrderInstanceID | String | 订单实例ID | GeneralOrderInstanceInfoPO.ID |
| objectNo | String | 对象编号 | SubContractPO.subContractNo |
| objectType | String | 对象类型 | - |
| filename | String | 文件名 | - |
| cloudID | String | 云存储ID | - |
| languageId | Integer | 语言ID | - |
| createdBy | String | 创建人 | - |
| createdDate | Date | 创建时间 | - |
| modifiedBy | String | 修改人 | - |
| modifiedDate | Date | 修改时间 | - |

### ConclusionListPO（结论信息）
| 字段名 | 类型 | 说明 | 关联实体 |
|--------|------|------|----------|
| ID | String | 主键ID | - |
| priorityLevel | Integer | 优先级 | - |
| createdBy | String | 创建人 | - |
| createdDate | Date | 创建时间 | - |

### ReceiveFileInfo（文件信息）
| 字段名 | 类型 | 说明 | 关联实体 |
|--------|------|------|----------|
| fileId | String | 文件ID | - |
| cloudId | String | 云存储ID | - |
| fileName | String | 文件名 | - |
| fileType | String | 文件类型 | - |

### SubContractExternalRelationshipPO（外部关系信息）
| 字段名 | 类型 | 说明 | 关联实体 |
|--------|------|------|----------|
| ID | String | 主键ID | - |
| subContractNo | String | 分包编号 | SubContractPO.subContractNo |
| externalNo | String | 外部编号 | - |
| subContractType | Integer | 分包类型 | - |
| createdBy | String | 创建人 | - |
| createdDate | Date | 创建时间 | - |

---

## 核心业务能力详细说明

### 1. toStarLims

#### 入参对象与业务实体关联关系
| 入参对象字段 | 字段类型 | 关联业务实体.字段 | 说明 |
|------------|----------|-------------------|------|
| subContractNo | String | SubContractPO.subContractNo | 分包编号，用于查询分包信息 |
| orderNo | String | GeneralOrderInstanceInfoPO.orderNo | 订单编号，用于查询订单信息 |
| productLineType | ProductLineType | - | 产品线类型，决定处理分支 |

#### 状态流转
```
stateDiagram-v2
    [*] --> Validating: 开始处理
    Validating --> Processing: 参数校验通过
    Validating --> Failed: 参数校验失败
    Processing --> Syncing: 数据处理完成
    Syncing --> Completed: 同步成功
    Syncing --> Failed: 同步失败
    Completed --> [*]
    Failed --> [*]
```

#### 处理流程详细图
```
flowchart TD
    Start([开始]) --> A[接收同步请求]
    A --> B{验证请求参数}
    B -->|验证失败| C[返回参数错误]
    C --> End([结束])
    
    B -->|验证通过| D[查询分包信息]
    D --> E{分包是否存在}
    E -->|不存在| F[返回分包不存在错误]
    F --> End
    
    E -->|存在| G[查询订单信息]
    G --> H{订单是否存在}
    H -->|不存在| I[返回订单不存在错误]
    I --> End
    
    H -->|存在| J[执行同步逻辑]
    J --> K[调用toStarlimsExecutor]
    K --> L{同步结果}
    L -->|成功| M[记录成功日志]
    L -->|失败| N[记录失败日志]
    
    M --> O[构建成功响应]
    N --> P[构建失败响应]
    O --> End
    P --> End
    
    style Start fill:#e1f5fe
    style M fill:#c8e6c9
    style N fill:#ffcdd2
    style End fill:#f3e5f5
```

### 2. receiveReportDoc

#### 入参对象与业务实体关联关系
| 入参对象字段 | 字段类型 | 关联业务实体.字段 | 说明 |
|------------|----------|-------------------|------|
| orderNo | String | GeneralOrderInstanceInfoPO.orderNo | 订单编号，用于查询订单信息 |
| subContractNo | String | SubContractPO.subContractNo | 分包编号，主要业务标识 |
| objectNo | String | SubContractExternalRelationshipPO.externalNo | StarLims系统编号 |
| productLineCode | String | - | 产品线代码，决定处理器链 |
| reportNo | String | ReportInfoPO.reportNo | 报告编号 |
| files | List<ReceiveFileInfo> | - | 报告文件列表 |
| conclusionSummary | List<ConclusionSummaryDTO> | ConclusionListPO | 结论摘要数据 |
| testGroup | List<TestGroupDTO> | - | 测试组数据 |

#### 状态流转
```
stateDiagram-v2
    [*] --> Validating: 开始处理
    Validating --> Processing: 参数校验通过
    Validating --> Failed: 参数校验失败
    Processing --> Saving: 数据处理完成
    Saving --> Completed: 保存成功
    Saving --> Failed: 保存失败
    Completed --> [*]
    Failed --> [*]
```

#### 处理流程详细图
```
flowchart TD
    Start([开始]) --> A[接收报告请求]
    A --> B{验证请求参数}
    B -->|验证失败| C[返回参数错误]
    C --> End([结束])
    
    B -->|验证通过| D[查询订单信息]
    D --> E{订单是否存在}
    E -->|不存在| F[返回订单不存在错误]
    F --> End
    
    E -->|存在| G{产品线类型判断}
    G -->|SL| H[执行SL处理逻辑]
    G -->|其他| I[执行其他产品线处理逻辑]
    
    H --> J[执行SL报告处理]
    I --> K[执行其他报告处理]
    
    J --> L{处理结果}
    K --> L
    L -->|成功| M[记录成功日志]
    L -->|失败| N[记录失败日志]
    
    M --> O[构建成功响应]
    N --> P[构建失败响应]
    O --> End
    P --> End
    
    style Start fill:#e1f5fe
    style M fill:#c8e6c9
    style N fill:#ffcdd2
    style End fill:#f3e5f5
```

### 3. updateTimeTrack

#### 入参对象与业务实体关联关系
| 入参对象字段 | 字段类型 | 关联业务实体.字段 | 说明 |
|------------|----------|-------------------|------|
| orderNo | String | GeneralOrderInstanceInfoPO.orderNo | 订单编号 |
| subContractNo | String | SubContractPO.subContractNo | 分包编号 |
| objectNo | String | SubContractExternalRelationshipPO.externalNo | StarLims系统编号 |
| trackType | String | - | 轨迹类型 |
| operationDate | String | - | 操作日期 |

#### 状态流转
```
stateDiagram-v2
    [*] --> Validating: 开始处理
    Validating --> Processing: 参数校验通过
    Validating --> Failed: 参数校验失败
    Processing --> Updating: 数据处理完成
    Updating --> Completed: 更新成功
    Updating --> Failed: 更新失败
    Completed --> [*]
    Failed --> [*]
```

#### 处理流程详细图
```
flowchart TD
    Start([开始]) --> A[接收时间轨迹请求]
    A --> B{验证请求参数}
    B -->|验证失败| C[返回参数错误]
    C --> End([结束])
    
    B -->|验证通过| D[校验StarLims数据]
    D --> E{数据校验结果}
    E -->|失败| F[返回校验失败]
    F --> End
    
    E -->|成功| G[查询分包信息]
    G --> H{分包是否存在}
    H -->|不存在| I[返回分包不存在错误]
    I --> End
    
    H -->|存在| J{分包渠道校验}
    J -->|不是StarLims分包| K[返回渠道错误]
    K --> End
    
    J -->|是StarLims分包| L{轨迹类型判断}
    L -->|FolderCreation| M[处理文件夹创建]
    L -->|CommitFolder| N[处理文件夹提交]
    L -->|FolderReported| O[处理文件夹报告]
    L -->|其他| P[返回类型错误]
    
    M --> Q[处理文件夹创建逻辑]
    N --> R[处理文件夹提交逻辑]
    O --> S[处理文件夹报告逻辑]
    P --> End
    
    Q --> T{处理结果}
    R --> T
    S --> T
    T -->|成功| U[记录成功日志]
    T -->|失败| V[记录失败日志]
    
    U --> W[构建成功响应]
    V --> X[构建失败响应]
    W --> End
    X --> End
    
    style Start fill:#e1f5fe
    style U fill:#c8e6c9
    style V fill:#ffcdd2
    style End fill:#f3e5f5
```

### 4. cleanStarlimsData

#### 入参对象与业务实体关联关系
| 入参对象字段 | 字段类型 | 关联业务实体.字段 | 说明 |
|------------|----------|-------------------|------|
| orderNo | String | GeneralOrderInstanceInfoPO.orderNo | 订单编号 |
| subContractNo | String | SubContractPO.subContractNo | 分包编号 |
| folderNo | String | SubContractExternalRelationshipPO.externalNo | 文件夹编号 |

#### 处理流程详细图
```
flowchart TD
    Start([开始]) --> A[接收数据清洗请求]
    A --> B{验证请求参数}
    B -->|验证失败| C[返回参数错误]
    C --> End([结束])
    
    B -->|验证通过| D[获取外部关系信息]
    D --> E{是否存在外部关系}
    E -->|不存在| F[创建新的外部关系]
    E -->|存在| G[使用现有外部关系]
    
    F --> H[处理外部关系列表]
    G --> H
    
    H --> I[遍历外部关系]
    I --> J[获取文件夹报告信息]
    J --> K{是否获取到报告信息}
    K -->|否| L[继续下一个]
    K -->|是| M[处理报告数据]
    M --> N[清洗Starlims数据]
    
    N --> O{清洗结果}
    O -->|成功| P[记录成功]
    O -->|失败| Q[记录失败]
    
    P --> R{是否还有更多关系}
    Q --> R
    R -->|是| I
    R -->|否| S[记录处理结果]
    
    S --> T[构建响应]
    T --> End
    
    style Start fill:#e1f5fe
    style P fill:#c8e6c9
    style Q fill:#ffcdd2
    style End fill:#f3e5f5
```

### 5. cancelSubReport

#### 入参对象与业务实体关联关系
| 入参对象字段 | 字段类型 | 关联业务实体.字段 | 说明 |
|------------|----------|-------------------|------|
| subContractNo | String | SubContractPO.subContractNo | 分包编号 |
| externalReportNo | String | - | 外部报告编号 |
| productLineCode | String | - | 产品线代码 |

#### 处理流程详细图
```
flowchart TD
    Start([开始]) --> A[接收取消报告请求]
    A --> B{验证请求参数}
    B -->|验证失败| C[返回参数错误]
    C --> End([结束])
    
    B -->|验证通过| D[StarLims鉴权]
    D --> E{鉴权结果}
    E -->|失败| F[返回鉴权失败]
    F --> End
    
    E -->|成功| G[调用分包服务取消报告]
    G --> H{取消结果}
    H -->|成功| I[记录成功日志]
    H -->|失败| J[记录失败日志]
    
    I --> K[构建成功响应]
    J --> L[构建失败响应]
    K --> End
    L --> End
    
    style Start fill:#e1f5fe
    style I fill:#c8e6c9
    style J fill:#ffcdd2
    style End fill:#f3e5f5
```

---

## 统一验证规则矩阵

### 第一阶段：基础参数验证

#### 基础参数验证规则
| 验证项 | 验证条件 | 错误信息 | 验证逻辑代码实现 | 失败影响 |
|--------|----------|----------|----------------|----------|
| 请求非空 | `body == null` | "Receive params is null ,please check the param that your send" | `if (body == null) { logger.info("接收到LocalIlayer的请求,参数为null "); result.setMsg("Receive params is null ,please check the param that your send"); return result; }` | 中断处理流程 |
| 订单编号非空 | `StringUtils.isBlank(orderNo)` | "parameter 'orderNo' can't be null" | `if (StringUtils.isBlank(orderNo)) { logger.info("[checkStarLimsData] 接收到LocalIlayer的请求,参数orderNo为null 无法继续校验"); result.setMsg("parameter 'orderNo' can't be null"); return result; }` | 中断处理流程 |
| 分包编号非空 | `StringUtils.isBlank(subContractNo)` | "there has some parameter are null,please check [subContractNo,objectType,objectNo]" | `if (StringUtils.isBlank(subContractNo) || StringUtils.isBlank(objectType) || StringUtils.isBlank(objectNo)) { logger.info("[checkStarLimsData]OrderNo:{} 接收到LocalIlayer的请求,subcontract:{},objectType:{},objectNo:{} 存在空值", orderNo, subContractNo, objectType, objectNo); result.setMsg("there has some parameter are null,please check [subContractNo,objectType,objectNo] "); return result; }` | 中断处理流程 |
| 对象编号非空 | `StringUtils.isBlank(objectNo)` | "there has some parameter are null,please check [subContractNo,objectType,objectNo]" | `if (StringUtils.isBlank(subContractNo) || StringUtils.isBlank(objectType) || StringUtils.isBlank(objectNo)) { logger.info("[checkStarLimsData]OrderNo:{} 接收到LocalIlayer的请求,subcontract:{},objectType:{},objectNo:{} 存在空值", orderNo, subContractNo, objectType, objectNo); result.setMsg("there has some parameter are null,please check [subContractNo,objectType,objectNo] "); return result; }` | 中断处理流程 |
| 对象类型非空 | `StringUtils.isBlank(objectType)` | "there has some parameter are null,please check [subContractNo,objectType,objectNo]" | `if (StringUtils.isBlank(subContractNo) || StringUtils.isBlank(objectType) || StringUtils.isBlank(objectNo)) { logger.info("[checkStarLimsData]OrderNo:{} 接收到LocalIlayer的请求,subcontract:{},objectType:{},objectNo:{} 存在空值", orderNo, subContractNo, objectType, objectNo); result.setMsg("there has some parameter are null,please check [subContractNo,objectType,objectNo] "); return result; }` | 中断处理流程 |

### 第二阶段：分包状态验证

#### 分包状态验证规则
| 验证项 | 验证条件 | 错误信息 | 验证逻辑代码实现 | 失败影响 |
|--------|----------|----------|----------------|----------|
| 分包存在性 | `subcontractInfo == null` | "unable to find subcontract information" | `SubContractPO subcontractInfo = subContractService.selectBySubcontractNo(subContractNo); if (subcontractInfo == null) { logger.info("subContractNo:{}[checkSubcontractStatus] 获取不到分包信息", subContractNo); result.setMsg("unable to find subcontract information"); return result; }` | 中断处理流程 |
| 分包渠道类型 | `!SubContractType.check(subContractOrder, SubContractType.ToStarLims)` | "not starLIMS subcontract" | `Integer subContractOrder = subcontractInfo.getSubContractOrder(); if (!SubContractType.check(subContractOrder, SubContractType.ToStarLims)) { logger.info("subContractNo:{}[checkSubcontractStatus] 当前分包单不是starLims的分包单：subContractOrder={}", subContractNo, subContractOrder); result.setMsg("not starLIMS subcontract"); return result; }` | 中断处理流程 |
| 分包取消状态 | `SubContractStatusEnum.check(status, SubContractStatusEnum.Cancelled)` | "subcontract already cancelled" | `Integer status = subcontractInfo.getStatus(); if (SubContractStatusEnum.check(status, SubContractStatusEnum.Cancelled)) { logger.info("subContractNo:{}[checkSubcontractStatus] 分包已经cancel", subContractNo); result.setMsg("subcontract already cancelled"); return result; }` | 中断处理流程 |

### 第三阶段：文件信息验证（receiveReportDoc）

#### 文件参数验证规则
| 验证项 | 验证条件 | 错误信息 | 验证逻辑代码实现 | 失败影响 |
|--------|----------|----------|----------------|----------|
| 文件列表非空 | `CollectionUtils.isEmpty(list)` | "there has some parameter are null,please check [files]" | `List<ReceiveFileInfo> list = body.getFiles(); if (CollectionUtils.isEmpty(list)) { logger.info("[checkReceiveReport] OrderNo:{} , files 为空", body.getOrderNo()); result.setMsg("there has some parameter are null,please check [files] "); }` | 中断处理流程 |
| 审批日期非空 | `StringUtils.isBlank(approveDate)` | "there has some parameter are null,please check [cloudId,objectType,fileName,approveDate]" | `String approveDate = body.getApproveDate(); for (ReceiveFileInfo x : list) { if (StringUtils.isBlank(x.getCloudId()) || StringUtils.isBlank(x.getFileId()) || StringUtils.isBlank(x.getFileName()) || StringUtils.isBlank(approveDate)) { logger.info("[checkReceiveReport]OrderNo:{} , cloudId:{},fileId:{},fileName:{},approveDate:{} 存在空值", orderNo, x.getCloudId(), x.getFileId(), x.getFileName(), approveDate); result.setMsg("there has some parameter are null,please check [cloudId,objectType,fileName,approveDate] "); break; } }` | 中断处理流程 |
| 文件参数完整性 | `StringUtils.isBlank(file.getFileId()) || StringUtils.isBlank(file.getCloudId()) || StringUtils.isBlank(file.getFileName())` | "there has some parameter are null,please check [cloudId,objectType,fileName,approveDate]" | `String approveDate = body.getApproveDate(); for (ReceiveFileInfo x : list) { if (StringUtils.isBlank(x.getCloudId()) || StringUtils.isBlank(x.getFileId()) || StringUtils.isBlank(x.getFileName()) || StringUtils.isBlank(approveDate)) { logger.info("[checkReceiveReport]OrderNo:{} , cloudId:{},fileId:{},fileName:{},approveDate:{} 存在空值", orderNo, x.getCloudId(), x.getFileId(), x.getFileName(), approveDate); result.setMsg("there has some parameter are null,please check [cloudId,objectType,fileName,approveDate] "); break; } }` | 中断处理流程 |

---

## 统一业务处理规则详解

### 分包同步规则
| 处理项 | 处理逻辑 | 调用接口/方法 | 成功标准 | 失败处理 |
|--------|----------|------------|----------|----------|
| 分包数据同步 | 将分包数据同步到StarLims系统 | toStarlimsExecutor.toStarlims | 同步接口返回成功 | 记录日志，返回错误 |

### 报告处理规则
| 处理项 | 处理逻辑 | 调用接口/方法 | 成功标准 | 失败处理 |
|--------|----------|------------|----------|----------|
| 报告数据保存 | 保存报告基础信息 | subReportFacade.buildSubReport4Starlims | 数据库保存成功 | 回滚事务，返回错误 |
| 文件信息保存 | 保存文件元数据到数据库 | subReportRemoteService.importReport | 数据库保存成功 | 回滚事务，返回错误 |
| 结论数据处理 | 批量处理结论数据 | conclusionService.batchConclusion | 结论数据保存成功 | 回滚事务，返回错误 |
| 测试组处理 | 处理测试组数据 | testGroupService.batchTestGroup | 测试组数据保存成功 | 记录日志，继续处理 |

### 状态更新规则
| 处理项 | 处理逻辑 | 调用接口/方法 | 成功标准 | 失败处理 |
|--------|----------|------------|----------|----------|
| 分包状态更新 | 更新分包状态 | subContractService.testingSubcontract | 状态更新成功 | 回滚事务，返回错误 |
| 订单状态同步 | 同步订单状态 | statusClient.insertStatusInfo | 状态更新成功 | 记录日志，继续处理 |

### 数据清洗规则
| 处理项 | 处理逻辑 | 调用接口/方法 | 成功标准 | 失败处理 |
|--------|----------|------------|----------|----------|
| 报告数据清洗 | 清洗Starlims报告数据 | testDataBizClient.cleanStarlimsData | 清洗接口返回成功 | 记录日志，返回错误 |

---

## 技术要素和监控指南

### 核心枚举定义
| 枚举类型 | 关键值 | 说明 | 业务影响 |
|---------|--------|------|----------|
| ProductLineType | SL, HL, MR | 产品线类型 | 决定处理逻辑分支 |
| SubContractStatusEnum | Created, Testing, Complete, Cancelled | 分包状态 | 影响状态流转和业务处理 |
| StarLimsTrackTypeEnums | FolderCreation, CommitFolder, FolderReported | StarLims轨迹类型 | 决定时间轨迹处理逻辑 |

### 关键错误类型和处理策略
| 错误级别 | 典型错误信息 | 处理策略 | 业务影响 |
|---------|-------------|----------|----------|
| 业务异常 | StarLimsReportException | 记录日志，返回错误信息 | 终止当前请求处理 |
| 系统异常 | RuntimeException | 记录异常堆栈，返回系统异常 | 终止当前请求处理 |
| 参数异常 | 参数校验失败 | 返回参数错误信息 | 终止当前请求处理 |

### 性能监控指标
| 监控维度 | 关键指标 | 正常范围 | 告警阈值 | 处理建议 |
|---------|----------|----------|----------|----------|
| 处理耗时 | 单次请求处理时间 | < 5秒 | > 10秒 | 检查处理器性能，优化数据库查询 |
| 成功率 | 处理成功率 | > 95% | < 90% | 检查业务逻辑，分析失败原因 |
| 并发量 | 同时处理请求数 | < 100 | > 200 | 考虑增加服务实例或优化处理逻辑 |

### 关键配置项
| 配置项 | 说明 | 默认值 | 影响范围 |
|--------|------|--------|----------|
| spring.transaction.timeout | 事务超时时间 | 30秒 | 整个处理流程 |
| logging.level.starlims | 日志级别 | INFO | 日志输出详细程度 |

---

## 问题排查指南

### 问题分类和快速诊断
| 问题类型 | 典型现象 | 快速排查点 | 常见解决方案 |
|---------|----------|------------|-------------|
| 参数校验失败 | 返回参数错误信息 | 检查请求参数完整性 | 补充缺失参数，修正参数格式 |
| 处理器执行失败 | 特定处理器报错 | 查看处理器执行日志 | 检查处理器业务逻辑，修复数据问题 |
| 数据库操作失败 | 事务回滚异常 | 检查数据库连接和数据完整性 | 修复数据问题，检查数据库性能 |
| 外部接口调用失败 | StarLims系统调用异常 | 检查网络连接和接口可用性 | 重试机制，降级处理 |

### 关键日志位置和分析方法
| 日志类型 | 关键字段 | 排查重点 | 分析建议 |
|---------|----------|----------|----------|
| 请求日志 | OrderNo, SubContractNo | 请求参数和处理结果 | 跟踪完整处理流程 |
| 处理器日志 | 处理器名称, 执行结果 | 具体处理器执行情况 | 定位失败的处理器 |
| 异常日志 | 异常堆栈, 错误信息 | 异常发生位置和原因 | 分析根本原因 |
| 性能日志 | 执行耗时, 处理器数量 | 性能瓶颈分析 | 优化慢处理器 |

### 常见问题及解决方案
| 问题描述 | 可能原因 | 解决方案 |
|---------|----------|----------|
| 分包状态不正确 | 状态流转逻辑错误 | 检查状态验证规则，确认状态转换条件 |
| 报告数据保存失败 | 数据库约束冲突 | 检查数据完整性，优化数据库索引 |
| StarLims接口调用失败 | 网络连接问题或认证失败 | 检查网络配置和认证信息 |
| 文件信息处理异常 | 文件格式不支持 | 检查文件类型验证逻辑 |

---

## 扩展性设计指南

### 新增处理逻辑步骤
1. **确定业务需求**：明确新增功能的业务场景和处理逻辑
2. **设计数据结构**：定义需要的业务实体和数据模型
3. **实现处理方法**：在StarLimsCommonService中添加新的处理方法
4. **添加验证规则**：实现必要的参数验证和业务规则校验
5. **编写单元测试**：确保新增功能的正确性和稳定性

### 性能优化建议
1. **数据库优化**：优化查询语句，添加必要索引
2. **缓存优化**：缓存频繁查询的数据
3. **异步处理**：对于非关键路径可考虑异步处理
4. **批量操作**：合并多个小操作为批量操作

---

## 版本变更记录

| 版本号 | 变更日期 | 变更内容 | 影响范围 | 变更人 |
|--------|----------|----------|----------|--------|
| v1.0 | 2025-04-05 | 初始版本，基础功能实现 | 核心功能 | 系统架构师 |

---

*文档版本：v1.0*  
*最后更新：2025年4月5日*  
*维护团队：StarLims集成开发组*