<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.otsnotes.dbstorages.mybatis.mapper.TestLineLabsectionRelationshipMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineLabsectionRelationshipPO" >
    <id column="id" property="id" jdbcType="VARCHAR" />
    <result column="order_id" property="orderId" jdbcType="VARCHAR" />
    <result column="test_line_instance_id" property="testLineInstanceId" jdbcType="VARCHAR" />
    <result column="lab_section_base_id" property="labSectionBaseId" jdbcType="BIGINT" />
    <result column="lab_section_id" property="labSectionId" jdbcType="BIGINT" />
    <result column="active_indicator" property="activeIndicator" jdbcType="BIT" />
    <result column="created_date" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="modified_date" property="modifiedDate" jdbcType="TIMESTAMP" />
    <result column="modified_by" property="modifiedBy" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, order_id, test_line_instance_id, lab_section_base_id, lab_section_id, active_indicator, 
    created_date, created_by, modified_date, modified_by
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineLabsectionRelationshipExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_testline_labsection_relationship
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from tb_testline_labsection_relationship
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from tb_testline_labsection_relationship
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineLabsectionRelationshipExample" >
    delete from tb_testline_labsection_relationship
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineLabsectionRelationshipPO" >
    insert into tb_testline_labsection_relationship (id, order_id, test_line_instance_id, 
      lab_section_base_id, lab_section_id, active_indicator, 
      created_date, created_by, modified_date, 
      modified_by)
    values (#{id,jdbcType=VARCHAR}, #{orderId,jdbcType=VARCHAR}, #{testLineInstanceId,jdbcType=VARCHAR}, 
      #{labSectionBaseId,jdbcType=BIGINT}, #{labSectionId,jdbcType=BIGINT}, #{activeIndicator,jdbcType=BIT}, 
      #{createdDate,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=VARCHAR}, #{modifiedDate,jdbcType=TIMESTAMP}, 
      #{modifiedBy,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineLabsectionRelationshipPO" >
    insert into tb_testline_labsection_relationship
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="orderId != null" >
        order_id,
      </if>
      <if test="testLineInstanceId != null" >
        test_line_instance_id,
      </if>
      <if test="labSectionBaseId != null" >
        lab_section_base_id,
      </if>
      <if test="labSectionId != null" >
        lab_section_id,
      </if>
      <if test="activeIndicator != null" >
        active_indicator,
      </if>
      <if test="createdDate != null" >
        created_date,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="modifiedDate != null" >
        modified_date,
      </if>
      <if test="modifiedBy != null" >
        modified_by,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null" >
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="testLineInstanceId != null" >
        #{testLineInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="labSectionBaseId != null" >
        #{labSectionBaseId,jdbcType=BIGINT},
      </if>
      <if test="labSectionId != null" >
        #{labSectionId,jdbcType=BIGINT},
      </if>
      <if test="activeIndicator != null" >
        #{activeIndicator,jdbcType=BIT},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineLabsectionRelationshipExample" resultType="java.lang.Integer" >
    select count(*) from tb_testline_labsection_relationship
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_testline_labsection_relationship
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.orderId != null" >
        order_id = #{record.orderId,jdbcType=VARCHAR},
      </if>
      <if test="record.testLineInstanceId != null" >
        test_line_instance_id = #{record.testLineInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="record.labSectionBaseId != null" >
        lab_section_base_id = #{record.labSectionBaseId,jdbcType=BIGINT},
      </if>
      <if test="record.labSectionId != null" >
        lab_section_id = #{record.labSectionId,jdbcType=BIGINT},
      </if>
      <if test="record.activeIndicator != null" >
        active_indicator = #{record.activeIndicator,jdbcType=BIT},
      </if>
      <if test="record.createdDate != null" >
        created_date = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdBy != null" >
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.modifiedDate != null" >
        modified_date = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedBy != null" >
        modified_by = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_testline_labsection_relationship
    set id = #{record.id,jdbcType=VARCHAR},
      order_id = #{record.orderId,jdbcType=VARCHAR},
      test_line_instance_id = #{record.testLineInstanceId,jdbcType=VARCHAR},
      lab_section_base_id = #{record.labSectionBaseId,jdbcType=BIGINT},
      lab_section_id = #{record.labSectionId,jdbcType=BIGINT},
      active_indicator = #{record.activeIndicator,jdbcType=BIT},
      created_date = #{record.createdDate,jdbcType=TIMESTAMP},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
      modified_date = #{record.modifiedDate,jdbcType=TIMESTAMP},
      modified_by = #{record.modifiedBy,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineLabsectionRelationshipPO" >
    update tb_testline_labsection_relationship
    <set >
      <if test="orderId != null" >
        order_id = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="testLineInstanceId != null" >
        test_line_instance_id = #{testLineInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="labSectionBaseId != null" >
        lab_section_base_id = #{labSectionBaseId,jdbcType=BIGINT},
      </if>
      <if test="labSectionId != null" >
        lab_section_id = #{labSectionId,jdbcType=BIGINT},
      </if>
      <if test="activeIndicator != null" >
        active_indicator = #{activeIndicator,jdbcType=BIT},
      </if>
      <if test="createdDate != null" >
        created_date = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        modified_date = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        modified_by = #{modifiedBy,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineLabsectionRelationshipPO" >
    update tb_testline_labsection_relationship
    set order_id = #{orderId,jdbcType=VARCHAR},
      test_line_instance_id = #{testLineInstanceId,jdbcType=VARCHAR},
      lab_section_base_id = #{labSectionBaseId,jdbcType=BIGINT},
      lab_section_id = #{labSectionId,jdbcType=BIGINT},
      active_indicator = #{activeIndicator,jdbcType=BIT},
      created_date = #{createdDate,jdbcType=TIMESTAMP},
      created_by = #{createdBy,jdbcType=VARCHAR},
      modified_date = #{modifiedDate,jdbcType=TIMESTAMP},
      modified_by = #{modifiedBy,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>