/**
 * MergeDocumentBySectionBreakResponse.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class MergeDocumentBySectionBreakResponse  implements java.io.Serializable {
    private String mergeDocumentBySectionBreakResult;

    private String errMsg;

    public MergeDocumentBySectionBreakResponse() {
    }

    public MergeDocumentBySectionBreakResponse(
           String mergeDocumentBySectionBreakResult,
           String errMsg) {
           this.mergeDocumentBySectionBreakResult = mergeDocumentBySectionBreakResult;
           this.errMsg = errMsg;
    }


    /**
     * Gets the mergeDocumentBySectionBreakResult value for this MergeDocumentBySectionBreakResponse.
     * 
     * @return mergeDocumentBySectionBreakResult
     */
    public String getMergeDocumentBySectionBreakResult() {
        return mergeDocumentBySectionBreakResult;
    }


    /**
     * Sets the mergeDocumentBySectionBreakResult value for this MergeDocumentBySectionBreakResponse.
     * 
     * @param mergeDocumentBySectionBreakResult
     */
    public void setMergeDocumentBySectionBreakResult(String mergeDocumentBySectionBreakResult) {
        this.mergeDocumentBySectionBreakResult = mergeDocumentBySectionBreakResult;
    }


    /**
     * Gets the errMsg value for this MergeDocumentBySectionBreakResponse.
     * 
     * @return errMsg
     */
    public String getErrMsg() {
        return errMsg;
    }


    /**
     * Sets the errMsg value for this MergeDocumentBySectionBreakResponse.
     * 
     * @param errMsg
     */
    public void setErrMsg(String errMsg) {
        this.errMsg = errMsg;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof MergeDocumentBySectionBreakResponse)) {
            return false;
        }
        MergeDocumentBySectionBreakResponse other = (MergeDocumentBySectionBreakResponse) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.mergeDocumentBySectionBreakResult==null && other.getMergeDocumentBySectionBreakResult()==null) || 
             (this.mergeDocumentBySectionBreakResult!=null &&
              this.mergeDocumentBySectionBreakResult.equals(other.getMergeDocumentBySectionBreakResult()))) &&
            ((this.errMsg==null && other.getErrMsg()==null) || 
             (this.errMsg!=null &&
              this.errMsg.equals(other.getErrMsg())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getMergeDocumentBySectionBreakResult() != null) {
            _hashCode += getMergeDocumentBySectionBreakResult().hashCode();
        }
        if (getErrMsg() != null) {
            _hashCode += getErrMsg().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(MergeDocumentBySectionBreakResponse.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", ">MergeDocumentBySectionBreakResponse"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("mergeDocumentBySectionBreakResult");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "MergeDocumentBySectionBreakResult"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("errMsg");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "errMsg"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
