<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.otsnotes.dbstorages.mybatis.mapper.SubcontractRequirementContactsMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.otsnotes.dbstorages.mybatis.model.SubcontractRequirementContactsPO" >
    <id column="id" property="id" jdbcType="VARCHAR" />
    <result column="order_id" property="order_id" jdbcType="VARCHAR" />
    <result column="subcontract_id" property="subcontract_id" jdbcType="VARCHAR" />
    <result column="contacts_type" property="contacts_type" jdbcType="TINYINT" />
    <result column="deliver_to" property="deliver_to" jdbcType="VARCHAR" />
    <result column="deliver_others" property="deliver_others" jdbcType="VARCHAR" />
    <result column="created_date" property="created_date" jdbcType="TIMESTAMP" />
    <result column="created_by" property="created_by" jdbcType="VARCHAR" />
    <result column="modified_date" property="modified_date" jdbcType="TIMESTAMP" />
    <result column="modified_by" property="modified_by" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, order_id, subcontract_id, contacts_type, deliver_to, deliver_others, created_date, 
    created_by, modified_date, modified_by
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.SubcontractRequirementContactsExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_subcontract_requirement_contacts
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from tb_subcontract_requirement_contacts
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from tb_subcontract_requirement_contacts
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.SubcontractRequirementContactsExample" >
    delete from tb_subcontract_requirement_contacts
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.SubcontractRequirementContactsPO" >
    insert into tb_subcontract_requirement_contacts (id, order_id, subcontract_id, 
      contacts_type, deliver_to, deliver_others, 
      created_date, created_by, modified_date, 
      modified_by)
    values (#{id,jdbcType=VARCHAR}, #{order_id,jdbcType=VARCHAR}, #{subcontract_id,jdbcType=VARCHAR}, 
      #{contacts_type,jdbcType=TINYINT}, #{deliver_to,jdbcType=VARCHAR}, #{deliver_others,jdbcType=VARCHAR}, 
      #{created_date,jdbcType=TIMESTAMP}, #{created_by,jdbcType=VARCHAR}, #{modified_date,jdbcType=TIMESTAMP}, 
      #{modified_by,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.SubcontractRequirementContactsPO" >
    insert into tb_subcontract_requirement_contacts
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="order_id != null" >
        order_id,
      </if>
      <if test="subcontract_id != null" >
        subcontract_id,
      </if>
      <if test="contacts_type != null" >
        contacts_type,
      </if>
      <if test="deliver_to != null" >
        deliver_to,
      </if>
      <if test="deliver_others != null" >
        deliver_others,
      </if>
      <if test="created_date != null" >
        created_date,
      </if>
      <if test="created_by != null" >
        created_by,
      </if>
      <if test="modified_date != null" >
        modified_date,
      </if>
      <if test="modified_by != null" >
        modified_by,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="order_id != null" >
        #{order_id,jdbcType=VARCHAR},
      </if>
      <if test="subcontract_id != null" >
        #{subcontract_id,jdbcType=VARCHAR},
      </if>
      <if test="contacts_type != null" >
        #{contacts_type,jdbcType=TINYINT},
      </if>
      <if test="deliver_to != null" >
        #{deliver_to,jdbcType=VARCHAR},
      </if>
      <if test="deliver_others != null" >
        #{deliver_others,jdbcType=VARCHAR},
      </if>
      <if test="created_date != null" >
        #{created_date,jdbcType=TIMESTAMP},
      </if>
      <if test="created_by != null" >
        #{created_by,jdbcType=VARCHAR},
      </if>
      <if test="modified_date != null" >
        #{modified_date,jdbcType=TIMESTAMP},
      </if>
      <if test="modified_by != null" >
        #{modified_by,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.SubcontractRequirementContactsExample" resultType="java.lang.Integer" >
    select count(*) from tb_subcontract_requirement_contacts
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_subcontract_requirement_contacts
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.order_id != null" >
        order_id = #{record.order_id,jdbcType=VARCHAR},
      </if>
      <if test="record.subcontract_id != null" >
        subcontract_id = #{record.subcontract_id,jdbcType=VARCHAR},
      </if>
      <if test="record.contacts_type != null" >
        contacts_type = #{record.contacts_type,jdbcType=TINYINT},
      </if>
      <if test="record.deliver_to != null" >
        deliver_to = #{record.deliver_to,jdbcType=VARCHAR},
      </if>
      <if test="record.deliver_others != null" >
        deliver_others = #{record.deliver_others,jdbcType=VARCHAR},
      </if>
      <if test="record.created_date != null" >
        created_date = #{record.created_date,jdbcType=TIMESTAMP},
      </if>
      <if test="record.created_by != null" >
        created_by = #{record.created_by,jdbcType=VARCHAR},
      </if>
      <if test="record.modified_date != null" >
        modified_date = #{record.modified_date,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modified_by != null" >
        modified_by = #{record.modified_by,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_subcontract_requirement_contacts
    set id = #{record.id,jdbcType=VARCHAR},
      order_id = #{record.order_id,jdbcType=VARCHAR},
      subcontract_id = #{record.subcontract_id,jdbcType=VARCHAR},
      contacts_type = #{record.contacts_type,jdbcType=TINYINT},
      deliver_to = #{record.deliver_to,jdbcType=VARCHAR},
      deliver_others = #{record.deliver_others,jdbcType=VARCHAR},
      created_date = #{record.created_date,jdbcType=TIMESTAMP},
      created_by = #{record.created_by,jdbcType=VARCHAR},
      modified_date = #{record.modified_date,jdbcType=TIMESTAMP},
      modified_by = #{record.modified_by,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.SubcontractRequirementContactsPO" >
    update tb_subcontract_requirement_contacts
    <set >
      <if test="order_id != null" >
        order_id = #{order_id,jdbcType=VARCHAR},
      </if>
      <if test="subcontract_id != null" >
        subcontract_id = #{subcontract_id,jdbcType=VARCHAR},
      </if>
      <if test="contacts_type != null" >
        contacts_type = #{contacts_type,jdbcType=TINYINT},
      </if>
      <if test="deliver_to != null" >
        deliver_to = #{deliver_to,jdbcType=VARCHAR},
      </if>
      <if test="deliver_others != null" >
        deliver_others = #{deliver_others,jdbcType=VARCHAR},
      </if>
      <if test="created_date != null" >
        created_date = #{created_date,jdbcType=TIMESTAMP},
      </if>
      <if test="created_by != null" >
        created_by = #{created_by,jdbcType=VARCHAR},
      </if>
      <if test="modified_date != null" >
        modified_date = #{modified_date,jdbcType=TIMESTAMP},
      </if>
      <if test="modified_by != null" >
        modified_by = #{modified_by,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.SubcontractRequirementContactsPO" >
    update tb_subcontract_requirement_contacts
    set order_id = #{order_id,jdbcType=VARCHAR},
      subcontract_id = #{subcontract_id,jdbcType=VARCHAR},
      contacts_type = #{contacts_type,jdbcType=TINYINT},
      deliver_to = #{deliver_to,jdbcType=VARCHAR},
      deliver_others = #{deliver_others,jdbcType=VARCHAR},
      created_date = #{created_date,jdbcType=TIMESTAMP},
      created_by = #{created_by,jdbcType=VARCHAR},
      modified_date = #{modified_date,jdbcType=TIMESTAMP},
      modified_by = #{modified_by,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>