<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.otsnotes.dbstorages.mybatis.mapper.MDLProductAttributeRelInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.otsnotes.dbstorages.mybatis.model.MDLProductAttributeRelInfoPO" >
    <result column="id" property="id" jdbcType="BIGINT" />
    <result column="mdl_base_id" property="mdlBaseId" jdbcType="BIGINT" />
    <result column="product_attribute_id" property="productAttributeId" jdbcType="INTEGER" />
    <result column="biz_version_id" property="bizVersionId" jdbcType="CHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="created_date" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="modified_date" property="modifiedDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, mdl_base_id, product_attribute_id, biz_version_id, `status`, created_date, modified_date
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.MDLProductAttributeRelInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_trims_mdl_product_attribute_relationship
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.MDLProductAttributeRelInfoExample" >
    delete from tb_trims_mdl_product_attribute_relationship
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.MDLProductAttributeRelInfoPO" >
    insert into tb_trims_mdl_product_attribute_relationship (id, mdl_base_id, product_attribute_id,
      biz_version_id, `status`, created_date, 
      modified_date)
    values (#{id,jdbcType=BIGINT}, #{mdlBaseId,jdbcType=BIGINT}, #{productAttributeId,jdbcType=INTEGER}, 
      #{bizVersionId,jdbcType=CHAR}, #{status,jdbcType=INTEGER}, #{createdDate,jdbcType=TIMESTAMP}, 
      #{modifiedDate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.MDLProductAttributeRelInfoPO" >
    insert into tb_trims_mdl_product_attribute_relationship
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="mdlBaseId != null" >
        mdl_base_id,
      </if>
      <if test="productAttributeId != null" >
        product_attribute_id,
      </if>
      <if test="bizVersionId != null" >
        biz_version_id,
      </if>
      <if test="status != null" >
        `status`,
      </if>
      <if test="createdDate != null" >
        created_date,
      </if>
      <if test="modifiedDate != null" >
        modified_date,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="mdlBaseId != null" >
        #{mdlBaseId,jdbcType=BIGINT},
      </if>
      <if test="productAttributeId != null" >
        #{productAttributeId,jdbcType=INTEGER},
      </if>
      <if test="bizVersionId != null" >
        #{bizVersionId,jdbcType=CHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.MDLProductAttributeRelInfoExample" resultType="java.lang.Integer" >
    select count(*) from tb_trims_mdl_product_attribute_relationship
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_trims_mdl_product_attribute_relationship
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.mdlBaseId != null" >
        mdl_base_id = #{record.mdlBaseId,jdbcType=BIGINT},
      </if>
      <if test="record.productAttributeId != null" >
        product_attribute_id = #{record.productAttributeId,jdbcType=INTEGER},
      </if>
      <if test="record.bizVersionId != null" >
        biz_version_id = #{record.bizVersionId,jdbcType=CHAR},
      </if>
      <if test="record.status != null" >
        `status` = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.createdDate != null" >
        created_date = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedDate != null" >
        modified_date = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_trims_mdl_product_attribute_relationship
    set id = #{record.id,jdbcType=BIGINT},
      mdl_base_id = #{record.mdlBaseId,jdbcType=BIGINT},
      product_attribute_id = #{record.productAttributeId,jdbcType=INTEGER},
      biz_version_id = #{record.bizVersionId,jdbcType=CHAR},
      `status` = #{record.status,jdbcType=INTEGER},
      created_date = #{record.createdDate,jdbcType=TIMESTAMP},
      modified_date = #{record.modifiedDate,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <insert id="batchInsert" parameterType="list" >
    insert into tb_trims_mdl_product_attribute_relationship
      (`id`,`mdl_base_id`,`product_attribute_id`,
      `biz_version_id`,`status`,`created_date`,
      `modified_date`)
    values 
    <foreach collection="list" item="item" index="index" separator="," > 
      ( #{ item.id, jdbcType=BIGINT},#{ item.mdlBaseId, jdbcType=BIGINT},#{ item.productAttributeId, jdbcType=INTEGER},
      #{ item.bizVersionId, jdbcType=CHAR},#{ item.status, jdbcType=INTEGER},#{ item.createdDate, jdbcType=TIMESTAMP},
      #{ item.modifiedDate, jdbcType=TIMESTAMP}) 
    </foreach>
  </insert>
  <update id="batchUpdate" parameterType="list" >
    <foreach collection="list" item="item" index="index" separator=";" > 
      update tb_trims_mdl_product_attribute_relationship
      <set>
        <if test="item.id != null"> 
          `id` = #{item.id, jdbcType = BIGINT},
        </if> 
        <if test="item.mdlBaseId != null"> 
          `mdl_base_id` = #{item.mdlBaseId, jdbcType = BIGINT},
        </if> 
        <if test="item.productAttributeId != null"> 
          `product_attribute_id` = #{item.productAttributeId, jdbcType = INTEGER},
        </if> 
        <if test="item.bizVersionId != null"> 
          `biz_version_id` = #{item.bizVersionId, jdbcType = CHAR},
        </if> 
        <if test="item.status != null"> 
          `status` = #{item.status, jdbcType = INTEGER},
        </if> 
        <if test="item.createdDate != null"> 
          `created_date` = #{item.createdDate, jdbcType = TIMESTAMP},
        </if> 
        <if test="item.modifiedDate != null"> 
          `modified_date` = #{item.modifiedDate, jdbcType = TIMESTAMP},
        </if> 
      </set>
      <where>
      </where>
    </foreach>
  </update>
</mapper>