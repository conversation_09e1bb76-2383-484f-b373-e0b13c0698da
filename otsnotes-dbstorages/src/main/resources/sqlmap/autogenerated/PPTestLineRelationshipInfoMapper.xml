<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.otsnotes.dbstorages.mybatis.mapper.PPTestLineRelationshipInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.otsnotes.dbstorages.mybatis.model.PPTestLineRelationshipInfoPO" >
    <id column="ID" property="ID" jdbcType="VARCHAR" />
    <result column="GeneralOrderInstanceID" property="generalOrderInstanceID" jdbcType="VARCHAR" />
    <result column="PpArtifactRelId" property="ppArtifactRelId" jdbcType="BIGINT" />
    <result column="PpBaseId" property="ppBaseId" jdbcType="BIGINT" />
    <result column="RootPpBaseId" property="rootPpBaseId" jdbcType="BIGINT" />
    <result column="TestLineInstanceID" property="testLineInstanceID" jdbcType="VARCHAR" />
    <result column="PPInstanceID" property="PPInstanceID" jdbcType="VARCHAR" />
    <result column="SectionID" property="sectionID" jdbcType="INTEGER" />
    <result column="SectionName" property="sectionName" jdbcType="VARCHAR" />
    <result column="SectionLevel" property="sectionLevel" jdbcType="VARCHAR" />
    <result column="PPNotes" property="PPNotes" jdbcType="VARCHAR" />
    <result column="CreatedDate" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="CreatedBy" property="createdBy" jdbcType="VARCHAR" />
    <result column="ModifiedDate" property="modifiedDate" jdbcType="TIMESTAMP" />
    <result column="ModifiedBy" property="modifiedBy" jdbcType="VARCHAR" />
    <result column="aid" property="aid" jdbcType="BIGINT" />
    <result column="ConstructionId" property="constructionId" jdbcType="VARCHAR" />
    <result column="QuotationTestlineInstanceID" property="quotationTestlineInstanceID" jdbcType="VARCHAR" />
    <result column="SubPpRelSeq" property="subPpRelSeq" jdbcType="INTEGER" />
    <result column="Seq" property="seq" jdbcType="BIGINT" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    ID, GeneralOrderInstanceID, PpArtifactRelId, PpBaseId, RootPpBaseId, TestLineInstanceID, 
    PPInstanceID, SectionID, SectionName, SectionLevel, PPNotes, CreatedDate, CreatedBy, 
    ModifiedDate, ModifiedBy, aid, ConstructionId, QuotationTestlineInstanceID, SubPpRelSeq, Seq
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.PPTestLineRelationshipInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tre_pp_test_line_relationship
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from tre_pp_test_line_relationship
    where ID = #{ID,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from tre_pp_test_line_relationship
    where ID = #{ID,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.PPTestLineRelationshipInfoExample" >
    delete from tre_pp_test_line_relationship
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.PPTestLineRelationshipInfoPO" >
    insert into tre_pp_test_line_relationship (ID, GeneralOrderInstanceID, PpArtifactRelId, 
      PpBaseId, RootPpBaseId, TestLineInstanceID, 
      PPInstanceID, SectionID, SectionName, 
      SectionLevel, PPNotes, CreatedDate, 
      CreatedBy, ModifiedDate, ModifiedBy, 
      aid, ConstructionId, QuotationTestlineInstanceID, SubPpRelSeq,
      Seq)
    values (#{ID,jdbcType=VARCHAR}, #{generalOrderInstanceID,jdbcType=VARCHAR}, #{ppArtifactRelId,jdbcType=BIGINT}, 
      #{ppBaseId,jdbcType=BIGINT}, #{rootPpBaseId,jdbcType=BIGINT}, #{testLineInstanceID,jdbcType=VARCHAR}, 
      #{PPInstanceID,jdbcType=VARCHAR}, #{sectionID,jdbcType=INTEGER}, #{sectionName,jdbcType=VARCHAR}, 
      #{sectionLevel,jdbcType=VARCHAR}, #{PPNotes,jdbcType=VARCHAR}, #{createdDate,jdbcType=TIMESTAMP}, 
      #{createdBy,jdbcType=VARCHAR}, #{modifiedDate,jdbcType=TIMESTAMP}, #{modifiedBy,jdbcType=VARCHAR}, 
      #{aid,jdbcType=BIGINT}, #{constructionId,jdbcType=VARCHAR}, #{quotationTestlineInstanceID,jdbcType=VARCHAR}, #{subPpRelSeq,jdbcType=INTEGER},
      #{seq,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.PPTestLineRelationshipInfoPO" >
    insert into tre_pp_test_line_relationship
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="ID != null" >
        ID,
      </if>
      <if test="generalOrderInstanceID != null" >
        GeneralOrderInstanceID,
      </if>
      <if test="ppArtifactRelId != null" >
        PpArtifactRelId,
      </if>
      <if test="ppBaseId != null" >
        PpBaseId,
      </if>
      <if test="rootPpBaseId != null" >
        RootPpBaseId,
      </if>
      <if test="testLineInstanceID != null" >
        TestLineInstanceID,
      </if>
      <if test="PPInstanceID != null" >
        PPInstanceID,
      </if>
      <if test="sectionID != null" >
        SectionID,
      </if>
      <if test="sectionName != null" >
        SectionName,
      </if>
      <if test="sectionLevel != null" >
        SectionLevel,
      </if>
      <if test="PPNotes != null" >
        PPNotes,
      </if>
      <if test="createdDate != null" >
        CreatedDate,
      </if>
      <if test="createdBy != null" >
        CreatedBy,
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate,
      </if>
      <if test="modifiedBy != null" >
        ModifiedBy,
      </if>
      <if test="aid != null" >
        aid,
      </if>
      <if test="constructionId != null" >
        ConstructionId,
      </if>
      <if test="quotationTestlineInstanceID != null" >
        QuotationTestlineInstanceID,
      </if>
      <if test="subPpRelSeq != null" >
        SubPpRelSeq,
      </if>
      <if test="seq != null" >
        Seq,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="ID != null" >
        #{ID,jdbcType=VARCHAR},
      </if>
      <if test="generalOrderInstanceID != null" >
        #{generalOrderInstanceID,jdbcType=VARCHAR},
      </if>
      <if test="ppArtifactRelId != null" >
        #{ppArtifactRelId,jdbcType=BIGINT},
      </if>
      <if test="ppBaseId != null" >
        #{ppBaseId,jdbcType=BIGINT},
      </if>
      <if test="rootPpBaseId != null" >
        #{rootPpBaseId,jdbcType=BIGINT},
      </if>
      <if test="testLineInstanceID != null" >
        #{testLineInstanceID,jdbcType=VARCHAR},
      </if>
      <if test="PPInstanceID != null" >
        #{PPInstanceID,jdbcType=VARCHAR},
      </if>
      <if test="sectionID != null" >
        #{sectionID,jdbcType=INTEGER},
      </if>
      <if test="sectionName != null" >
        #{sectionName,jdbcType=VARCHAR},
      </if>
      <if test="sectionLevel != null" >
        #{sectionLevel,jdbcType=VARCHAR},
      </if>
      <if test="PPNotes != null" >
        #{PPNotes,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="aid != null" >
        #{aid,jdbcType=BIGINT},
      </if>
      <if test="constructionId != null" >
        #{constructionId,jdbcType=VARCHAR},
      </if>
      <if test="quotationTestlineInstanceID != null" >
        #{quotationTestlineInstanceID,jdbcType=VARCHAR},
      </if>
      <if test="subPpRelSeq != null" >
        #{subPpRelSeq,jdbcType=INTEGER},
      </if>
      <if test="seq != null" >
        #{seq,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.PPTestLineRelationshipInfoExample" resultType="java.lang.Integer" >
    select count(*) from tre_pp_test_line_relationship
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tre_pp_test_line_relationship
    <set >
      <if test="record.ID != null" >
        ID = #{record.ID,jdbcType=VARCHAR},
      </if>
      <if test="record.generalOrderInstanceID != null" >
        GeneralOrderInstanceID = #{record.generalOrderInstanceID,jdbcType=VARCHAR},
      </if>
      <if test="record.ppArtifactRelId != null" >
        PpArtifactRelId = #{record.ppArtifactRelId,jdbcType=BIGINT},
      </if>
      <if test="record.ppBaseId != null" >
        PpBaseId = #{record.ppBaseId,jdbcType=BIGINT},
      </if>
      <if test="record.rootPpBaseId != null" >
        RootPpBaseId = #{record.rootPpBaseId,jdbcType=BIGINT},
      </if>
      <if test="record.testLineInstanceID != null" >
        TestLineInstanceID = #{record.testLineInstanceID,jdbcType=VARCHAR},
      </if>
      <if test="record.PPInstanceID != null" >
        PPInstanceID = #{record.PPInstanceID,jdbcType=VARCHAR},
      </if>
      <if test="record.sectionID != null" >
        SectionID = #{record.sectionID,jdbcType=INTEGER},
      </if>
      <if test="record.sectionName != null" >
        SectionName = #{record.sectionName,jdbcType=VARCHAR},
      </if>
      <if test="record.sectionLevel != null" >
        SectionLevel = #{record.sectionLevel,jdbcType=VARCHAR},
      </if>
      <if test="record.PPNotes != null" >
        PPNotes = #{record.PPNotes,jdbcType=VARCHAR},
      </if>
      <if test="record.createdDate != null" >
        CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdBy != null" >
        CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.modifiedDate != null" >
        ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedBy != null" >
        ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.aid != null" >
        aid = #{record.aid,jdbcType=BIGINT},
      </if>
      <if test="record.constructionId != null" >
        ConstructionId = #{record.constructionId,jdbcType=VARCHAR},
      </if>
      <if test="record.quotationTestlineInstanceID != null" >
        QuotationTestlineInstanceID = #{record.quotationTestlineInstanceID,jdbcType=VARCHAR},
      </if>
      <if test="record.subPpRelSeq != null" >
        SubPpRelSeq = #{record.subPpRelSeq,jdbcType=INTEGER},
      </if>
      <if test="record.seq != null" >
        Seq = #{record.seq,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tre_pp_test_line_relationship
    set ID = #{record.ID,jdbcType=VARCHAR},
      GeneralOrderInstanceID = #{record.generalOrderInstanceID,jdbcType=VARCHAR},
      PpArtifactRelId = #{record.ppArtifactRelId,jdbcType=BIGINT},
      PpBaseId = #{record.ppBaseId,jdbcType=BIGINT},
      RootPpBaseId = #{record.rootPpBaseId,jdbcType=BIGINT},
      TestLineInstanceID = #{record.testLineInstanceID,jdbcType=VARCHAR},
      PPInstanceID = #{record.PPInstanceID,jdbcType=VARCHAR},
      SectionID = #{record.sectionID,jdbcType=INTEGER},
      SectionName = #{record.sectionName,jdbcType=VARCHAR},
      SectionLevel = #{record.sectionLevel,jdbcType=VARCHAR},
      PPNotes = #{record.PPNotes,jdbcType=VARCHAR},
      CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      aid = #{record.aid,jdbcType=BIGINT},
      ConstructionId = #{record.constructionId,jdbcType=VARCHAR},
      QuotationTestlineInstanceID = #{record.quotationTestlineInstanceID,jdbcType=VARCHAR},
      SubPpRelSeq = #{record.subPpRelSeq,jdbcType=INTEGER},
      Seq = #{record.seq,jdbcType=BIGINT}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.PPTestLineRelationshipInfoPO" >
    update tre_pp_test_line_relationship
    <set >
      <if test="generalOrderInstanceID != null" >
        GeneralOrderInstanceID = #{generalOrderInstanceID,jdbcType=VARCHAR},
      </if>
      <if test="ppArtifactRelId != null" >
        PpArtifactRelId = #{ppArtifactRelId,jdbcType=BIGINT},
      </if>
      <if test="ppBaseId != null" >
        PpBaseId = #{ppBaseId,jdbcType=BIGINT},
      </if>
      <if test="rootPpBaseId != null" >
        RootPpBaseId = #{rootPpBaseId,jdbcType=BIGINT},
      </if>
      <if test="testLineInstanceID != null" >
        TestLineInstanceID = #{testLineInstanceID,jdbcType=VARCHAR},
      </if>
      <if test="PPInstanceID != null" >
        PPInstanceID = #{PPInstanceID,jdbcType=VARCHAR},
      </if>
      <if test="sectionID != null" >
        SectionID = #{sectionID,jdbcType=INTEGER},
      </if>
      <if test="sectionName != null" >
        SectionName = #{sectionName,jdbcType=VARCHAR},
      </if>
      <if test="sectionLevel != null" >
        SectionLevel = #{sectionLevel,jdbcType=VARCHAR},
      </if>
      <if test="PPNotes != null" >
        PPNotes = #{PPNotes,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        CreatedBy = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        ModifiedBy = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="aid != null" >
        aid = #{aid,jdbcType=BIGINT},
      </if>
      <if test="constructionId != null" >
        ConstructionId = #{constructionId,jdbcType=VARCHAR},
      </if>
      <if test="quotationTestlineInstanceID != null" >
        QuotationTestlineInstanceID = #{quotationTestlineInstanceID,jdbcType=VARCHAR},
      </if>
      <if test="subPpRelSeq != null" >
        SubPpRelSeq = #{subPpRelSeq,jdbcType=INTEGER},
      </if>
      <if test="seq != null" >
        Seq = #{seq,jdbcType=BIGINT},
      </if>
    </set>
    where ID = #{ID,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.PPTestLineRelationshipInfoPO" >
    update tre_pp_test_line_relationship
    set GeneralOrderInstanceID = #{generalOrderInstanceID,jdbcType=VARCHAR},
      PpArtifactRelId = #{ppArtifactRelId,jdbcType=BIGINT},
      PpBaseId = #{ppBaseId,jdbcType=BIGINT},
      RootPpBaseId = #{rootPpBaseId,jdbcType=BIGINT},
      TestLineInstanceID = #{testLineInstanceID,jdbcType=VARCHAR},
      PPInstanceID = #{PPInstanceID,jdbcType=VARCHAR},
      SectionID = #{sectionID,jdbcType=INTEGER},
      SectionName = #{sectionName,jdbcType=VARCHAR},
      SectionLevel = #{sectionLevel,jdbcType=VARCHAR},
      PPNotes = #{PPNotes,jdbcType=VARCHAR},
      CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      CreatedBy = #{createdBy,jdbcType=VARCHAR},
      ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{modifiedBy,jdbcType=VARCHAR},
      aid = #{aid,jdbcType=BIGINT},
      ConstructionId = #{constructionId,jdbcType=VARCHAR},
      QuotationTestlineInstanceID = #{quotationTestlineInstanceID,jdbcType=VARCHAR},
      SubPpRelSeq = #{subPpRelSeq,jdbcType=INTEGER},
      Seq = #{seq,jdbcType=BIGINT}
    where ID = #{ID,jdbcType=VARCHAR}
  </update>
</mapper>