# TestSample对象层级管理和能力文档 v1.0

## 文档说明
本文档详细描述了TestSample对象的层级管理架构和核心能力，包括树形结构设计、Group关系管理、以及各种业务操作能力。TestSample作为测试样本的核心领域对象，支持3层级的树形结构和横向Group关系管理。

## 业务概述
TestSample是测试管理系统中的核心领域对象，用于管理测试样本的层级关系和分组逻辑。通过树形结构实现垂直层级管理，通过Group对象实现横向关系管理，为复杂的测试场景提供灵活的数据组织能力。

## 核心特性
- **树形结构**：支持3层级的父子关系管理
- **深度控制**：子节点最大深度为2
- **横向关系**：通过Group对象建立TestSample间的关联
- **类型管理**：支持share和mix两种Group类型
- **动态扩展**：支持运行时动态调整层级关系

---

## 对象结构定义

### TestSample核心属性

| 字段名 | 类型 | 说明 | 约束条件 |
|--------|------|------|----------|
| id | String | 唯一标识符 | 主键，非空 |
| name | String | 样本名称 | 非空，长度1-100 |
| code | String | 样本编码 | 唯一，非空 |
| type | SampleType | 样本类型 | 枚举值 |
| status | SampleStatus | 样本状态 | 枚举值 |
| level | Integer | 层级深度 | 0-2，0为根节点 |
| parentId | String | 父节点ID | 可空，根节点为null |
| children | List<TestSample> | 子节点集合 | 延迟加载 |
| groups | List<Group> | 关联的Group集合 | 多对多关系 |
| properties | Map<String, Object> | 扩展属性 | JSON格式存储 |
| createdBy | String | 创建人 | 非空 |
| createdDate | Date | 创建时间 | 非空 |
| modifiedBy | String | 修改人 | 可空 |
| modifiedDate | Date | 修改时间 | 可空 |

### Group对象属性

| 字段名 | 类型 | 说明 | 约束条件 |
|--------|------|------|----------|
| id | String | 唯一标识符 | 主键，非空 |
| name | String | 分组名称 | 非空，长度1-50 |
| type | GroupType | 分组类型 | SHARE或MIX |
| description | String | 分组描述 | 可空，长度0-200 |
| samples | List<TestSample> | 关联的样本集合 | 多对多关系 |
| priority | Integer | 优先级 | 1-100，默认50 |
| isActive | Boolean | 是否激活 | 默认true |
| createdBy | String | 创建人 | 非空 |
| createdDate | Date | 创建时间 | 非空 |

### 枚举定义

#### SampleType（样本类型）
```java
public enum SampleType {
    ROOT("根节点样本"),
    BRANCH("分支节点样本"), 
    LEAF("叶子节点样本"),
    TEMPLATE("模板样本");
}
```

#### SampleStatus（样本状态）
```java
public enum SampleStatus {
    DRAFT("草稿"),
    ACTIVE("激活"),
    INACTIVE("停用"),
    ARCHIVED("归档");
}
```

#### GroupType（分组类型）
```java
public enum GroupType {
    SHARE("共享型分组 - 样本间共享资源和配置"),
    MIX("混合型分组 - 样本间建立复杂业务关系");
}
```

---

## 层级管理架构

### 三层级结构设计

```
Level 0 (根节点)
├── Level 1 (中间节点)
│   ├── Level 2 (叶子节点)
│   ├── Level 2 (叶子节点)
│   └── Level 2 (叶子节点)
├── Level 1 (中间节点)
│   ├── Level 2 (叶子节点)
│   └── Level 2 (叶子节点)
└── Level 1 (中间节点)
    └── Level 2 (叶子节点)
```

### 层级约束规则

| 层级 | 名称 | 约束条件 | 业务含义 |
|------|------|----------|----------|
| Level 0 | 根节点 | parentId为null，可有多个子节点 | 测试项目或测试套件 |
| Level 1 | 中间节点 | 必须有父节点，可有多个子节点 | 测试模块或测试分类 |
| Level 2 | 叶子节点 | 必须有父节点，不能有子节点 | 具体测试用例或测试数据 |

### 层级操作能力

#### 1. 节点创建
```java
/**
 * 创建子节点
 * 
 * @param parentId 父节点ID
 * @param sample 待创建的样本对象
 * @return 创建结果
 * @throws BusinessException 当层级深度超限时抛出异常
 */
public TestSample createChild(String parentId, TestSample sample) {
    // 1. 验证父节点存在性
    TestSample parent = findById(parentId);
    if (parent == null) {
        throw new BusinessException("父节点不存在");
    }
    
    // 2. 验证层级深度限制
    if (parent.getLevel() >= 2) {
        throw new BusinessException("已达到最大层级深度，无法创建子节点");
    }
    
    // 3. 设置层级关系
    sample.setParentId(parentId);
    sample.setLevel(parent.getLevel() + 1);
    
    // 4. 保存并返回
    return save(sample);
}
```

#### 2. 节点移动
```java
/**
 * 移动节点到新的父节点下
 * 
 * @param nodeId 待移动节点ID
 * @param newParentId 新父节点ID
 * @return 移动结果
 */
public boolean moveNode(String nodeId, String newParentId) {
    // 1. 获取待移动节点及其所有子节点
    TestSample node = findById(nodeId);
    List<TestSample> descendants = findAllDescendants(nodeId);
    
    // 2. 验证移动后的层级深度
    TestSample newParent = findById(newParentId);
    int maxDescendantDepth = calculateMaxDepth(descendants);
    if (newParent.getLevel() + 1 + maxDescendantDepth > 2) {
        throw new BusinessException("移动后将超过最大层级深度限制");
    }
    
    // 3. 更新层级关系
    updateNodeHierarchy(node, newParent);
    
    return true;
}
```

#### 3. 节点删除
```java
/**
 * 删除节点（级联删除所有子节点）
 * 
 * @param nodeId 节点ID
 * @return 删除结果
 */
public boolean deleteNode(String nodeId) {
    // 1. 获取所有子孙节点
    List<TestSample> descendants = findAllDescendants(nodeId);
    
    // 2. 检查Group关联关系
    checkGroupRelations(nodeId, descendants);
    
    // 3. 级联删除
    deleteNodesWithRelations(nodeId, descendants);
    
    return true;
}
```

---

## Group关系管理

### Group类型详解

#### SHARE类型分组
- **用途**：用于建立TestSample间的资源共享关系
- **特点**：
  - 分组内的样本共享配置、数据、执行环境等资源
  - 支持配置继承和覆盖机制
  - 适用于相似测试场景的样本管理
- **应用场景**：
  - 同一测试环境下的多个测试用例
  - 共享测试数据的样本集合
  - 需要统一配置管理的样本组

#### MIX类型分组  
- **用途**：用于建立TestSample间的复杂业务关系
- **特点**：
  - 支持多种关系类型（依赖、互斥、组合等）
  - 可定义复杂的执行顺序和条件
  - 支持动态关系调整
- **应用场景**：
  - 有执行依赖关系的测试序列
  - 互斥执行的测试分支
  - 需要组合执行的测试套件

### Group关系操作

#### 1. 创建分组关系
```java
/**
 * 将TestSample加入到指定Group中
 * 
 * @param sampleId 样本ID
 * @param groupId 分组ID
 * @param relationProperties 关系属性
 * @return 操作结果
 */
public boolean addSampleToGroup(String sampleId, String groupId, 
                               Map<String, Object> relationProperties) {
    // 1. 验证样本和分组存在性
    TestSample sample = findSampleById(sampleId);
    Group group = findGroupById(groupId);
    
    // 2. 根据Group类型执行不同的关联逻辑
    if (group.getType() == GroupType.SHARE) {
        return addToShareGroup(sample, group, relationProperties);
    } else if (group.getType() == GroupType.MIX) {
        return addToMixGroup(sample, group, relationProperties);
    }
    
    return false;
}
```

#### 2. 查询关联关系
```java
/**
 * 获取TestSample的所有Group关系
 * 
 * @param sampleId 样本ID
 * @return Group关系列表
 */
public List<GroupRelation> getSampleGroupRelations(String sampleId) {
    return groupRelationRepository.findBySampleId(sampleId);
}

/**
 * 获取指定类型的关联样本
 * 
 * @param sampleId 样本ID
 * @param groupType 分组类型
 * @return 关联样本列表
 */
public List<TestSample> getRelatedSamples(String sampleId, GroupType groupType) {
    List<Group> groups = findGroupsBySampleAndType(sampleId, groupType);
    return groups.stream()
                 .flatMap(group -> group.getSamples().stream())
                 .filter(sample -> !sample.getId().equals(sampleId))
                 .distinct()
                 .collect(Collectors.toList());
}
```

---

## 核心业务能力

### 1. 层级遍历能力
```java
/**
 * 深度优先遍历
 */
public void traverseDepthFirst(String rootId, Consumer<TestSample> visitor) {
    TestSample root = findById(rootId);
    if (root != null) {
        visitor.accept(root);
        root.getChildren().forEach(child -> 
            traverseDepthFirst(child.getId(), visitor));
    }
}

/**
 * 广度优先遍历  
 */
public void traverseBreadthFirst(String rootId, Consumer<TestSample> visitor) {
    Queue<TestSample> queue = new LinkedList<>();
    TestSample root = findById(rootId);
    if (root != null) {
        queue.offer(root);
        while (!queue.isEmpty()) {
            TestSample current = queue.poll();
            visitor.accept(current);
            queue.addAll(current.getChildren());
        }
    }
}
```

### 2. 路径查找能力
```java
/**
 * 查找从根节点到指定节点的路径
 */
public List<TestSample> findPathToRoot(String nodeId) {
    List<TestSample> path = new ArrayList<>();
    TestSample current = findById(nodeId);
    
    while (current != null) {
        path.add(0, current);
        current = current.getParentId() != null ? 
                  findById(current.getParentId()) : null;
    }
    
    return path;
}
```

### 3. 批量操作能力
```java
/**
 * 批量更新节点状态
 */
public int batchUpdateStatus(List<String> nodeIds, SampleStatus status) {
    return nodeIds.stream()
                  .mapToInt(id -> updateNodeStatus(id, status) ? 1 : 0)
                  .sum();
}
```

---

## 关系图示

### TestSample层级关系图
```mermaid
graph TD
    A[Root Sample A] --> B[Branch Sample B]
    A --> C[Branch Sample C]
    A --> D[Branch Sample D]
    
    B --> E[Leaf Sample E]
    B --> F[Leaf Sample F]
    B --> G[Leaf Sample G]
    
    C --> H[Leaf Sample H]
    C --> I[Leaf Sample I]
    
    D --> J[Leaf Sample J]
    
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#f3e5f5
    style D fill:#f3e5f5
    style E fill:#c8e6c9
    style F fill:#c8e6c9
    style G fill:#c8e6c9
    style H fill:#c8e6c9
    style I fill:#c8e6c9
    style J fill:#c8e6c9
```

### Group关系图
```mermaid
graph LR
    subgraph "SHARE Group"
        S1[Sample 1] -.共享配置.-> S2[Sample 2]
        S2 -.共享配置.-> S3[Sample 3]
        S3 -.共享配置.-> S1
    end
    
    subgraph "MIX Group"
        M1[Sample A] -->|依赖| M2[Sample B]
        M2 -->|依赖| M3[Sample C]
        M1 -.互斥.-> M4[Sample D]
    end
    
    style S1 fill:#e3f2fd
    style S2 fill:#e3f2fd
    style S3 fill:#e3f2fd
    style M1 fill:#fce4ec
    style M2 fill:#fce4ec
    style M3 fill:#fce4ec
    style M4 fill:#fce4ec
```

---

## 使用示例

### 创建层级结构示例
```java
// 1. 创建根节点
TestSample rootSample = TestSample.builder()
    .name("API测试套件")
    .code("API_TEST_SUITE_001")
    .type(SampleType.ROOT)
    .level(0)
    .build();
String rootId = testSampleService.create(rootSample);

// 2. 创建中间节点
TestSample branchSample = TestSample.builder()
    .name("用户管理模块")
    .code("USER_MODULE_001")
    .type(SampleType.BRANCH)
    .build();
String branchId = testSampleService.createChild(rootId, branchSample);

// 3. 创建叶子节点
TestSample leafSample = TestSample.builder()
    .name("用户登录测试")
    .code("USER_LOGIN_001")
    .type(SampleType.LEAF)
    .build();
testSampleService.createChild(branchId, leafSample);
```

### Group关系管理示例
```java
// 1. 创建SHARE类型分组
Group shareGroup = Group.builder()
    .name("环境配置共享组")
    .type(GroupType.SHARE)
    .description("共享测试环境配置的样本组")
    .build();
String shareGroupId = groupService.create(shareGroup);

// 2. 将样本加入SHARE分组
Map<String, Object> shareProperties = new HashMap<>();
shareProperties.put("configLevel", "high");
shareProperties.put("inheritMode", "override");
testSampleService.addSampleToGroup(leafSample.getId(), shareGroupId, shareProperties);

// 3. 创建MIX类型分组
Group mixGroup = Group.builder()
    .name("执行依赖组")
    .type(GroupType.MIX)
    .description("有执行顺序依赖的样本组")
    .build();
String mixGroupId = groupService.create(mixGroup);

// 4. 建立依赖关系
Map<String, Object> mixProperties = new HashMap<>();
mixProperties.put("relationType", "DEPENDENCY");
mixProperties.put("executionOrder", 1);
testSampleService.addSampleToGroup(leafSample.getId(), mixGroupId, mixProperties);
```

---

## 最佳实践

### 层级设计原则
1. **单一职责**：每个层级应有明确的业务含义
2. **深度控制**：严格控制在3层以内，避免过深的层级结构
3. **命名规范**：采用统一的命名规范，便于理解和维护
4. **状态管理**：合理设计状态流转，确保数据一致性

### Group使用建议
1. **类型选择**：
   - 资源共享场景优先使用SHARE类型
   - 复杂业务关系场景使用MIX类型
2. **关系维护**：
   - 定期清理无效的Group关系
   - 避免创建过于复杂的关系网络
3. **性能优化**：
   - 合理使用延迟加载
   - 对频繁查询的关系建立索引

### 【风险】注意事项
1. **循环依赖**：在MIX类型分组中要避免创建循环依赖关系
2. **层级深度**：严格控制层级深度，超过限制会影响系统性能
3. **并发安全**：在多线程环境下操作层级关系时要注意并发安全
4. **数据一致性**：删除节点时要确保相关Group关系的一致性

---

## 性能优化策略

### 查询优化
1. **索引设计**：
   - 在parentId字段上建立索引，优化层级查询
   - 在level字段上建立索引，支持按层级快速过滤
   - 在Group关联表上建立复合索引

2. **缓存策略**：
   - 对频繁访问的根节点进行缓存
   - 缓存Group关系映射，减少数据库查询
   - 使用分布式缓存存储完整的树形结构

3. **分页加载**：
   - 对大量子节点采用分页加载策略
   - 实现懒加载机制，按需加载子树

### 数据库设计优化
```sql
-- TestSample表结构
CREATE TABLE test_sample (
    id VARCHAR(32) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(50) UNIQUE NOT NULL,
    type VARCHAR(20) NOT NULL,
    status VARCHAR(20) NOT NULL,
    level INT NOT NULL CHECK (level >= 0 AND level <= 2),
    parent_id VARCHAR(32),
    properties JSON,
    created_by VARCHAR(50) NOT NULL,
    created_date TIMESTAMP NOT NULL,
    modified_by VARCHAR(50),
    modified_date TIMESTAMP,

    INDEX idx_parent_id (parent_id),
    INDEX idx_level (level),
    INDEX idx_code (code),
    INDEX idx_status (status),
    FOREIGN KEY (parent_id) REFERENCES test_sample(id) ON DELETE CASCADE
);

-- Group表结构
CREATE TABLE sample_group (
    id VARCHAR(32) PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    type VARCHAR(10) NOT NULL CHECK (type IN ('SHARE', 'MIX')),
    description VARCHAR(200),
    priority INT DEFAULT 50,
    is_active BOOLEAN DEFAULT TRUE,
    created_by VARCHAR(50) NOT NULL,
    created_date TIMESTAMP NOT NULL,

    INDEX idx_type (type),
    INDEX idx_active (is_active)
);

-- Group关系表
CREATE TABLE sample_group_relation (
    id VARCHAR(32) PRIMARY KEY,
    sample_id VARCHAR(32) NOT NULL,
    group_id VARCHAR(32) NOT NULL,
    relation_properties JSON,
    created_date TIMESTAMP NOT NULL,

    UNIQUE KEY uk_sample_group (sample_id, group_id),
    INDEX idx_sample_id (sample_id),
    INDEX idx_group_id (group_id),
    FOREIGN KEY (sample_id) REFERENCES test_sample(id) ON DELETE CASCADE,
    FOREIGN KEY (group_id) REFERENCES sample_group(id) ON DELETE CASCADE
);
```

---

## 监控和运维

### 关键指标监控
| 监控项 | 指标说明 | 正常范围 | 告警阈值 |
|--------|----------|----------|----------|
| 层级深度 | 最大层级深度 | ≤ 2 | > 2 |
| 节点数量 | 单个父节点下的子节点数 | < 100 | > 200 |
| Group关系数 | 单个样本的Group关联数 | < 10 | > 20 |
| 查询响应时间 | 层级查询平均响应时间 | < 100ms | > 500ms |
| 内存使用 | 树形结构缓存内存占用 | < 500MB | > 1GB |

### 数据一致性检查
```java
/**
 * 数据一致性检查服务
 */
@Service
public class DataConsistencyChecker {

    /**
     * 检查层级深度一致性
     */
    public List<String> checkLevelConsistency() {
        List<String> inconsistentNodes = new ArrayList<>();

        // 检查所有节点的level字段是否与实际层级一致
        List<TestSample> allSamples = testSampleRepository.findAll();
        for (TestSample sample : allSamples) {
            int actualLevel = calculateActualLevel(sample);
            if (sample.getLevel() != actualLevel) {
                inconsistentNodes.add(sample.getId());
            }
        }

        return inconsistentNodes;
    }

    /**
     * 检查孤儿节点
     */
    public List<String> checkOrphanNodes() {
        return testSampleRepository.findOrphanNodes();
    }

    /**
     * 检查循环依赖
     */
    public List<String> checkCircularDependency() {
        // 实现循环依赖检测算法
        return detectCircularDependency();
    }
}
```

### 运维工具
```java
/**
 * TestSample运维工具类
 */
@Component
public class TestSampleMaintenanceTool {

    /**
     * 批量修复层级深度
     */
    public int fixLevelInconsistency() {
        List<String> inconsistentNodes = dataConsistencyChecker.checkLevelConsistency();
        int fixedCount = 0;

        for (String nodeId : inconsistentNodes) {
            TestSample sample = testSampleRepository.findById(nodeId);
            int correctLevel = calculateActualLevel(sample);
            sample.setLevel(correctLevel);
            testSampleRepository.save(sample);
            fixedCount++;
        }

        return fixedCount;
    }

    /**
     * 清理无效Group关系
     */
    public int cleanupInvalidGroupRelations() {
        return groupRelationRepository.deleteInvalidRelations();
    }

    /**
     * 生成层级结构报告
     */
    public HierarchyReport generateHierarchyReport() {
        return HierarchyReport.builder()
            .totalNodes(testSampleRepository.count())
            .rootNodes(testSampleRepository.countByLevel(0))
            .branchNodes(testSampleRepository.countByLevel(1))
            .leafNodes(testSampleRepository.countByLevel(2))
            .totalGroups(groupRepository.count())
            .shareGroups(groupRepository.countByType(GroupType.SHARE))
            .mixGroups(groupRepository.countByType(GroupType.MIX))
            .build();
    }
}
```

---

## 扩展性设计

### 插件化架构
```java
/**
 * TestSample操作插件接口
 */
public interface TestSamplePlugin {

    /**
     * 插件名称
     */
    String getName();

    /**
     * 支持的操作类型
     */
    Set<OperationType> getSupportedOperations();

    /**
     * 执行前置处理
     */
    void beforeOperation(TestSample sample, OperationType operation);

    /**
     * 执行后置处理
     */
    void afterOperation(TestSample sample, OperationType operation);
}

/**
 * 插件管理器
 */
@Service
public class PluginManager {

    private final List<TestSamplePlugin> plugins = new ArrayList<>();

    public void registerPlugin(TestSamplePlugin plugin) {
        plugins.add(plugin);
    }

    public void executePlugins(TestSample sample, OperationType operation, PluginPhase phase) {
        plugins.stream()
               .filter(plugin -> plugin.getSupportedOperations().contains(operation))
               .forEach(plugin -> {
                   if (phase == PluginPhase.BEFORE) {
                       plugin.beforeOperation(sample, operation);
                   } else {
                       plugin.afterOperation(sample, operation);
                   }
               });
    }
}
```

### 自定义扩展点
1. **自定义属性验证器**：支持业务特定的属性验证逻辑
2. **自定义关系处理器**：支持特殊的Group关系处理逻辑
3. **自定义序列化器**：支持不同格式的数据导入导出
4. **自定义缓存策略**：支持业务特定的缓存优化策略

---

## 版本变更记录

| 版本号 | 变更日期 | 变更内容 | 影响范围 | 变更人 |
|--------|----------|----------|----------|--------|
| v1.0 | 2025-09-29 | 初始版本，完整的层级管理和Group关系设计 | 全部功能 | 系统架构师 |

---

## 附录

### A. 常见问题解答

**Q1: 为什么限制层级深度为3层？**
A1: 基于性能和复杂度考虑，3层结构能满足大部分业务场景，同时保持良好的查询性能和维护性。

**Q2: SHARE和MIX类型的Group有什么本质区别？**
A2: SHARE类型主要用于资源共享，关系相对简单；MIX类型用于复杂业务关系，支持依赖、互斥等多种关系类型。

**Q3: 如何处理大量节点的性能问题？**
A3: 采用分页加载、缓存策略、索引优化等多种手段，具体可参考性能优化策略章节。

### B. 相关技术文档
- [TestSample API接口文档](./TestSample-API文档.md)
- [Group管理服务文档](./Group管理服务文档.md)
- [数据库设计文档](./数据库设计文档.md)

---

*文档版本：v1.0*
*最后更新：2025年9月29日*
*维护团队：测试管理开发组*
