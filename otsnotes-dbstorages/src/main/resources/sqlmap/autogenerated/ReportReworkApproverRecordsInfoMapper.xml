<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.otsnotes.dbstorages.mybatis.mapper.ReportReworkApproverRecordsInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.otsnotes.dbstorages.mybatis.model.ReportReworkApproverRecordsInfoPO" >
    <id column="RecordId" property="recordId" jdbcType="BIGINT" />
    <result column="ApplyId" property="applyId" jdbcType="BIGINT" />
    <result column="OrderNo" property="orderNo" jdbcType="VARCHAR" />
    <result column="LabCode" property="labCode" jdbcType="VARCHAR" />
    <result column="ReportID" property="reportID" jdbcType="VARCHAR" />
    <result column="ReasonType" property="reasonType" jdbcType="INTEGER" />
    <result column="Reason" property="reason" jdbcType="VARCHAR" />
    <result column="Causedby" property="causedby" jdbcType="VARCHAR" />
    <result column="ApplyStatus" property="applyStatus" jdbcType="INTEGER" />
    <result column="ApplyTime" property="applyTime" jdbcType="TIMESTAMP" />
    <result column="ApplyBy" property="applyBy" jdbcType="VARCHAR" />
    <result column="ApproverTime" property="approverTime" jdbcType="TIMESTAMP" />
    <result column="ApproverBy" property="approverBy" jdbcType="VARCHAR" />
    <result column="LastModifiedTimestamp" property="lastModifiedTimestamp" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    RecordId, ApplyId, OrderNo, LabCode, ReportID, ReasonType, Reason, Causedby, ApplyStatus, 
    ApplyTime, ApplyBy, ApproverTime, ApproverBy, LastModifiedTimestamp
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.ReportReworkApproverRecordsInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_report_rework_approver_records
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from tb_report_rework_approver_records
    where RecordId = #{recordId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from tb_report_rework_approver_records
    where RecordId = #{recordId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.ReportReworkApproverRecordsInfoExample" >
    delete from tb_report_rework_approver_records
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.ReportReworkApproverRecordsInfoPO" >
    insert into tb_report_rework_approver_records (RecordId, ApplyId, OrderNo, 
      LabCode, ReportID, ReasonType, 
      Reason, Causedby, ApplyStatus, 
      ApplyTime, ApplyBy, ApproverTime, 
      ApproverBy, LastModifiedTimestamp)
    values (#{recordId,jdbcType=BIGINT}, #{applyId,jdbcType=BIGINT}, #{orderNo,jdbcType=VARCHAR}, 
      #{labCode,jdbcType=VARCHAR}, #{reportID,jdbcType=VARCHAR}, #{reasonType,jdbcType=INTEGER}, 
      #{reason,jdbcType=VARCHAR}, #{causedby,jdbcType=VARCHAR}, #{applyStatus,jdbcType=INTEGER}, 
      #{applyTime,jdbcType=TIMESTAMP}, #{applyBy,jdbcType=VARCHAR}, #{approverTime,jdbcType=TIMESTAMP}, 
      #{approverBy,jdbcType=VARCHAR}, #{lastModifiedTimestamp,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.ReportReworkApproverRecordsInfoPO" >
    insert into tb_report_rework_approver_records
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="recordId != null" >
        RecordId,
      </if>
      <if test="applyId != null" >
        ApplyId,
      </if>
      <if test="orderNo != null" >
        OrderNo,
      </if>
      <if test="labCode != null" >
        LabCode,
      </if>
      <if test="reportID != null" >
        ReportID,
      </if>
      <if test="reasonType != null" >
        ReasonType,
      </if>
      <if test="reason != null" >
        Reason,
      </if>
      <if test="causedby != null" >
        Causedby,
      </if>
      <if test="applyStatus != null" >
        ApplyStatus,
      </if>
      <if test="applyTime != null" >
        ApplyTime,
      </if>
      <if test="applyBy != null" >
        ApplyBy,
      </if>
      <if test="approverTime != null" >
        ApproverTime,
      </if>
      <if test="approverBy != null" >
        ApproverBy,
      </if>
      <if test="lastModifiedTimestamp != null" >
        LastModifiedTimestamp,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="recordId != null" >
        #{recordId,jdbcType=BIGINT},
      </if>
      <if test="applyId != null" >
        #{applyId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null" >
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="labCode != null" >
        #{labCode,jdbcType=VARCHAR},
      </if>
      <if test="reportID != null" >
        #{reportID,jdbcType=VARCHAR},
      </if>
      <if test="reasonType != null" >
        #{reasonType,jdbcType=INTEGER},
      </if>
      <if test="reason != null" >
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="causedby != null" >
        #{causedby,jdbcType=VARCHAR},
      </if>
      <if test="applyStatus != null" >
        #{applyStatus,jdbcType=INTEGER},
      </if>
      <if test="applyTime != null" >
        #{applyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="applyBy != null" >
        #{applyBy,jdbcType=VARCHAR},
      </if>
      <if test="approverTime != null" >
        #{approverTime,jdbcType=TIMESTAMP},
      </if>
      <if test="approverBy != null" >
        #{approverBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTimestamp != null" >
        #{lastModifiedTimestamp,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.ReportReworkApproverRecordsInfoExample" resultType="java.lang.Integer" >
    select count(*) from tb_report_rework_approver_records
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_report_rework_approver_records
    <set >
      <if test="record.recordId != null" >
        RecordId = #{record.recordId,jdbcType=BIGINT},
      </if>
      <if test="record.applyId != null" >
        ApplyId = #{record.applyId,jdbcType=BIGINT},
      </if>
      <if test="record.orderNo != null" >
        OrderNo = #{record.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.labCode != null" >
        LabCode = #{record.labCode,jdbcType=VARCHAR},
      </if>
      <if test="record.reportID != null" >
        ReportID = #{record.reportID,jdbcType=VARCHAR},
      </if>
      <if test="record.reasonType != null" >
        ReasonType = #{record.reasonType,jdbcType=INTEGER},
      </if>
      <if test="record.reason != null" >
        Reason = #{record.reason,jdbcType=VARCHAR},
      </if>
      <if test="record.causedby != null" >
        Causedby = #{record.causedby,jdbcType=VARCHAR},
      </if>
      <if test="record.applyStatus != null" >
        ApplyStatus = #{record.applyStatus,jdbcType=INTEGER},
      </if>
      <if test="record.applyTime != null" >
        ApplyTime = #{record.applyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.applyBy != null" >
        ApplyBy = #{record.applyBy,jdbcType=VARCHAR},
      </if>
      <if test="record.approverTime != null" >
        ApproverTime = #{record.approverTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.approverBy != null" >
        ApproverBy = #{record.approverBy,jdbcType=VARCHAR},
      </if>
      <if test="record.lastModifiedTimestamp != null" >
        LastModifiedTimestamp = #{record.lastModifiedTimestamp,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_report_rework_approver_records
    set RecordId = #{record.recordId,jdbcType=BIGINT},
      ApplyId = #{record.applyId,jdbcType=BIGINT},
      OrderNo = #{record.orderNo,jdbcType=VARCHAR},
      LabCode = #{record.labCode,jdbcType=VARCHAR},
      ReportID = #{record.reportID,jdbcType=VARCHAR},
      ReasonType = #{record.reasonType,jdbcType=INTEGER},
      Reason = #{record.reason,jdbcType=VARCHAR},
      Causedby = #{record.causedby,jdbcType=VARCHAR},
      ApplyStatus = #{record.applyStatus,jdbcType=INTEGER},
      ApplyTime = #{record.applyTime,jdbcType=TIMESTAMP},
      ApplyBy = #{record.applyBy,jdbcType=VARCHAR},
      ApproverTime = #{record.approverTime,jdbcType=TIMESTAMP},
      ApproverBy = #{record.approverBy,jdbcType=VARCHAR},
      LastModifiedTimestamp = #{record.lastModifiedTimestamp,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.ReportReworkApproverRecordsInfoPO" >
    update tb_report_rework_approver_records
    <set >
      <if test="applyId != null" >
        ApplyId = #{applyId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null" >
        OrderNo = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="labCode != null" >
        LabCode = #{labCode,jdbcType=VARCHAR},
      </if>
      <if test="reportID != null" >
        ReportID = #{reportID,jdbcType=VARCHAR},
      </if>
      <if test="reasonType != null" >
        ReasonType = #{reasonType,jdbcType=INTEGER},
      </if>
      <if test="reason != null" >
        Reason = #{reason,jdbcType=VARCHAR},
      </if>
      <if test="causedby != null" >
        Causedby = #{causedby,jdbcType=VARCHAR},
      </if>
      <if test="applyStatus != null" >
        ApplyStatus = #{applyStatus,jdbcType=INTEGER},
      </if>
      <if test="applyTime != null" >
        ApplyTime = #{applyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="applyBy != null" >
        ApplyBy = #{applyBy,jdbcType=VARCHAR},
      </if>
      <if test="approverTime != null" >
        ApproverTime = #{approverTime,jdbcType=TIMESTAMP},
      </if>
      <if test="approverBy != null" >
        ApproverBy = #{approverBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTimestamp != null" >
        LastModifiedTimestamp = #{lastModifiedTimestamp,jdbcType=TIMESTAMP},
      </if>
    </set>
    where RecordId = #{recordId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.ReportReworkApproverRecordsInfoPO" >
    update tb_report_rework_approver_records
    set ApplyId = #{applyId,jdbcType=BIGINT},
      OrderNo = #{orderNo,jdbcType=VARCHAR},
      LabCode = #{labCode,jdbcType=VARCHAR},
      ReportID = #{reportID,jdbcType=VARCHAR},
      ReasonType = #{reasonType,jdbcType=INTEGER},
      Reason = #{reason,jdbcType=VARCHAR},
      Causedby = #{causedby,jdbcType=VARCHAR},
      ApplyStatus = #{applyStatus,jdbcType=INTEGER},
      ApplyTime = #{applyTime,jdbcType=TIMESTAMP},
      ApplyBy = #{applyBy,jdbcType=VARCHAR},
      ApproverTime = #{approverTime,jdbcType=TIMESTAMP},
      ApproverBy = #{approverBy,jdbcType=VARCHAR},
      LastModifiedTimestamp = #{lastModifiedTimestamp,jdbcType=TIMESTAMP}
    where RecordId = #{recordId,jdbcType=BIGINT}
  </update>
</mapper>